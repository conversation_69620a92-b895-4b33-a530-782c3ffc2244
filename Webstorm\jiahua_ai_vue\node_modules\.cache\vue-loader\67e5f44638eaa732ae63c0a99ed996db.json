{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\InventoryManagementList.vue?vue&type=template&id=7ca16c2e&scoped=true&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\InventoryManagementList.vue", "mtime": 1753843740756}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753423171139}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"inventory-management-container\" },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"stats-cards\" },\n        [\n          _c(\n            \"a-row\",\n            { attrs: { gutter: [16, 16] } },\n            [\n              _c(\"a-col\", { attrs: { xs: 24, sm: 12, md: 8, lg: 6 } }, [\n                _c(\"div\", { staticClass: \"stat-card total-items\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"stat-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: \"database\" } })],\n                    1\n                  ),\n                  _c(\"div\", { staticClass: \"stat-info\" }, [\n                    _c(\"div\", { staticClass: \"stat-title\" }, [\n                      _vm._v(\"总物品数\")\n                    ]),\n                    _c(\"div\", { staticClass: \"stat-value\" }, [\n                      _vm._v(_vm._s(_vm.totalItems))\n                    ]),\n                    _c(\n                      \"div\",\n                      { staticClass: \"stat-trend\" },\n                      [\n                        _c(\"a-icon\", { attrs: { type: \"arrow-up\" } }),\n                        _c(\"span\", [_vm._v(_vm._s(_vm.trendData.items) + \"%\")])\n                      ],\n                      1\n                    )\n                  ])\n                ])\n              ]),\n              _c(\"a-col\", { attrs: { xs: 24, sm: 12, md: 8, lg: 6 } }, [\n                _c(\"div\", { staticClass: \"stat-card total-inventory\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"stat-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: \"inbox\" } })],\n                    1\n                  ),\n                  _c(\"div\", { staticClass: \"stat-info\" }, [\n                    _c(\"div\", { staticClass: \"stat-title\" }, [\n                      _vm._v(\"库存总量\")\n                    ]),\n                    _c(\"div\", { staticClass: \"stat-value\" }, [\n                      _vm._v(_vm._s(_vm.totalInventory))\n                    ]),\n                    _c(\n                      \"div\",\n                      { staticClass: \"stat-trend\" },\n                      [\n                        _c(\"a-icon\", { attrs: { type: \"arrow-up\" } }),\n                        _c(\"span\", [\n                          _vm._v(_vm._s(_vm.trendData.inventory) + \"%\")\n                        ])\n                      ],\n                      1\n                    )\n                  ])\n                ])\n              ]),\n              _c(\"a-col\", { attrs: { xs: 24, sm: 12, md: 8, lg: 6 } }, [\n                _c(\"div\", { staticClass: \"stat-card available-inventory\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"stat-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: \"check-circle\" } })],\n                    1\n                  ),\n                  _c(\"div\", { staticClass: \"stat-info\" }, [\n                    _c(\"div\", { staticClass: \"stat-title\" }, [\n                      _vm._v(\"可用库存\")\n                    ]),\n                    _c(\"div\", { staticClass: \"stat-value\" }, [\n                      _vm._v(_vm._s(_vm.availableInventory))\n                    ]),\n                    _c(\n                      \"div\",\n                      { staticClass: \"stat-trend\" },\n                      [\n                        _c(\"a-icon\", { attrs: { type: \"arrow-down\" } }),\n                        _c(\"span\", [\n                          _vm._v(_vm._s(_vm.trendData.available) + \"%\")\n                        ])\n                      ],\n                      1\n                    )\n                  ])\n                ])\n              ]),\n              _c(\"a-col\", { attrs: { xs: 24, sm: 12, md: 8, lg: 6 } }, [\n                _c(\"div\", { staticClass: \"stat-card total-cost\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"stat-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: \"dollar\" } })],\n                    1\n                  ),\n                  _c(\"div\", { staticClass: \"stat-info\" }, [\n                    _c(\"div\", { staticClass: \"stat-title\" }, [\n                      _vm._v(\"总成本\")\n                    ]),\n                    _c(\"div\", { staticClass: \"stat-value\" }, [\n                      _vm._v(\"¥\" + _vm._s(_vm.formatNumber(_vm.totalCost)))\n                    ]),\n                    _c(\n                      \"div\",\n                      { staticClass: \"stat-trend\" },\n                      [\n                        _c(\"a-icon\", { attrs: { type: \"arrow-up\" } }),\n                        _c(\"span\", [_vm._v(_vm._s(_vm.trendData.cost) + \"%\")])\n                      ],\n                      1\n                    )\n                  ])\n                ])\n              ])\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\"div\", { staticClass: \"search-filter-bar\" }, [\n        _c(\n          \"div\",\n          { staticClass: \"search-area\" },\n          [\n            _c(\n              \"a-input-search\",\n              {\n                staticClass: \"search-input\",\n                attrs: {\n                  placeholder: \"搜索产品名称或物品名称\",\n                  \"allow-clear\": \"\"\n                },\n                on: { search: _vm.onSearch }\n              },\n              [\n                _c(\"a-icon\", {\n                  attrs: { slot: \"prefix\", type: \"search\" },\n                  slot: \"prefix\"\n                })\n              ],\n              1\n            ),\n            _c(\n              \"a-button-group\",\n              { staticClass: \"type-filter\" },\n              [\n                _c(\n                  \"a-tooltip\",\n                  { attrs: { title: \"物品\" } },\n                  [\n                    _c(\n                      \"a-button\",\n                      {\n                        attrs: {\n                          type:\n                            _vm.inventoryTypeFilter === \"material\"\n                              ? \"primary\"\n                              : \"default\"\n                        },\n                        on: {\n                          click: function() {\n                            _vm.inventoryTypeFilter = \"material\"\n                            _vm.loadData(1)\n                          }\n                        }\n                      },\n                      [_vm._v(\"\\n              物料\\n            \")]\n                    )\n                  ],\n                  1\n                ),\n                _c(\n                  \"a-tooltip\",\n                  { attrs: { title: \"产品\" } },\n                  [\n                    _c(\n                      \"a-button\",\n                      {\n                        attrs: {\n                          type:\n                            _vm.inventoryTypeFilter === \"product\"\n                              ? \"primary\"\n                              : \"default\"\n                        },\n                        on: {\n                          click: function() {\n                            _vm.inventoryTypeFilter = \"product\"\n                            _vm.loadData(1)\n                          }\n                        }\n                      },\n                      [_vm._v(\"\\n              产品\\n            \")]\n                    )\n                  ],\n                  1\n                )\n              ],\n              1\n            )\n          ],\n          1\n        ),\n        _c(\n          \"div\",\n          { staticClass: \"filter-actions\" },\n          [\n            _c(\n              \"a-button-group\",\n              { staticClass: \"view-toggle\" },\n              [\n                _c(\n                  \"a-tooltip\",\n                  { attrs: { title: \"数据表格视图\" } },\n                  [\n                    _c(\n                      \"a-button\",\n                      {\n                        attrs: {\n                          type: _vm.viewMode === \"table\" ? \"primary\" : \"default\"\n                        },\n                        on: {\n                          click: function($event) {\n                            _vm.viewMode = \"table\"\n                          }\n                        }\n                      },\n                      [_c(\"a-icon\", { attrs: { type: \"table\" } })],\n                      1\n                    )\n                  ],\n                  1\n                ),\n                _c(\n                  \"a-tooltip\",\n                  { attrs: { title: \"卡片视图\" } },\n                  [\n                    _c(\n                      \"a-button\",\n                      {\n                        attrs: {\n                          type: _vm.viewMode === \"card\" ? \"primary\" : \"default\"\n                        },\n                        on: {\n                          click: function($event) {\n                            _vm.viewMode = \"card\"\n                          }\n                        }\n                      },\n                      [_c(\"a-icon\", { attrs: { type: \"appstore\" } })],\n                      1\n                    )\n                  ],\n                  1\n                )\n              ],\n              1\n            )\n          ],\n          1\n        )\n      ]),\n      _vm.selectedRowKeys.length > 0\n        ? _c(\n            \"div\",\n            { staticClass: \"selection-info\" },\n            [\n              _c(\n                \"a-alert\",\n                {\n                  attrs: {\n                    message: \"已选择 \" + _vm.selectedRowKeys.length + \" 项\",\n                    type: \"info\",\n                    \"show-icon\": \"\",\n                    closable: \"\"\n                  },\n                  on: {\n                    close: function($event) {\n                      _vm.selectedRowKeys = []\n                    }\n                  }\n                },\n                [\n                  _c(\n                    \"template\",\n                    { slot: \"action\" },\n                    [\n                      _c(\n                        \"a-button\",\n                        {\n                          staticStyle: { \"margin-right\": \"8px\" },\n                          attrs: { size: \"small\", type: \"primary\" },\n                          on: { click: _vm.handleBatchAdjust }\n                        },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"tool\" } }),\n                          _vm._v(\" 批量调整\\n          \")\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"a-button\",\n                        {\n                          attrs: { size: \"small\", type: \"danger\" },\n                          on: { click: _vm.batchDelete }\n                        },\n                        [_vm._v(\"\\n            批量删除\\n          \")]\n                      )\n                    ],\n                    1\n                  )\n                ],\n                2\n              )\n            ],\n            1\n          )\n        : _vm._e(),\n      _vm.viewMode === \"card\"\n        ? _c(\n            \"div\",\n            { staticClass: \"inventory-card-view\" },\n            [\n              _c(\n                \"a-spin\",\n                { attrs: { spinning: _vm.loading } },\n                [\n                  _vm.dataSource.length === 0\n                    ? _c(\"a-empty\", { attrs: { description: \"暂无库存数据\" } })\n                    : _c(\n                        \"a-row\",\n                        { attrs: { gutter: [16, 16] } },\n                        _vm._l(_vm.dataSource, function(item) {\n                          return _c(\n                            \"a-col\",\n                            {\n                              key: item.id,\n                              attrs: { xs: 24, sm: 12, md: 12, lg: 8, xl: 6 }\n                            },\n                            [\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"inventory-card\",\n                                  class: {\n                                    \"selected-card\": _vm.selectedRowKeys.includes(\n                                      item.id\n                                    )\n                                  },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.toggleSelection(item.id)\n                                    }\n                                  }\n                                },\n                                [\n                                  _c(\n                                    \"div\",\n                                    { staticClass: \"card-header\" },\n                                    [\n                                      _c(\n                                        \"div\",\n                                        {\n                                          staticClass: \"item-name\",\n                                          attrs: { title: item.imItemName }\n                                        },\n                                        [_vm._v(_vm._s(item.imItemName))]\n                                      ),\n                                      _c(\"a-checkbox\", {\n                                        attrs: {\n                                          checked: _vm.selectedRowKeys.includes(\n                                            item.id\n                                          )\n                                        },\n                                        on: {\n                                          change: function(e) {\n                                            return _vm.onItemSelect(\n                                              item.id,\n                                              e.target.checked\n                                            )\n                                          },\n                                          click: function($event) {\n                                            $event.stopPropagation()\n                                          }\n                                        }\n                                      })\n                                    ],\n                                    1\n                                  ),\n                                  _c(\"div\", { staticClass: \"card-product\" }, [\n                                    _c(\"span\", [\n                                      _vm._v(\n                                        \"产品: \" + _vm._s(item.imProductName)\n                                      )\n                                    ]),\n                                    _c(\"span\", { staticClass: \"item-id\" }, [\n                                      _vm._v(\"ID: \" + _vm._s(item.imItemId))\n                                    ])\n                                  ]),\n                                  _c(\n                                    \"div\",\n                                    { staticClass: \"inventory-metrics\" },\n                                    [\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"metric-item\" },\n                                        [\n                                          _c(\n                                            \"div\",\n                                            { staticClass: \"metric-value\" },\n                                            [\n                                              _vm._v(\n                                                _vm._s(item.imInventoryQuantity)\n                                              )\n                                            ]\n                                          ),\n                                          _c(\n                                            \"div\",\n                                            { staticClass: \"metric-label\" },\n                                            [_vm._v(\"库存数量\")]\n                                          ),\n                                          _c(\"a-progress\", {\n                                            attrs: {\n                                              percent: 100,\n                                              strokeColor: \"#52c41a\",\n                                              size: \"small\",\n                                              showInfo: false\n                                            }\n                                          })\n                                        ],\n                                        1\n                                      ),\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"metric-item\" },\n                                        [\n                                          _c(\n                                            \"div\",\n                                            { staticClass: \"metric-value\" },\n                                            [\n                                              _vm._v(\n                                                _vm._s(item.imAvailableStock)\n                                              )\n                                            ]\n                                          ),\n                                          _c(\n                                            \"div\",\n                                            { staticClass: \"metric-label\" },\n                                            [_vm._v(\"可用库存\")]\n                                          ),\n                                          _c(\"a-progress\", {\n                                            attrs: {\n                                              percent: _vm.calculateAvailablePercent(\n                                                item.imInventoryQuantity,\n                                                item.imAvailableStock\n                                              ),\n                                              strokeColor: _vm.getStockColor(\n                                                item.imInventoryQuantity,\n                                                item.imAvailableStock\n                                              ),\n                                              size: \"small\",\n                                              showInfo: false\n                                            }\n                                          })\n                                        ],\n                                        1\n                                      )\n                                    ]\n                                  ),\n                                  _c(\"div\", { staticClass: \"price-info\" }, [\n                                    _c(\"div\", { staticClass: \"price-item\" }, [\n                                      _c(\n                                        \"span\",\n                                        { staticClass: \"price-label\" },\n                                        [_vm._v(\"单价:\")]\n                                      ),\n                                      _c(\n                                        \"span\",\n                                        { staticClass: \"price-value\" },\n                                        [\n                                          _vm._v(\n                                            \"¥\" +\n                                              _vm._s(\n                                                _vm.formatNumber(\n                                                  item.imItemPrice\n                                                )\n                                              )\n                                          )\n                                        ]\n                                      ),\n                                      _c(\n                                        \"span\",\n                                        { staticClass: \"unit-label\" },\n                                        [_vm._v(\"/ \" + _vm._s(item.imItemUnit))]\n                                      )\n                                    ]),\n                                    _c(\"div\", { staticClass: \"price-item\" }, [\n                                      _c(\n                                        \"span\",\n                                        { staticClass: \"price-label\" },\n                                        [_vm._v(\"总成本:\")]\n                                      ),\n                                      _c(\n                                        \"span\",\n                                        { staticClass: \"price-value\" },\n                                        [\n                                          _vm._v(\n                                            \"¥\" +\n                                              _vm._s(\n                                                _vm.formatNumber(\n                                                  item.imTotalCost\n                                                )\n                                              )\n                                          )\n                                        ]\n                                      )\n                                    ])\n                                  ]),\n                                  _c(\n                                    \"div\",\n                                    { staticClass: \"card-actions\" },\n                                    [\n                                      _c(\n                                        \"a-button\",\n                                        {\n                                          attrs: { type: \"link\" },\n                                          on: {\n                                            click: function($event) {\n                                              $event.stopPropagation()\n                                              return _vm.handleDetail(item)\n                                            }\n                                          }\n                                        },\n                                        [\n                                          _c(\"a-icon\", {\n                                            attrs: { type: \"eye\" }\n                                          }),\n                                          _vm._v(\" 详情\\n                \")\n                                        ],\n                                        1\n                                      )\n                                    ],\n                                    1\n                                  )\n                                ]\n                              )\n                            ]\n                          )\n                        }),\n                        1\n                      ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-pagination\" },\n                    [\n                      _c(\"a-pagination\", {\n                        attrs: {\n                          current: _vm.ipagination.current,\n                          pageSize: _vm.ipagination.pageSize,\n                          total: _vm.ipagination.total,\n                          pageSizeOptions: _vm.ipagination.pageSizeOptions,\n                          showTotal: function(total, range) {\n                            return (\n                              range[0] + \"-\" + range[1] + \" 共 \" + total + \" 条\"\n                            )\n                          },\n                          showSizeChanger: \"\",\n                          showQuickJumper: \"\"\n                        },\n                        on: {\n                          change: _vm.handleCardPageChange,\n                          showSizeChange: _vm.handleCardShowSizeChange\n                        }\n                      })\n                    ],\n                    1\n                  )\n                ],\n                1\n              )\n            ],\n            1\n          )\n        : _vm.viewMode === \"table\"\n        ? _c(\n            \"div\",\n            { staticClass: \"inventory-table-view\" },\n            [\n              _c(\n                \"a-spin\",\n                { attrs: { spinning: _vm.loading } },\n                [\n                  _c(\"a-table\", {\n                    ref: \"table\",\n                    staticClass: \"custom-inventory-table\",\n                    attrs: {\n                      size: \"middle\",\n                      scroll: { x: true },\n                      rowKey: function(record) {\n                        return record.id\n                      },\n                      columns: _vm.getColumns(),\n                      dataSource: _vm.dataSource,\n                      pagination: _vm.ipagination,\n                      rowSelection: _vm.rowSelection\n                    },\n                    on: { change: _vm.handleTableChange },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"imInventoryQuantitySlot\",\n                        fn: function(text, record) {\n                          return [\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"inventory-cell clickable-cell\",\n                                on: {\n                                  click: function($event) {\n                                    return _vm.handleInventoryClick(record)\n                                  }\n                                }\n                              },\n                              [\n                                _c(\n                                  \"span\",\n                                  { staticClass: \"inventory-number\" },\n                                  [_vm._v(_vm._s(text))]\n                                ),\n                                _c(\"a-progress\", {\n                                  attrs: {\n                                    percent: _vm.calculateStockPercent(\n                                      record.imInventoryQuantity,\n                                      record.imAvailableStock\n                                    ),\n                                    size: \"small\",\n                                    strokeColor: \"#52c41a\",\n                                    showInfo: false\n                                  }\n                                })\n                              ],\n                              1\n                            )\n                          ]\n                        }\n                      },\n                      {\n                        key: \"imAvailableStockSlot\",\n                        fn: function(text, record) {\n                          return [\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"inventory-cell clickable-cell\",\n                                on: {\n                                  click: function($event) {\n                                    return _vm.handleAvailableStockClick(record)\n                                  }\n                                }\n                              },\n                              [\n                                _c(\n                                  \"span\",\n                                  { staticClass: \"inventory-number\" },\n                                  [_vm._v(_vm._s(text))]\n                                ),\n                                _c(\"a-progress\", {\n                                  attrs: {\n                                    percent: _vm.calculateAvailablePercent(\n                                      record.imInventoryQuantity,\n                                      record.imAvailableStock\n                                    ),\n                                    strokeColor: _vm.getStockColor(\n                                      record.imInventoryQuantity,\n                                      record.imAvailableStock\n                                    ),\n                                    size: \"small\",\n                                    showInfo: false\n                                  }\n                                })\n                              ],\n                              1\n                            )\n                          ]\n                        }\n                      },\n                      {\n                        key: \"priceSlot\",\n                        fn: function(text) {\n                          return [\n                            _c(\"span\", { staticClass: \"price-text\" }, [\n                              _vm._v(\"¥\" + _vm._s(_vm.formatNumber(text)))\n                            ])\n                          ]\n                        }\n                      },\n                      {\n                        key: \"action\",\n                        fn: function(text, record) {\n                          return [\n                            _c(\n                              \"div\",\n                              { staticClass: \"table-actions\" },\n                              [\n                                _c(\n                                  \"a-button\",\n                                  {\n                                    attrs: { type: \"link\" },\n                                    on: {\n                                      click: function($event) {\n                                        return _vm.handleDetail(record)\n                                      }\n                                    }\n                                  },\n                                  [\n                                    _c(\"a-icon\", { attrs: { type: \"eye\" } }),\n                                    _vm._v(\" 详情\\n              \")\n                                  ],\n                                  1\n                                ),\n                                record.imInventoryType === \"2\"\n                                  ? _c(\n                                      \"a-button\",\n                                      {\n                                        attrs: { type: \"link\" },\n                                        on: {\n                                          click: function($event) {\n                                            return _vm.handleSoldInventory(\n                                              record\n                                            )\n                                          }\n                                        }\n                                      },\n                                      [\n                                        _c(\"a-icon\", {\n                                          attrs: { type: \"shopping-cart\" }\n                                        }),\n                                        _vm._v(\" 已售出库存\\n              \")\n                                      ],\n                                      1\n                                    )\n                                  : _vm._e()\n                              ],\n                              1\n                            )\n                          ]\n                        }\n                      }\n                    ])\n                  })\n                ],\n                1\n              )\n            ],\n            1\n          )\n        : _vm._e(),\n      _c(\"inventory-management-modal\", {\n        ref: \"modalForm\",\n        on: { ok: _vm.modalFormOk }\n      }),\n      _c(\"inventory-management-detail\", {\n        ref: \"detailDrawer\",\n        on: { close: _vm.closeDetail, edit: _vm.handleEdit }\n      }),\n      _c(\"batch-stock-adjust-modal\", {\n        ref: \"batchAdjustModal\",\n        attrs: { \"selected-items\": _vm.getSelectedItems() },\n        on: { success: _vm.handleBatchAdjustSuccess }\n      }),\n      _c(\"inventory-detail-modal\", { ref: \"inventoryDetailModal\" }),\n      _c(\"product-inventory-detail-modal\", {\n        ref: \"productInventoryDetailModal\"\n      }),\n      _c(\"sold-inventory-modal\", { ref: \"soldInventoryModal\" })\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}