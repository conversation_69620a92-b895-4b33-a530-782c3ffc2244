{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\InventoryManagementList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\InventoryManagementList.vue", "mtime": 1753843740756}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753423167852}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport '@/assets/less/TableExpand.less'\r\nimport { mixinDevice } from '@/utils/mixin'\r\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin'\r\nimport InventoryManagementModal from './modules/InventoryManagementModal'\r\nimport InventoryManagementDetail from './modules/InventoryManagementDetail'\r\nimport BatchStockAdjustModal from './modules/BatchStockAdjustModal'\r\nimport InventoryDetailModal from './modules/InventoryDetailModal'\r\nimport ProductInventoryDetailModal from './modules/ProductInventoryDetailModal'\r\nimport SoldInventoryModal from './modules/SoldInventoryModal'\r\n\r\nexport default {\r\n  name: 'InventoryManagementList',\r\n  mixins: [JeecgListMixin, mixinDevice],\r\n  components: {\r\n    InventoryManagementModal,\r\n    InventoryManagementDetail,\r\n    BatchStockAdjustModal,\r\n    InventoryDetailModal,\r\n    ProductInventoryDetailModal,\r\n    SoldInventoryModal\r\n  },\r\n  data() {\r\n    return {\r\n      allDataForStats: [],\r\n      inventoryTypeFilter: 'material',\r\n      trendData: {\r\n        items: 5.2,\r\n        inventory: 3.7,\r\n        available: -1.2,\r\n        cost: 2.4\r\n      },\r\n      description: '库存管理表管理页面',\r\n      viewMode: 'table',\r\n      columns: [\r\n        {\r\n          title: '#',\r\n          dataIndex: '',\r\n          key: 'rowIndex',\r\n          width: 60,\r\n          align: \"center\",\r\n          customRender: function(t, r, index) {\r\n            return parseInt(index) + 1;\r\n          }\r\n        },\r\n        {\r\n          title: '产品id',\r\n          align: \"center\",\r\n          dataIndex: 'imProductId'\r\n        },\r\n        {\r\n          title: '产品名',\r\n          align: \"center\",\r\n          dataIndex: 'imProductName'\r\n        },\r\n        {\r\n          title: '物品id',\r\n          align: \"center\",\r\n          dataIndex: 'imItemId'\r\n        },\r\n        {\r\n          title: '物品名',\r\n          align: \"center\",\r\n          dataIndex: 'imItemName'\r\n        },\r\n        {\r\n          title: '物品单位',\r\n          align: \"center\",\r\n          dataIndex: 'imItemUnit'\r\n        },\r\n        {\r\n          title: '物品单价',\r\n          align: \"center\",\r\n          dataIndex: 'imItemPrice'\r\n        },\r\n        {\r\n          title: '成本',\r\n          align: \"center\",\r\n          dataIndex: 'imCost'\r\n        },\r\n        {\r\n          title: '库存数量',\r\n          align: \"center\",\r\n          dataIndex: 'imInventoryQuantity'\r\n        },\r\n        {\r\n          title: '可用库存',\r\n          align: \"center\",\r\n          dataIndex: 'imAvailableStock'\r\n        },\r\n        {\r\n          title: '总成本',\r\n          align: \"center\",\r\n          dataIndex: 'imTotalCost'\r\n        },\r\n        {\r\n          title: '操作',\r\n          dataIndex: 'action',\r\n          align: \"center\",\r\n          fixed: \"right\",\r\n          width: 147,\r\n          scopedSlots: {\r\n            customRender: 'action'\r\n          }\r\n        }\r\n      ],\r\n      url: {\r\n        list: \"/admin/inventoryManagement/list\",\r\n        delete: \"/admin/inventoryManagement/delete\",\r\n        deleteBatch: \"/admin/inventoryManagement/deleteBatch\",\r\n        exportXlsUrl: \"/admin/inventoryManagement/exportXls\",\r\n        importExcelUrl: \"admin/inventoryManagement/importExcel\",\r\n      },\r\n      dictOptions: {},\r\n      superFieldList: [],\r\n    }\r\n  },\r\n  created() {\r\n    this.getSuperFieldList();\r\n    this.enhanceColumns();\r\n  },\r\n  computed: {\r\n    importExcelUrl: function() {\r\n      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;\r\n    },\r\n    rowSelection() {\r\n      return {\r\n        selectedRowKeys: this.selectedRowKeys,\r\n        onChange: this.onSelectChange,\r\n        onSelect: this.onSelect,\r\n        onSelectAll: this.onSelectAll,\r\n      }\r\n    },\r\n\r\n    totalItems() {\r\n      return this.dataSource.length || 0;\r\n    },\r\n    totalInventory() {\r\n      return this.dataSource.reduce((sum, item) => {\r\n        return sum + (parseInt(item.imInventoryQuantity) || 0);\r\n      }, 0);\r\n    },\r\n    availableInventory() {\r\n      return this.dataSource.reduce((sum, item) => {\r\n        return sum + (parseInt(item.imAvailableStock) || 0);\r\n      }, 0);\r\n    },\r\n    totalCost() {\r\n      return this.dataSource.reduce((sum, item) => {\r\n        return sum + (parseFloat(item.imTotalCost) || 0);\r\n      }, 0);\r\n    }\r\n  },\r\n  methods: {\r\n\r\n    getColumns() {\r\n      if(this.inventoryTypeFilter === 'material'){\r\n        return this.enhancedColumns();\r\n      }else{\r\n        return this.productColumns();\r\n      }\r\n    },\r\n    enhancedColumns() {\r\n      return [\r\n        {\r\n          title: '物品信息',\r\n          align: \"center\",\r\n          children: [\r\n            // {\r\n            //   title: '产品名称',\r\n            //   align: \"left\",\r\n            //   dataIndex: 'imProductName',\r\n            //   width: 150,\r\n            // },\r\n            {\r\n              title: '物品名称',\r\n              align: \"left\",\r\n              dataIndex: 'imItemName',\r\n              width: 150,\r\n            },\r\n            // {\r\n            //   title: '物品ID',\r\n            //   align: \"center\",\r\n            //   dataIndex: 'imItemId',\r\n            //   width: 100,\r\n            // }\r\n          ]\r\n        },\r\n        {\r\n          title: '库存信息',\r\n          align: \"center\",\r\n          children: [\r\n            {\r\n              title: '库存数量',\r\n              align: \"center\",\r\n              dataIndex: 'imInventoryQuantity',\r\n              width: 150,\r\n              scopedSlots: { customRender: 'imInventoryQuantitySlot' }\r\n            },\r\n            {\r\n              title: '可用库存',\r\n              align: \"center\",\r\n              dataIndex: 'imAvailableStock',\r\n              width: 150,\r\n              scopedSlots: { customRender: 'imAvailableStockSlot' }\r\n            },\r\n            {\r\n              title: '单位',\r\n              align: \"center\",\r\n              dataIndex: 'imItemUnit',\r\n              width: 80,\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          title: '价格/成本',\r\n          align: \"center\",\r\n          children: [\r\n            {\r\n              title: '平均单价',\r\n              align: \"right\",\r\n              dataIndex: 'imItemPrice',\r\n              width: 120,\r\n              scopedSlots: { customRender: 'priceSlot' }\r\n            },\r\n            {\r\n              title: '总成本',\r\n              align: \"right\",\r\n              dataIndex: 'imTotalCost',\r\n              width: 120,\r\n              scopedSlots: { customRender: 'priceSlot' }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          title: '操作',\r\n          dataIndex: 'action',\r\n          align: \"center\",\r\n          fixed: \"right\",\r\n          width: 180,\r\n          scopedSlots: { customRender: 'action' }\r\n        }\r\n      ];\r\n    },\r\n    productColumns() {\r\n      return [\r\n        {\r\n          title: '产品信息',\r\n          align: \"center\",\r\n          children: [\r\n            {\r\n              title: '产品名称',\r\n              align: \"left\",\r\n              dataIndex: 'imProductName',\r\n              width: 150,\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          title: '库存信息',\r\n          align: \"center\",\r\n          children: [\r\n            {\r\n              title: '库存数量',\r\n              align: \"center\",\r\n              dataIndex: 'imInventoryQuantity',\r\n              width: 150,\r\n              scopedSlots: { customRender: 'imInventoryQuantitySlot' }\r\n            },\r\n            {\r\n              title: '可用库存',\r\n              align: \"center\",\r\n              dataIndex: 'imAvailableStock',\r\n              width: 150,\r\n              scopedSlots: { customRender: 'imAvailableStockSlot' }\r\n            },\r\n            {\r\n              title: '单位',\r\n              align: \"center\",\r\n              dataIndex: 'imItemUnit',\r\n              width: 80,\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          title: '价格/成本',\r\n          align: \"center\",\r\n          children: [\r\n            {\r\n              title: '平均单价成本',\r\n              align: \"right\",\r\n              dataIndex: 'imCost',\r\n              width: 120,\r\n              scopedSlots: { customRender: 'priceSlot' }\r\n            },\r\n            {\r\n              title: '总成本',\r\n              align: \"right\",\r\n              dataIndex: 'imTotalCost',\r\n              width: 120,\r\n              scopedSlots: { customRender: 'priceSlot' }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          title: '操作',\r\n          dataIndex: 'action',\r\n          align: \"center\",\r\n          fixed: \"right\",\r\n          width: 280,\r\n          scopedSlots: { customRender: 'action' }\r\n        }\r\n      ];\r\n    },\r\n    handleCardPageChange(page, pageSize) {\r\n      this.ipagination.current = page;\r\n      this.loadData();\r\n    },\r\n    handleCardShowSizeChange(current, size) {\r\n      this.ipagination.pageSize = size;\r\n      this.ipagination.current = 1;\r\n      this.loadData();\r\n    },\r\n    loadData(arg) {\r\n      if (arg === 1) {\r\n        this.ipagination.current = 1;\r\n      }\r\n\r\n      const params = this.getQueryParams();\r\n      if (this.inventoryTypeFilter !== 'all') {\r\n        params.imInventoryType = this.inventoryTypeFilter === 'material' ? '1' : '2';\r\n      }\r\n      this.loading = true;\r\n      this.$http.get(this.url.list, { params }).then((res) => {\r\n        if (res.success) {\r\n          this.dataSource = res.result.records || [];\r\n          this.ipagination.total = res.result.total || 0;\r\n        }\r\n        if (res.code === 510) {\r\n          this.$message.warning(res.message);\r\n        }\r\n        this.loading = false;\r\n        this.$nextTick(() => {\r\n          this.$forceUpdate();\r\n        });\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    initDictConfig() {},\r\n    getSuperFieldList() {\r\n      let fieldList = [];\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imProductId',\r\n        text: '产品id',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imProductName',\r\n        text: '产品名',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imItemId',\r\n        text: '物品id',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imItemName',\r\n        text: '物品名',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imItemUnit',\r\n        text: '物品单位',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'BigDecimal',\r\n        value: 'imItemPrice',\r\n        text: '物品单价',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'BigDecimal',\r\n        value: 'imCost',\r\n        text: '成本',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imInventoryQuantity',\r\n        text: '库存数量',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imAvailableStock',\r\n        text: '可用库存',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'BigDecimal',\r\n        value: 'imTotalCost',\r\n        text: '总成本',\r\n        dictCode: ''\r\n      })\r\n      this.superFieldList = fieldList\r\n    },\r\n    enhanceColumns() {},\r\n    toggleSelection(id) {\r\n      if (this.selectedRowKeys.includes(id)) {\r\n        this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== id);\r\n      } else {\r\n        this.selectedRowKeys.push(id);\r\n      }\r\n    },\r\n    onItemSelect(id, checked) {\r\n      if (checked && !this.selectedRowKeys.includes(id)) {\r\n        this.selectedRowKeys.push(id);\r\n      } else if (!checked && this.selectedRowKeys.includes(id)) {\r\n        this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== id);\r\n      }\r\n    },\r\n    calculateStockPercent(total, available) {\r\n      return 100;\r\n    },\r\n    calculateAvailablePercent(total, available) {\r\n      if (!total || total <= 0 || !available) return 0;\r\n      return Math.min(100, Math.round((available / total) * 100));\r\n    },\r\n    getStockColor(total, available) {\r\n      if (!total || !available) return '#f5222d';\r\n      const percent = (available / total) * 100;\r\n      if (percent < 20) return '#f5222d';\r\n      if (percent < 50) return '#faad14';\r\n      return '#52c41a';\r\n    },\r\n    formatNumber(num) {\r\n      if (!num || isNaN(num)) return '0.00';\r\n      return parseFloat(num).toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n    },\r\n    onSearch(value) {\r\n      this.queryParam = {\r\n        ...this.queryParam,\r\n        imProductName: value,\r\n        imItemName: value\r\n      };\r\n      this.loadData(1);\r\n    },\r\n    handleEdit(record) {\r\n      this.$refs.modalForm.edit(record);\r\n      this.$refs.modalForm.title = \"编辑\";\r\n      this.$refs.modalForm.disableSubmit = false;\r\n    },\r\n    handleDetail(record) {\r\n      this.$refs.detailDrawer.show(record);\r\n    },\r\n    \r\n    closeDetail() {\r\n      // 可以在这里处理详情关闭后的逻辑\r\n    },\r\n    batchDelete() {\r\n      if (this.selectedRowKeys.length <= 0) {\r\n        this.$message.warning('请选择一条记录！');\r\n        return false;\r\n      } else {\r\n        let ids = \"\";\r\n        for (let a = 0; a < this.selectedRowKeys.length; a++) {\r\n          ids += this.selectedRowKeys[a] + \",\";\r\n        }\r\n        this.$confirm({\r\n          title: \"确认删除\",\r\n          content: \"是否删除选中数据?\",\r\n          onOk: () => {\r\n            this.loading = true;\r\n            this.$http.delete(this.url.deleteBatch, { params: { ids: ids } }).then((res) => {\r\n              if (res.success) {\r\n                this.$message.success(res.message);\r\n                this.loadData();\r\n                this.onClearSelected();\r\n              } else {\r\n                this.$message.warning(res.message);\r\n              }\r\n              this.loading = false;\r\n            });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    handleBatchAdjust() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('请至少选择一项进行操作');\r\n        return;\r\n      }\r\n      this.$refs.batchAdjustModal.show();\r\n    },\r\n    \r\n    handleBatchAdjustSuccess() {\r\n      this.loadData();\r\n      this.selectedRowKeys = [];\r\n    },\r\n    \r\n    getSelectedItems() {\r\n      return this.dataSource.filter(item => this.selectedRowKeys.includes(item.id));\r\n    },\r\n\r\n    // 处理库存数量点击事件\r\n    handleInventoryClick(record) {\r\n      if (record.imInventoryType === '2') {\r\n        // 产品类型：显示产品成本、生产批次等信息\r\n        this.$refs.productInventoryDetailModal.show(record, 'inventory');\r\n      } else {\r\n        // 物料类型：显示采购详情\r\n        this.$refs.inventoryDetailModal.show(record, 'inventory');\r\n      }\r\n    },\r\n\r\n    // 处理可用库存点击事件\r\n    handleAvailableStockClick(record) {\r\n      if (record.imInventoryType === '2') {\r\n        // 产品类型：显示产品成本、生产批次等信息\r\n        this.$refs.productInventoryDetailModal.show(record, 'available');\r\n      } else {\r\n        // 物料类型：显示采购详情\r\n        this.$refs.inventoryDetailModal.show(record, 'available');\r\n      }\r\n    },\r\n\r\n    // 处理已售出库存点击事件\r\n    handleSoldInventory(record) {\r\n      this.$refs.soldInventoryModal.show(record);\r\n    }\r\n  }\r\n}\r\n", {"version": 3, "sources": ["InventoryManagementList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo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file": "InventoryManagementList.vue", "sourceRoot": "src/views/admin/purchaseSalesInventory/inventoryManagement", "sourcesContent": ["<template>\r\n  <div class=\"inventory-management-container\">\r\n    <!-- 顶部统计卡片 -->\r\n    <div class=\"stats-cards\">\r\n      <a-row :gutter=\"[16, 16]\">\r\n        <a-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\">\r\n          <div class=\"stat-card total-items\">\r\n            <div class=\"stat-icon\">\r\n              <a-icon type=\"database\" />\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <div class=\"stat-title\">总物品数</div>\r\n              <div class=\"stat-value\">{{ totalItems }}</div>\r\n              <div class=\"stat-trend\">\r\n                <a-icon type=\"arrow-up\" />\r\n                <span>{{ trendData.items }}%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </a-col>\r\n        <a-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\">\r\n          <div class=\"stat-card total-inventory\">\r\n            <div class=\"stat-icon\">\r\n              <a-icon type=\"inbox\" />\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <div class=\"stat-title\">库存总量</div>\r\n              <div class=\"stat-value\">{{ totalInventory }}</div>\r\n              <div class=\"stat-trend\">\r\n                <a-icon type=\"arrow-up\" />\r\n                <span>{{ trendData.inventory }}%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </a-col>\r\n        <a-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\">\r\n          <div class=\"stat-card available-inventory\">\r\n            <div class=\"stat-icon\">\r\n              <a-icon type=\"check-circle\" />\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <div class=\"stat-title\">可用库存</div>\r\n              <div class=\"stat-value\">{{ availableInventory }}</div>\r\n              <div class=\"stat-trend\">\r\n                <a-icon type=\"arrow-down\" />\r\n                <span>{{ trendData.available }}%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </a-col>\r\n        <a-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\">\r\n          <div class=\"stat-card total-cost\">\r\n            <div class=\"stat-icon\">\r\n              <a-icon type=\"dollar\" />\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <div class=\"stat-title\">总成本</div>\r\n              <div class=\"stat-value\">¥{{ formatNumber(totalCost) }}</div>\r\n              <div class=\"stat-trend\">\r\n                <a-icon type=\"arrow-up\" />\r\n                <span>{{ trendData.cost }}%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </a-col>\r\n      </a-row>\r\n    </div>\r\n\r\n    <!-- 搜索与过滤栏 -->\r\n    <div class=\"search-filter-bar\">\r\n      <div class=\"search-area\">\r\n        <a-input-search\r\n          placeholder=\"搜索产品名称或物品名称\"\r\n          @search=\"onSearch\"\r\n          allow-clear\r\n          class=\"search-input\"\r\n        >\r\n          <a-icon slot=\"prefix\" type=\"search\" />\r\n        </a-input-search>\r\n        <a-button-group class=\"type-filter\">\r\n<!--          <a-tooltip title=\"全部\">-->\r\n<!--            <a-button-->\r\n<!--              :type=\"inventoryTypeFilter === 'all' ? 'primary' : 'default'\"-->\r\n<!--              @click=\"() => { inventoryTypeFilter = 'all'; loadData(1); }\"-->\r\n<!--            >-->\r\n<!--              全部-->\r\n<!--            </a-button>-->\r\n<!--          </a-tooltip>-->\r\n          <a-tooltip title=\"物品\">\r\n            <a-button\r\n              :type=\"inventoryTypeFilter === 'material' ? 'primary' : 'default'\"\r\n              @click=\"() => { inventoryTypeFilter = 'material'; loadData(1); }\"\r\n            >\r\n              物料\r\n            </a-button>\r\n          </a-tooltip>\r\n          <a-tooltip title=\"产品\">\r\n            <a-button\r\n              :type=\"inventoryTypeFilter === 'product' ? 'primary' : 'default'\"\r\n              @click=\"() => { inventoryTypeFilter = 'product'; loadData(1); }\"\r\n            >\r\n              产品\r\n            </a-button>\r\n          </a-tooltip>\r\n        </a-button-group>\r\n      </div>\r\n\r\n      <div class=\"filter-actions\">\r\n        <a-button-group class=\"view-toggle\">\r\n          <a-tooltip title=\"数据表格视图\">\r\n            <a-button :type=\"viewMode === 'table' ? 'primary' : 'default'\" @click=\"viewMode = 'table'\">\r\n              <a-icon type=\"table\" />\r\n            </a-button>\r\n          </a-tooltip>\r\n          <a-tooltip title=\"卡片视图\">\r\n            <a-button :type=\"viewMode === 'card' ? 'primary' : 'default'\" @click=\"viewMode = 'card'\">\r\n              <a-icon type=\"appstore\" />\r\n            </a-button>\r\n          </a-tooltip>\r\n        </a-button-group>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 选择信息提示 -->\r\n    <div v-if=\"selectedRowKeys.length > 0\" class=\"selection-info\">\r\n      <a-alert\r\n        :message=\"`已选择 ${selectedRowKeys.length} 项`\"\r\n        type=\"info\"\r\n        show-icon\r\n        closable\r\n        @close=\"selectedRowKeys = []\"\r\n      >\r\n        <template slot=\"action\">\r\n          <a-button size=\"small\" type=\"primary\" @click=\"handleBatchAdjust\" style=\"margin-right: 8px;\">\r\n            <a-icon type=\"tool\" /> 批量调整\r\n          </a-button>\r\n          <a-button size=\"small\" type=\"danger\" @click=\"batchDelete\">\r\n            批量删除\r\n          </a-button>\r\n        </template>\r\n      </a-alert>\r\n    </div>\r\n\r\n    <!-- 卡片视图 -->\r\n    <div v-if=\"viewMode === 'card'\" class=\"inventory-card-view\">\r\n      <a-spin :spinning=\"loading\">\r\n        <a-empty v-if=\"dataSource.length === 0\" description=\"暂无库存数据\" />\r\n        <a-row :gutter=\"[16, 16]\" v-else>\r\n          <a-col :xs=\"24\" :sm=\"12\" :md=\"12\" :lg=\"8\" :xl=\"6\" v-for=\"item in dataSource\" :key=\"item.id\">\r\n            <div\r\n              class=\"inventory-card\"\r\n              :class=\"{ 'selected-card': selectedRowKeys.includes(item.id) }\"\r\n              @click=\"toggleSelection(item.id)\"\r\n            >\r\n              <div class=\"card-header\">\r\n                <div class=\"item-name\" :title=\"item.imItemName\">{{ item.imItemName }}</div>\r\n                <a-checkbox\r\n                  :checked=\"selectedRowKeys.includes(item.id)\"\r\n                  @change=\"(e) => onItemSelect(item.id, e.target.checked)\"\r\n                  @click.stop\r\n                />\r\n              </div>\r\n\r\n              <div class=\"card-product\">\r\n                <span>产品: {{ item.imProductName }}</span>\r\n                <span class=\"item-id\">ID: {{ item.imItemId }}</span>\r\n              </div>\r\n\r\n              <div class=\"inventory-metrics\">\r\n                <div class=\"metric-item\">\r\n                  <div class=\"metric-value\">{{ item.imInventoryQuantity }}</div>\r\n                  <div class=\"metric-label\">库存数量</div>\r\n                  <a-progress\r\n                    :percent=\"100\"\r\n                    strokeColor=\"#52c41a\"\r\n                    size=\"small\"\r\n                    :showInfo=\"false\"\r\n                  />\r\n                </div>\r\n\r\n                <div class=\"metric-item\">\r\n                  <div class=\"metric-value\">{{ item.imAvailableStock }}</div>\r\n                  <div class=\"metric-label\">可用库存</div>\r\n                  <a-progress\r\n                    :percent=\"calculateAvailablePercent(item.imInventoryQuantity, item.imAvailableStock)\"\r\n                    :strokeColor=\"getStockColor(item.imInventoryQuantity, item.imAvailableStock)\"\r\n                    size=\"small\"\r\n                    :showInfo=\"false\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"price-info\">\r\n                <div class=\"price-item\">\r\n                  <span class=\"price-label\">单价:</span>\r\n                  <span class=\"price-value\">¥{{ formatNumber(item.imItemPrice) }}</span>\r\n                  <span class=\"unit-label\">/ {{ item.imItemUnit }}</span>\r\n                </div>\r\n                <div class=\"price-item\">\r\n                  <span class=\"price-label\">总成本:</span>\r\n                  <span class=\"price-value\">¥{{ formatNumber(item.imTotalCost) }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"card-actions\">\r\n                <a-button type=\"link\" @click.stop=\"handleDetail(item)\">\r\n                  <a-icon type=\"eye\" /> 详情\r\n                </a-button>\r\n                <!-- <a-button type=\"link\" class=\"edit-btn\" @click.stop=\"handleEdit(item)\">\r\n                  <a-icon type=\"edit\" /> 编辑\r\n                </a-button>\r\n                <a-button type=\"link\" class=\"delete-btn\" @click.stop=\"handleDelete(item.id)\">\r\n                  <a-icon type=\"delete\" /> 删除\r\n                </a-button> -->\r\n              </div>\r\n            </div>\r\n          </a-col>\r\n        </a-row>\r\n        <!-- 卡片分页 -->\r\n        <div class=\"card-pagination\">\r\n          <a-pagination\r\n            :current=\"ipagination.current\"\r\n            :pageSize=\"ipagination.pageSize\"\r\n            :total=\"ipagination.total\"\r\n            :pageSizeOptions=\"ipagination.pageSizeOptions\"\r\n            :showTotal=\"(total, range) => `${range[0]}-${range[1]} 共 ${total} 条`\"\r\n            showSizeChanger\r\n            showQuickJumper\r\n            @change=\"handleCardPageChange\"\r\n            @showSizeChange=\"handleCardShowSizeChange\"\r\n          />\r\n        </div>\r\n      </a-spin>\r\n    </div>\r\n\r\n    <!-- 表格视图 -->\r\n    <div v-else-if=\"viewMode === 'table'\" class=\"inventory-table-view\">\r\n      <a-spin :spinning=\"loading\">\r\n        <a-table\r\n          ref=\"table\"\r\n          size=\"middle\"\r\n          :scroll=\"{x:true}\"\r\n          :rowKey=\"record => record.id\"\r\n          :columns=\"getColumns()\"\r\n          :dataSource=\"dataSource\"\r\n          :pagination=\"ipagination\"\r\n          :rowSelection=\"rowSelection\"\r\n          class=\"custom-inventory-table\"\r\n          @change=\"handleTableChange\"\r\n        >\r\n          <template slot=\"imInventoryQuantitySlot\" slot-scope=\"text, record\">\r\n            <div class=\"inventory-cell clickable-cell\" @click=\"handleInventoryClick(record)\">\r\n              <span class=\"inventory-number\">{{ text }}</span>\r\n              <a-progress\r\n                :percent=\"calculateStockPercent(record.imInventoryQuantity, record.imAvailableStock)\"\r\n                size=\"small\"\r\n                strokeColor=\"#52c41a\"\r\n                :showInfo=\"false\"\r\n              />\r\n            </div>\r\n          </template>\r\n\r\n          <template slot=\"imAvailableStockSlot\" slot-scope=\"text, record\">\r\n            <div class=\"inventory-cell clickable-cell\" @click=\"handleAvailableStockClick(record)\">\r\n              <span class=\"inventory-number\">{{ text }}</span>\r\n              <a-progress\r\n                :percent=\"calculateAvailablePercent(record.imInventoryQuantity, record.imAvailableStock)\"\r\n                :strokeColor=\"getStockColor(record.imInventoryQuantity, record.imAvailableStock)\"\r\n                size=\"small\"\r\n                :showInfo=\"false\"\r\n              />\r\n            </div>\r\n          </template>\r\n\r\n          <template slot=\"priceSlot\" slot-scope=\"text\">\r\n            <span class=\"price-text\">¥{{ formatNumber(text) }}</span>\r\n          </template>\r\n\r\n          <template slot=\"action\" slot-scope=\"text, record\">\r\n            <div class=\"table-actions\">\r\n              <a-button type=\"link\" @click=\"handleDetail(record)\">\r\n                <a-icon type=\"eye\" /> 详情\r\n              </a-button>\r\n              <a-button v-if=\"record.imInventoryType === '2'\" type=\"link\" @click=\"handleSoldInventory(record)\">\r\n                <a-icon type=\"shopping-cart\" /> 已售出库存\r\n              </a-button>\r\n              <!-- <a-button type=\"link\" class=\"edit-btn\" @click=\"handleEdit(record)\">\r\n                <a-icon type=\"edit\" /> 编辑\r\n              </a-button>\r\n              <a-button type=\"link\" class=\"delete-btn\" @click=\"handleDelete(record.id)\">\r\n                <a-icon type=\"delete\" /> 删除\r\n              </a-button> -->\r\n            </div>\r\n          </template>\r\n        </a-table>\r\n      </a-spin>\r\n    </div>\r\n\r\n    <inventory-management-modal ref=\"modalForm\" @ok=\"modalFormOk\"></inventory-management-modal>\r\n    <inventory-management-detail ref=\"detailDrawer\" @close=\"closeDetail\" @edit=\"handleEdit\"></inventory-management-detail>\r\n    <batch-stock-adjust-modal ref=\"batchAdjustModal\" :selected-items=\"getSelectedItems()\" @success=\"handleBatchAdjustSuccess\"></batch-stock-adjust-modal>\r\n    <inventory-detail-modal ref=\"inventoryDetailModal\"></inventory-detail-modal>\r\n    <product-inventory-detail-modal ref=\"productInventoryDetailModal\"></product-inventory-detail-modal>\r\n    <sold-inventory-modal ref=\"soldInventoryModal\"></sold-inventory-modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport '@/assets/less/TableExpand.less'\r\nimport { mixinDevice } from '@/utils/mixin'\r\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin'\r\nimport InventoryManagementModal from './modules/InventoryManagementModal'\r\nimport InventoryManagementDetail from './modules/InventoryManagementDetail'\r\nimport BatchStockAdjustModal from './modules/BatchStockAdjustModal'\r\nimport InventoryDetailModal from './modules/InventoryDetailModal'\r\nimport ProductInventoryDetailModal from './modules/ProductInventoryDetailModal'\r\nimport SoldInventoryModal from './modules/SoldInventoryModal'\r\n\r\nexport default {\r\n  name: 'InventoryManagementList',\r\n  mixins: [JeecgListMixin, mixinDevice],\r\n  components: {\r\n    InventoryManagementModal,\r\n    InventoryManagementDetail,\r\n    BatchStockAdjustModal,\r\n    InventoryDetailModal,\r\n    ProductInventoryDetailModal,\r\n    SoldInventoryModal\r\n  },\r\n  data() {\r\n    return {\r\n      allDataForStats: [],\r\n      inventoryTypeFilter: 'material',\r\n      trendData: {\r\n        items: 5.2,\r\n        inventory: 3.7,\r\n        available: -1.2,\r\n        cost: 2.4\r\n      },\r\n      description: '库存管理表管理页面',\r\n      viewMode: 'table',\r\n      columns: [\r\n        {\r\n          title: '#',\r\n          dataIndex: '',\r\n          key: 'rowIndex',\r\n          width: 60,\r\n          align: \"center\",\r\n          customRender: function(t, r, index) {\r\n            return parseInt(index) + 1;\r\n          }\r\n        },\r\n        {\r\n          title: '产品id',\r\n          align: \"center\",\r\n          dataIndex: 'imProductId'\r\n        },\r\n        {\r\n          title: '产品名',\r\n          align: \"center\",\r\n          dataIndex: 'imProductName'\r\n        },\r\n        {\r\n          title: '物品id',\r\n          align: \"center\",\r\n          dataIndex: 'imItemId'\r\n        },\r\n        {\r\n          title: '物品名',\r\n          align: \"center\",\r\n          dataIndex: 'imItemName'\r\n        },\r\n        {\r\n          title: '物品单位',\r\n          align: \"center\",\r\n          dataIndex: 'imItemUnit'\r\n        },\r\n        {\r\n          title: '物品单价',\r\n          align: \"center\",\r\n          dataIndex: 'imItemPrice'\r\n        },\r\n        {\r\n          title: '成本',\r\n          align: \"center\",\r\n          dataIndex: 'imCost'\r\n        },\r\n        {\r\n          title: '库存数量',\r\n          align: \"center\",\r\n          dataIndex: 'imInventoryQuantity'\r\n        },\r\n        {\r\n          title: '可用库存',\r\n          align: \"center\",\r\n          dataIndex: 'imAvailableStock'\r\n        },\r\n        {\r\n          title: '总成本',\r\n          align: \"center\",\r\n          dataIndex: 'imTotalCost'\r\n        },\r\n        {\r\n          title: '操作',\r\n          dataIndex: 'action',\r\n          align: \"center\",\r\n          fixed: \"right\",\r\n          width: 147,\r\n          scopedSlots: {\r\n            customRender: 'action'\r\n          }\r\n        }\r\n      ],\r\n      url: {\r\n        list: \"/admin/inventoryManagement/list\",\r\n        delete: \"/admin/inventoryManagement/delete\",\r\n        deleteBatch: \"/admin/inventoryManagement/deleteBatch\",\r\n        exportXlsUrl: \"/admin/inventoryManagement/exportXls\",\r\n        importExcelUrl: \"admin/inventoryManagement/importExcel\",\r\n      },\r\n      dictOptions: {},\r\n      superFieldList: [],\r\n    }\r\n  },\r\n  created() {\r\n    this.getSuperFieldList();\r\n    this.enhanceColumns();\r\n  },\r\n  computed: {\r\n    importExcelUrl: function() {\r\n      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;\r\n    },\r\n    rowSelection() {\r\n      return {\r\n        selectedRowKeys: this.selectedRowKeys,\r\n        onChange: this.onSelectChange,\r\n        onSelect: this.onSelect,\r\n        onSelectAll: this.onSelectAll,\r\n      }\r\n    },\r\n\r\n    totalItems() {\r\n      return this.dataSource.length || 0;\r\n    },\r\n    totalInventory() {\r\n      return this.dataSource.reduce((sum, item) => {\r\n        return sum + (parseInt(item.imInventoryQuantity) || 0);\r\n      }, 0);\r\n    },\r\n    availableInventory() {\r\n      return this.dataSource.reduce((sum, item) => {\r\n        return sum + (parseInt(item.imAvailableStock) || 0);\r\n      }, 0);\r\n    },\r\n    totalCost() {\r\n      return this.dataSource.reduce((sum, item) => {\r\n        return sum + (parseFloat(item.imTotalCost) || 0);\r\n      }, 0);\r\n    }\r\n  },\r\n  methods: {\r\n\r\n    getColumns() {\r\n      if(this.inventoryTypeFilter === 'material'){\r\n        return this.enhancedColumns();\r\n      }else{\r\n        return this.productColumns();\r\n      }\r\n    },\r\n    enhancedColumns() {\r\n      return [\r\n        {\r\n          title: '物品信息',\r\n          align: \"center\",\r\n          children: [\r\n            // {\r\n            //   title: '产品名称',\r\n            //   align: \"left\",\r\n            //   dataIndex: 'imProductName',\r\n            //   width: 150,\r\n            // },\r\n            {\r\n              title: '物品名称',\r\n              align: \"left\",\r\n              dataIndex: 'imItemName',\r\n              width: 150,\r\n            },\r\n            // {\r\n            //   title: '物品ID',\r\n            //   align: \"center\",\r\n            //   dataIndex: 'imItemId',\r\n            //   width: 100,\r\n            // }\r\n          ]\r\n        },\r\n        {\r\n          title: '库存信息',\r\n          align: \"center\",\r\n          children: [\r\n            {\r\n              title: '库存数量',\r\n              align: \"center\",\r\n              dataIndex: 'imInventoryQuantity',\r\n              width: 150,\r\n              scopedSlots: { customRender: 'imInventoryQuantitySlot' }\r\n            },\r\n            {\r\n              title: '可用库存',\r\n              align: \"center\",\r\n              dataIndex: 'imAvailableStock',\r\n              width: 150,\r\n              scopedSlots: { customRender: 'imAvailableStockSlot' }\r\n            },\r\n            {\r\n              title: '单位',\r\n              align: \"center\",\r\n              dataIndex: 'imItemUnit',\r\n              width: 80,\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          title: '价格/成本',\r\n          align: \"center\",\r\n          children: [\r\n            {\r\n              title: '平均单价',\r\n              align: \"right\",\r\n              dataIndex: 'imItemPrice',\r\n              width: 120,\r\n              scopedSlots: { customRender: 'priceSlot' }\r\n            },\r\n            {\r\n              title: '总成本',\r\n              align: \"right\",\r\n              dataIndex: 'imTotalCost',\r\n              width: 120,\r\n              scopedSlots: { customRender: 'priceSlot' }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          title: '操作',\r\n          dataIndex: 'action',\r\n          align: \"center\",\r\n          fixed: \"right\",\r\n          width: 180,\r\n          scopedSlots: { customRender: 'action' }\r\n        }\r\n      ];\r\n    },\r\n    productColumns() {\r\n      return [\r\n        {\r\n          title: '产品信息',\r\n          align: \"center\",\r\n          children: [\r\n            {\r\n              title: '产品名称',\r\n              align: \"left\",\r\n              dataIndex: 'imProductName',\r\n              width: 150,\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          title: '库存信息',\r\n          align: \"center\",\r\n          children: [\r\n            {\r\n              title: '库存数量',\r\n              align: \"center\",\r\n              dataIndex: 'imInventoryQuantity',\r\n              width: 150,\r\n              scopedSlots: { customRender: 'imInventoryQuantitySlot' }\r\n            },\r\n            {\r\n              title: '可用库存',\r\n              align: \"center\",\r\n              dataIndex: 'imAvailableStock',\r\n              width: 150,\r\n              scopedSlots: { customRender: 'imAvailableStockSlot' }\r\n            },\r\n            {\r\n              title: '单位',\r\n              align: \"center\",\r\n              dataIndex: 'imItemUnit',\r\n              width: 80,\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          title: '价格/成本',\r\n          align: \"center\",\r\n          children: [\r\n            {\r\n              title: '平均单价成本',\r\n              align: \"right\",\r\n              dataIndex: 'imCost',\r\n              width: 120,\r\n              scopedSlots: { customRender: 'priceSlot' }\r\n            },\r\n            {\r\n              title: '总成本',\r\n              align: \"right\",\r\n              dataIndex: 'imTotalCost',\r\n              width: 120,\r\n              scopedSlots: { customRender: 'priceSlot' }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          title: '操作',\r\n          dataIndex: 'action',\r\n          align: \"center\",\r\n          fixed: \"right\",\r\n          width: 280,\r\n          scopedSlots: { customRender: 'action' }\r\n        }\r\n      ];\r\n    },\r\n    handleCardPageChange(page, pageSize) {\r\n      this.ipagination.current = page;\r\n      this.loadData();\r\n    },\r\n    handleCardShowSizeChange(current, size) {\r\n      this.ipagination.pageSize = size;\r\n      this.ipagination.current = 1;\r\n      this.loadData();\r\n    },\r\n    loadData(arg) {\r\n      if (arg === 1) {\r\n        this.ipagination.current = 1;\r\n      }\r\n\r\n      const params = this.getQueryParams();\r\n      if (this.inventoryTypeFilter !== 'all') {\r\n        params.imInventoryType = this.inventoryTypeFilter === 'material' ? '1' : '2';\r\n      }\r\n      this.loading = true;\r\n      this.$http.get(this.url.list, { params }).then((res) => {\r\n        if (res.success) {\r\n          this.dataSource = res.result.records || [];\r\n          this.ipagination.total = res.result.total || 0;\r\n        }\r\n        if (res.code === 510) {\r\n          this.$message.warning(res.message);\r\n        }\r\n        this.loading = false;\r\n        this.$nextTick(() => {\r\n          this.$forceUpdate();\r\n        });\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    initDictConfig() {},\r\n    getSuperFieldList() {\r\n      let fieldList = [];\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imProductId',\r\n        text: '产品id',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imProductName',\r\n        text: '产品名',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imItemId',\r\n        text: '物品id',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imItemName',\r\n        text: '物品名',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imItemUnit',\r\n        text: '物品单位',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'BigDecimal',\r\n        value: 'imItemPrice',\r\n        text: '物品单价',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'BigDecimal',\r\n        value: 'imCost',\r\n        text: '成本',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imInventoryQuantity',\r\n        text: '库存数量',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'string',\r\n        value: 'imAvailableStock',\r\n        text: '可用库存',\r\n        dictCode: ''\r\n      })\r\n      fieldList.push({\r\n        type: 'BigDecimal',\r\n        value: 'imTotalCost',\r\n        text: '总成本',\r\n        dictCode: ''\r\n      })\r\n      this.superFieldList = fieldList\r\n    },\r\n    enhanceColumns() {},\r\n    toggleSelection(id) {\r\n      if (this.selectedRowKeys.includes(id)) {\r\n        this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== id);\r\n      } else {\r\n        this.selectedRowKeys.push(id);\r\n      }\r\n    },\r\n    onItemSelect(id, checked) {\r\n      if (checked && !this.selectedRowKeys.includes(id)) {\r\n        this.selectedRowKeys.push(id);\r\n      } else if (!checked && this.selectedRowKeys.includes(id)) {\r\n        this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== id);\r\n      }\r\n    },\r\n    calculateStockPercent(total, available) {\r\n      return 100;\r\n    },\r\n    calculateAvailablePercent(total, available) {\r\n      if (!total || total <= 0 || !available) return 0;\r\n      return Math.min(100, Math.round((available / total) * 100));\r\n    },\r\n    getStockColor(total, available) {\r\n      if (!total || !available) return '#f5222d';\r\n      const percent = (available / total) * 100;\r\n      if (percent < 20) return '#f5222d';\r\n      if (percent < 50) return '#faad14';\r\n      return '#52c41a';\r\n    },\r\n    formatNumber(num) {\r\n      if (!num || isNaN(num)) return '0.00';\r\n      return parseFloat(num).toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n    },\r\n    onSearch(value) {\r\n      this.queryParam = {\r\n        ...this.queryParam,\r\n        imProductName: value,\r\n        imItemName: value\r\n      };\r\n      this.loadData(1);\r\n    },\r\n    handleEdit(record) {\r\n      this.$refs.modalForm.edit(record);\r\n      this.$refs.modalForm.title = \"编辑\";\r\n      this.$refs.modalForm.disableSubmit = false;\r\n    },\r\n    handleDetail(record) {\r\n      this.$refs.detailDrawer.show(record);\r\n    },\r\n    \r\n    closeDetail() {\r\n      // 可以在这里处理详情关闭后的逻辑\r\n    },\r\n    batchDelete() {\r\n      if (this.selectedRowKeys.length <= 0) {\r\n        this.$message.warning('请选择一条记录！');\r\n        return false;\r\n      } else {\r\n        let ids = \"\";\r\n        for (let a = 0; a < this.selectedRowKeys.length; a++) {\r\n          ids += this.selectedRowKeys[a] + \",\";\r\n        }\r\n        this.$confirm({\r\n          title: \"确认删除\",\r\n          content: \"是否删除选中数据?\",\r\n          onOk: () => {\r\n            this.loading = true;\r\n            this.$http.delete(this.url.deleteBatch, { params: { ids: ids } }).then((res) => {\r\n              if (res.success) {\r\n                this.$message.success(res.message);\r\n                this.loadData();\r\n                this.onClearSelected();\r\n              } else {\r\n                this.$message.warning(res.message);\r\n              }\r\n              this.loading = false;\r\n            });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    handleBatchAdjust() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('请至少选择一项进行操作');\r\n        return;\r\n      }\r\n      this.$refs.batchAdjustModal.show();\r\n    },\r\n    \r\n    handleBatchAdjustSuccess() {\r\n      this.loadData();\r\n      this.selectedRowKeys = [];\r\n    },\r\n    \r\n    getSelectedItems() {\r\n      return this.dataSource.filter(item => this.selectedRowKeys.includes(item.id));\r\n    },\r\n\r\n    // 处理库存数量点击事件\r\n    handleInventoryClick(record) {\r\n      if (record.imInventoryType === '2') {\r\n        // 产品类型：显示产品成本、生产批次等信息\r\n        this.$refs.productInventoryDetailModal.show(record, 'inventory');\r\n      } else {\r\n        // 物料类型：显示采购详情\r\n        this.$refs.inventoryDetailModal.show(record, 'inventory');\r\n      }\r\n    },\r\n\r\n    // 处理可用库存点击事件\r\n    handleAvailableStockClick(record) {\r\n      if (record.imInventoryType === '2') {\r\n        // 产品类型：显示产品成本、生产批次等信息\r\n        this.$refs.productInventoryDetailModal.show(record, 'available');\r\n      } else {\r\n        // 物料类型：显示采购详情\r\n        this.$refs.inventoryDetailModal.show(record, 'available');\r\n      }\r\n    },\r\n\r\n    // 处理已售出库存点击事件\r\n    handleSoldInventory(record) {\r\n      this.$refs.soldInventoryModal.show(record);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 容器样式 */\r\n.inventory-management-container {\r\n  padding: 24px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-cards {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 24px;\r\n  background: #ffffff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n  border: 1px solid #f0f0f0;\r\n  height: 100%;\r\n  transition: all 0.3s ease;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.stat-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 4px;\r\n  height: 100%;\r\n}\r\n\r\n.stat-card.total-items::before {\r\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\r\n}\r\n\r\n.stat-card.total-inventory::before {\r\n  background: linear-gradient(135deg, #faad14 0%, #fa8c16 100%);\r\n}\r\n\r\n.stat-card.available-inventory::before {\r\n  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);\r\n}\r\n\r\n.stat-card.total-cost::before {\r\n  background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);\r\n}\r\n\r\n.stat-icon {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 64px;\r\n  height: 64px;\r\n  border-radius: 50%;\r\n  margin-right: 20px;\r\n  font-size: 28px;\r\n  color: #ffffff;\r\n}\r\n\r\n.stat-card.total-items .stat-icon {\r\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\r\n}\r\n\r\n.stat-card.total-inventory .stat-icon {\r\n  background: linear-gradient(135deg, #faad14 0%, #fa8c16 100%);\r\n}\r\n\r\n.stat-card.available-inventory .stat-icon {\r\n  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);\r\n}\r\n\r\n.stat-card.total-cost .stat-icon {\r\n  background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);\r\n}\r\n\r\n.stat-info {\r\n  flex: 1;\r\n}\r\n\r\n.stat-title {\r\n  color: #595959;\r\n  font-size: 14px;\r\n  margin-bottom: 8px;\r\n  font-weight: 400;\r\n}\r\n\r\n.stat-value {\r\n  color: #262626;\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  margin-bottom: 8px;\r\n  line-height: 1.2;\r\n}\r\n\r\n.stat-trend {\r\n  font-size: 12px;\r\n  color: #8c8c8c;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-trend .anticon-arrow-up {\r\n  color: #52c41a;\r\n}\r\n\r\n.stat-trend .anticon-arrow-down {\r\n  color: #ff4d4f;\r\n}\r\n\r\n/* 搜索过滤栏样式 */\r\n.search-filter-bar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  background: #ffffff;\r\n  padding: 20px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.search-area {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-input {\r\n  width: 320px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.type-filter {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.type-filter .ant-btn {\r\n  border-radius: 0;\r\n  border-right: 1px solid #d9d9d9;\r\n}\r\n\r\n.type-filter .ant-btn:first-child {\r\n  border-radius: 8px 0 0 8px;\r\n}\r\n\r\n.type-filter .ant-btn:last-child {\r\n  border-radius: 0 8px 8px 0;\r\n  border-right: none;\r\n}\r\n\r\n.filter-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.view-toggle {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.view-toggle .ant-btn {\r\n  border-radius: 0;\r\n  border-right: 1px solid #d9d9d9;\r\n}\r\n\r\n.view-toggle .ant-btn:first-child {\r\n  border-radius: 8px 0 0 8px;\r\n}\r\n\r\n.view-toggle .ant-btn:last-child {\r\n  border-radius: 0 8px 8px 0;\r\n  border-right: none;\r\n}\r\n\r\n/* 选择信息样式 */\r\n.selection-info {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 卡片视图样式 */\r\n.inventory-card-view {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.inventory-card {\r\n  background: #ffffff;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  height: 100%;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n  border: 2px solid transparent;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.inventory-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\r\n  border-color: #e6f7ff;\r\n}\r\n\r\n.inventory-card.selected-card {\r\n  border-color: #1890ff;\r\n  background: #e6f7ff;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.item-name {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n  max-width: calc(100% - 40px);\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.card-product {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  color: #595959;\r\n  font-size: 14px;\r\n}\r\n\r\n.item-id {\r\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\r\n  color: #8c8c8c;\r\n  background: #f5f5f5;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.inventory-metrics {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 20px;\r\n  gap: 16px;\r\n}\r\n\r\n.metric-item {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.metric-value {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #262626;\r\n  margin-bottom: 4px;\r\n  line-height: 1.2;\r\n}\r\n\r\n.metric-label {\r\n  font-size: 12px;\r\n  color: #8c8c8c;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.price-info {\r\n  padding: 16px;\r\n  background: #fafafa;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.price-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.price-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.price-label {\r\n  color: #8c8c8c;\r\n  margin-right: 8px;\r\n  width: 60px;\r\n  font-size: 14px;\r\n}\r\n\r\n.price-value {\r\n  color: #262626;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n}\r\n\r\n.unit-label {\r\n  color: #8c8c8c;\r\n  margin-left: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.card-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 8px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-actions .ant-btn {\r\n  padding: 4px 12px;\r\n  height: auto;\r\n  font-size: 12px;\r\n}\r\n\r\n.edit-btn {\r\n  color: #1890ff;\r\n}\r\n\r\n.delete-btn {\r\n  color: #ff4d4f;\r\n}\r\n\r\n/* 卡片分页样式 */\r\n.card-pagination {\r\n  margin-top: 32px;\r\n  text-align: center;\r\n  background: #ffffff;\r\n  padding: 20px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n/* 表格视图样式 */\r\n.inventory-table-view {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.custom-inventory-table {\r\n  background: #ffffff;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.inventory-cell {\r\n  text-align: center;\r\n\r\n  &.clickable-cell {\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    padding: 8px;\r\n    border-radius: 4px;\r\n\r\n    &:hover {\r\n      background-color: #e6f7ff;\r\n      transform: translateY(-1px);\r\n      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);\r\n    }\r\n  }\r\n}\r\n\r\n.inventory-number {\r\n  display: block;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n  color: #262626;\r\n  font-size: 16px;\r\n}\r\n\r\n.price-text {\r\n  font-weight: 600;\r\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\r\n  color: #262626;\r\n  font-size: 14px;\r\n}\r\n\r\n.table-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.table-actions .ant-btn {\r\n  padding: 4px 8px;\r\n  height: auto;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 移动端适配 */\r\n@media (max-width: 768px) {\r\n  .inventory-management-container {\r\n    padding: 16px;\r\n  }\r\n\r\n  .search-filter-bar {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 12px;\r\n  }\r\n\r\n  .search-area {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n\r\n  .search-input {\r\n    width: 100%;\r\n  }\r\n\r\n  .filter-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .stat-card {\r\n    padding: 16px;\r\n  }\r\n\r\n  .stat-icon {\r\n    width: 48px;\r\n    height: 48px;\r\n    margin-right: 16px;\r\n    font-size: 24px;\r\n  }\r\n\r\n  .stat-value {\r\n    font-size: 24px;\r\n  }\r\n\r\n  .inventory-card {\r\n    padding: 16px;\r\n  }\r\n\r\n  .inventory-card:hover {\r\n    transform: none;\r\n  }\r\n\r\n  .inventory-metrics {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n\r\n  .metric-item {\r\n    text-align: left;\r\n  }\r\n\r\n  .card-actions {\r\n    flex-wrap: wrap;\r\n    gap: 4px;\r\n  }\r\n\r\n  .card-actions .ant-btn {\r\n    flex: 1;\r\n    min-width: 0;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .inventory-management-container {\r\n    padding: 8px;\r\n  }\r\n\r\n  .stat-card {\r\n    padding: 12px;\r\n  }\r\n\r\n  .stat-icon {\r\n    width: 40px;\r\n    height: 40px;\r\n    margin-right: 12px;\r\n    font-size: 20px;\r\n  }\r\n\r\n  .stat-value {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .inventory-card {\r\n    padding: 12px;\r\n  }\r\n\r\n  .item-name {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .metric-value {\r\n    font-size: 24px;\r\n  }\r\n}\r\n</style>"]}]}