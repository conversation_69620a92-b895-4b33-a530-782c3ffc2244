{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\modules\\InventoryDetailModal.vue?vue&type=template&id=38b42cac&scoped=true&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\modules\\InventoryDetailModal.vue", "mtime": 1753844660496}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753423171139}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"a-modal\",\n    {\n      staticClass: \"inventory-detail-modal\",\n      attrs: {\n        title: _vm.modalTitle,\n        width: 800,\n        visible: _vm.visible,\n        confirmLoading: _vm.confirmLoading,\n        destroyOnClose: \"\"\n      },\n      on: { ok: _vm.handleOk, cancel: _vm.handleCancel }\n    },\n    [\n      _c(\"a-spin\", { attrs: { spinning: _vm.loading } }, [\n        _c(\"div\", { staticClass: \"detail-content\" }, [\n          _c(\"div\", { staticClass: \"detail-section\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"section-title\" },\n              [\n                _c(\"a-icon\", {\n                  attrs: {\n                    type:\n                      _vm.record.imInventoryType === \"1\"\n                        ? \"database\"\n                        : \"appstore\"\n                  }\n                }),\n                _c(\"span\", [\n                  _vm._v(\n                    _vm._s(\n                      _vm.record.imInventoryType === \"1\"\n                        ? \"物料信息\"\n                        : \"产品信息\"\n                    )\n                  )\n                ])\n              ],\n              1\n            ),\n            _c(\"div\", { staticClass: \"info-grid\" }, [\n              _c(\"div\", { staticClass: \"info-item\" }, [\n                _c(\"div\", { staticClass: \"info-label\" }, [\n                  _vm._v(\n                    _vm._s(\n                      _vm.record.imInventoryType === \"1\"\n                        ? \"物料名称\"\n                        : \"产品名称\"\n                    )\n                  )\n                ]),\n                _c(\"div\", { staticClass: \"info-value\" }, [\n                  _vm._v(\n                    _vm._s(\n                      _vm.record.imInventoryType === \"1\"\n                        ? _vm.record.imItemName\n                        : _vm.record.imProductName\n                    )\n                  )\n                ])\n              ]),\n              _c(\"div\", { staticClass: \"info-item\" }, [\n                _c(\"div\", { staticClass: \"info-label\" }, [_vm._v(\"单位\")]),\n                _c(\"div\", { staticClass: \"info-value\" }, [\n                  _vm._v(_vm._s(_vm.record.imItemUnit || \"-\"))\n                ])\n              ]),\n              _c(\"div\", { staticClass: \"info-item\" }, [\n                _c(\"div\", { staticClass: \"info-label\" }, [\n                  _vm._v(\n                    _vm._s(\n                      _vm.clickType === \"inventory\"\n                        ? \"总库存数量\"\n                        : \"可用库存数量\"\n                    )\n                  )\n                ]),\n                _c(\"div\", { staticClass: \"info-value highlight\" }, [\n                  _vm._v(\n                    _vm._s(\n                      _vm.clickType === \"inventory\"\n                        ? _vm.record.imInventoryQuantity\n                        : _vm.record.imAvailableStock\n                    )\n                  )\n                ])\n              ]),\n              _c(\"div\", { staticClass: \"info-item\" }, [\n                _c(\"div\", { staticClass: \"info-label\" }, [\n                  _vm._v(\n                    _vm._s(\n                      _vm.record.imInventoryType === \"1\"\n                        ? \"平均单价\"\n                        : \"平均成本\"\n                    )\n                  )\n                ]),\n                _c(\"div\", { staticClass: \"info-value price\" }, [\n                  _vm._v(\n                    \"¥\" +\n                      _vm._s(\n                        _vm.formatNumber(\n                          _vm.record.imInventoryType === \"1\"\n                            ? _vm.record.imItemPrice\n                            : _vm.record.imCost\n                        )\n                      )\n                  )\n                ])\n              ])\n            ])\n          ]),\n          _vm.record.imInventoryType === \"1\"\n            ? _c(\n                \"div\",\n                { staticClass: \"detail-section\" },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"section-title\" },\n                    [\n                      _c(\"a-icon\", { attrs: { type: \"shopping\" } }),\n                      _c(\"span\", [_vm._v(\"采购详情\")])\n                    ],\n                    1\n                  ),\n                  _c(\"a-table\", {\n                    attrs: {\n                      columns: _vm.purchaseColumns,\n                      dataSource: _vm.purchaseDetails,\n                      pagination: false,\n                      size: \"small\",\n                      loading: _vm.detailLoading,\n                      locale: { emptyText: \"暂无采购记录数据\" },\n                      scroll: { y: 250 }\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"cost\",\n                          fn: function(text) {\n                            return [\n                              _c(\"span\", { staticClass: \"price-text\" }, [\n                                _vm._v(\"¥\" + _vm._s(_vm.formatNumber(text)))\n                              ])\n                            ]\n                          }\n                        },\n                        {\n                          key: \"quantity\",\n                          fn: function(text) {\n                            return [\n                              _c(\"span\", { staticClass: \"quantity-text\" }, [\n                                _vm._v(_vm._s(text))\n                              ])\n                            ]\n                          }\n                        }\n                      ],\n                      null,\n                      false,\n                      4294057056\n                    )\n                  })\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm.record.imInventoryType === \"2\"\n            ? _c(\"div\", { staticClass: \"detail-section\" }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"section-title\" },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"calculator\" } }),\n                    _c(\"span\", [_vm._v(\"产品成本（根据物料清单）\")])\n                  ],\n                  1\n                ),\n                _vm.productCostLoading\n                  ? _c(\n                      \"div\",\n                      { staticClass: \"loading-container\" },\n                      [_c(\"a-spin\", { attrs: { size: \"large\" } })],\n                      1\n                    )\n                  : _vm.productCost.materialDetails &&\n                    _vm.productCost.materialDetails.length > 0\n                  ? _c(\n                      \"div\",\n                      [\n                        _c(\"div\", { staticClass: \"cost-summary\" }, [\n                          _c(\"div\", { staticClass: \"cost-item\" }, [\n                            _c(\"span\", { staticClass: \"cost-label\" }, [\n                              _vm._v(\"物料清单：\")\n                            ]),\n                            _c(\"span\", { staticClass: \"cost-value\" }, [\n                              _vm._v(\n                                _vm._s(\n                                  _vm.productCost.materialBillName || \"未命名\"\n                                )\n                              )\n                            ])\n                          ]),\n                          _c(\"div\", { staticClass: \"cost-item\" }, [\n                            _c(\"span\", { staticClass: \"cost-label\" }, [\n                              _vm._v(\"总成本：\")\n                            ]),\n                            _c(\"span\", { staticClass: \"cost-value price\" }, [\n                              _vm._v(\n                                \"¥\" +\n                                  _vm._s(\n                                    _vm.formatNumber(_vm.productCost.totalCost)\n                                  )\n                              )\n                            ])\n                          ])\n                        ]),\n                        _c(\"a-table\", {\n                          attrs: {\n                            columns: _vm.materialCostColumns,\n                            dataSource: _vm.productCost.materialDetails,\n                            pagination: false,\n                            size: \"small\",\n                            locale: { emptyText: \"暂无物料成本数据\" },\n                            scroll: { y: 150 }\n                          },\n                          scopedSlots: _vm._u(\n                            [\n                              {\n                                key: \"cost\",\n                                fn: function(text) {\n                                  return [\n                                    _c(\"span\", { staticClass: \"price-text\" }, [\n                                      _vm._v(\n                                        \"¥\" + _vm._s(_vm.formatNumber(text))\n                                      )\n                                    ])\n                                  ]\n                                }\n                              },\n                              {\n                                key: \"unitPrice\",\n                                fn: function(text) {\n                                  return [\n                                    _c(\"span\", { staticClass: \"price-text\" }, [\n                                      _vm._v(\n                                        \"¥\" + _vm._s(_vm.formatNumber(text))\n                                      )\n                                    ])\n                                  ]\n                                }\n                              }\n                            ],\n                            null,\n                            false,\n                            305623977\n                          )\n                        })\n                      ],\n                      1\n                    )\n                  : _c(\n                      \"div\",\n                      { staticClass: \"empty-state\" },\n                      [\n                        _c(\"a-empty\", {\n                          attrs: { description: \"暂无物料清单成本数据\" }\n                        })\n                      ],\n                      1\n                    )\n              ])\n            : _vm._e(),\n          _vm.record.imInventoryType === \"2\"\n            ? _c(\n                \"div\",\n                { staticClass: \"detail-section\" },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"section-title\" },\n                    [\n                      _c(\"a-icon\", { attrs: { type: \"tool\" } }),\n                      _c(\"span\", [_vm._v(\"生产批次详情\")])\n                    ],\n                    1\n                  ),\n                  _c(\"a-table\", {\n                    attrs: {\n                      columns: _vm.productionColumns,\n                      dataSource: _vm.productionDetails,\n                      pagination: false,\n                      size: \"small\",\n                      loading: _vm.detailLoading,\n                      locale: { emptyText: \"暂无生产记录数据\" },\n                      scroll: { y: 250 }\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"cost\",\n                          fn: function(text) {\n                            return [\n                              _c(\"span\", { staticClass: \"price-text\" }, [\n                                _vm._v(\"¥\" + _vm._s(_vm.formatNumber(text)))\n                              ])\n                            ]\n                          }\n                        },\n                        {\n                          key: \"quantity\",\n                          fn: function(text) {\n                            return [\n                              _c(\"span\", { staticClass: \"quantity-text\" }, [\n                                _vm._v(_vm._s(text))\n                              ])\n                            ]\n                          }\n                        },\n                        {\n                          key: \"priority\",\n                          fn: function(text, record) {\n                            return [\n                              _c(\n                                \"a-checkbox\",\n                                {\n                                  attrs: { checked: record.prioritySale },\n                                  on: {\n                                    change: function(e) {\n                                      return _vm.handlePriorityChange(\n                                        record,\n                                        e.target.checked\n                                      )\n                                    }\n                                  }\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n              优先出售此批次\\n            \"\n                                  )\n                                ]\n                              )\n                            ]\n                          }\n                        }\n                      ],\n                      null,\n                      false,\n                      520401024\n                    )\n                  })\n                ],\n                1\n              )\n            : _vm._e()\n        ])\n      ])\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}