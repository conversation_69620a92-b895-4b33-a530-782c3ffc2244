<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <!-- 产品选择卡片 -->
        <a-card class="custom-card product-card" :bordered="false">
          <div class="card-title">
            <a-icon type="shop" />
            <span>选择产品</span>
          </div>
          <a-form-model-item prop="productId">
            <!-- 产品搜索选择器 -->
            <div class="product-search-wrapper">
              <a-auto-complete
                v-model="model.productName"
                placeholder="请输入产品名称搜索"
                :dropdownMatchSelectWidth="false"
                @search="handleProductSearch"
                @select="handleProductSelect"
                class="custom-select"
                size="large"
                :filterOption="false"
                :defaultActiveFirstOption="false"
              >
                <template slot="dataSource">
                  <a-select-option v-if="loadingProducts" disabled key="loading">
                    <a-icon type="loading" spin />
                    加载中...
                  </a-select-option>
                  <a-select-option
                    v-for="product in filteredProducts"
                    :key="product.id"
                    :value="product.id"
                  >
                    {{ product.name }}
                  </a-select-option>
                </template>
                <a-input>
                  <a-icon slot="suffix" type="search" @click="loadProducts" />
                </a-input>
              </a-auto-complete>
            </div>
            
            <!-- 隐藏的产品ID字段，用于表单提交 -->
            <a-input type="hidden" v-model="model.productId" />
          </a-form-model-item>
          
          <div v-if="model.productId && model.productName" class="selected-product-info">
            <a-alert type="success" show-icon>
              <template slot="message">
                已选择产品: <strong>{{ model.productName }}</strong>
              </template>
            </a-alert>
          </div>
        </a-card>

        <!-- 物料清单名称卡片 -->
        <a-card class="custom-card name-card" :bordered="false">
          <div class="card-title">
            <a-icon type="edit" />
            <span>物料清单名称</span>
          </div>
          <a-form-model-item prop="materialBillName">
            <a-input
              v-model="model.materialBillName"
              placeholder="请输入物料清单名称"
              size="large"
              :maxLength="50"
              show-count
            >
              <a-icon slot="prefix" type="file-text" />
            </a-input>
          </a-form-model-item>
        </a-card>

        <!-- 物料清单卡片 -->
        <a-card class="custom-card material-card" :bordered="false">
          <div class="card-header">
            <div class="card-title">
              <a-icon type="database" />
              <span>物料清单</span>
            </div>
            <a-badge :count="materialList.length" :overflowCount="99" class="material-count-badge" />
          </div>

          <div class="material-search-container">
            <a-input-search
              placeholder="输入物料名称或单位进行模糊搜索"
              v-model="searchMaterialName"
              @search="searchMaterial"
              enter-button
              class="search-input"
              size="large"
            >
              <a-icon slot="prefix" type="search" />
            </a-input-search>
            
            <a-tooltip title="从物料库中添加物料">
              <a-button type="primary" icon="plus" @click="searchMaterial" size="large">
                添加物料
              </a-button>
            </a-tooltip>
          </div>

          <div class="material-list-container">
            <a-empty v-if="materialList.length === 0" description="暂无物料，请搜索并添加">
              <a-button type="primary" @click="searchMaterial">添加物料</a-button>
            </a-empty>
            <a-table
              v-else
              :columns="materialColumns"
              :dataSource="materialList"
              :pagination="false"
              :rowKey="record => record.id || record.tempId"
              size="middle"
              :scroll="{ x: 800 }"
              :bordered="true"
              :rowClassName="'material-table-row'"
            >
              <!-- 物料名称列 -->
              <template slot="materialName" slot-scope="text, record, index">
                <div class="material-name-cell">
                  <template v-if="record.tempId">
                    <a-input
                      v-model="materialList[index].materialName"
                      placeholder="请输入物料名称"
                      @change="updateMaterialJson"
                    />
                  </template>
                  <template v-else>
                    <span class="material-name">{{ text }}</span>
                    <a-tag v-if="record.imSpecs" color="blue" class="material-spec-tag">{{ record.imSpecs }}</a-tag>
                  </template>
                </div>
              </template>

              <!-- 单位列 -->
              <template slot="materialUnit" slot-scope="text, record, index">
                <div class="unit-cell">
                  <template v-if="record.tempId">
                    <a-input
                      v-model="materialList[index].materialUnit"
                      placeholder="请输入单位"
                      @change="updateMaterialJson"
                    />
                  </template>
                  <template v-else>
                    <a-tag color="cyan">{{ text || '个' }}</a-tag>
                  </template>
                </div>
              </template>

              <!-- 数量列 -->
              <template slot="materialCount" slot-scope="text, record, index">
                <div class="count-cell">
                  <a-input-number
                    v-model="materialList[index].materialCount"
                    :min="1"
                    @change="value => handleCountChange(value, index)"
                    class="count-input"
                    size="default"
                  />
                  <span class="unit-text">{{ record.materialSpecifications || '个' }}</span>
                </div>
              </template>

              <!-- 操作列 -->
              <template slot="action" slot-scope="text, record, index">
                <div class="action-cell">
                  <a-button type="danger" size="small" @click="removeMaterial(index)" class="delete-btn">
                    <a-icon type="delete" />
                  </a-button>
                </div>
              </template>
            </a-table>
            
            <div v-if="materialList.length > 0" class="material-summary">
              <div class="summary-item">
                <span class="summary-label">总物料数:</span>
                <span class="summary-value">{{ materialList.length }}</span>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 物料清单设置卡片 -->
        <a-card class="custom-card status-card" :bordered="false">
          <div class="card-title">
            <a-icon type="setting" />
            <span>物料清单设置</span>
          </div>
          <a-form-model-item label="物料清单状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="materialType">
            <div class="status-switch-container">
              <a-switch
                checkedChildren="启用"
                unCheckedChildren="禁用"
                @change="onChose"
                v-model="visibleCheck"
                class="custom-switch"
                size="large"
              />
              <span :class="['status-text', visibleCheck ? 'status-enabled' : 'status-disabled']">
                {{ visibleCheck ? '清单已启用' : '清单已禁用' }}
              </span>
            </div>
          </a-form-model-item>

          <a-form-model-item label="库存用完处理策略" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stockEmptyStrategy">
            <a-checkbox-group v-model="stockEmptyStrategyArray" class="strategy-checkbox-group">
              <a-checkbox value="1" class="strategy-checkbox" style="margin-left: 7px;">
                <div class="checkbox-content">
                  <div class="checkbox-title">自动禁用此物料清单</div>
                  <div class="checkbox-desc">当前采购批次库存用完时，自动禁用此物料清单</div>
                </div>
              </a-checkbox>
              <a-checkbox value="2" class="strategy-checkbox">
                <div class="checkbox-content">
                  <div class="checkbox-title">自动切换为同成本物料</div>
                  <div class="checkbox-desc">当前采购批次库存用完时，自动切换为同成本的其他物料</div>
                </div>
              </a-checkbox>
            </a-checkbox-group>
          </a-form-model-item>
        </a-card>
        
        <!-- 提交按钮区域 -->
        <div class="form-actions" v-if="!formDisabled">
          <a-button @click="resetForm" :disabled="confirmLoading">
            <a-icon type="undo" />重置
          </a-button>
          <a-button type="primary" @click="submitForm" :loading="confirmLoading" :disabled="materialList.length === 0">
            <a-icon type="check" />保存
          </a-button>
        </div>
      </a-form-model>
    </j-form-container>

    <!-- 物料搜索弹窗 -->
    <a-modal
      title="搜索物料库存"
      :visible="inventoryModalVisible"
      :width="800"
      @cancel="closeInventoryModal"
      :footer="null"
      :destroyOnClose="true"
      :maskClosable="false"
      class="inventory-modal"
    >
      <div class="modal-header">
        <a-icon type="database" theme="filled" />
        <span>添加物料到清单</span>
      </div>
      
      <div class="inventory-search-container">
        <a-input-search
          placeholder="输入物料名称或单位进行模糊搜索"
          v-model="inventorySearchName"
          @search="searchInventory"
          enter-button
          class="search-input"
          size="large"
          @pressEnter="searchInventory"
        >
          <a-icon slot="prefix" type="search" />
        </a-input-search>
      </div>

      <a-spin :spinning="loadingInventory">
        <div class="inventory-list-container">
          <a-empty v-if="inventoryList.length === 0 && !loadingInventory" description="未找到物料，请尝试其他关键词" />
          <a-table
            v-else
            :columns="inventoryColumns"
            :dataSource="inventoryList"
            :pagination="{ pageSize: 5, showQuickJumper: true, showSizeChanger: true }"
            :rowKey="record => record.id"
            size="middle"
            :bordered="true"
            :rowClassName="'inventory-table-row'"
          >
            <template slot="imItemName" slot-scope="text">
              <span class="inventory-name">{{ text }}</span>
            </template>

            <template slot="imUnit" slot-scope="text">
              <a-tag color="blue">{{ text || '个' }}</a-tag>
            </template>

            <template slot="imQuantity" slot-scope="text">
              <span class="quantity-value">{{ text }}</span>
            </template>

            <template slot="purchaseBatch" slot-scope="text">
              <a-tag color="green">{{ text || '批次-001' }}</a-tag>
            </template>

            <template slot="purchaseTime" slot-scope="text">
              <span class="purchase-time">{{ text || '2025-01-01' }}</span>
            </template>

            <template slot="purchaseUnitCost" slot-scope="text">
              <span class="unit-cost">¥{{ text || '0.00' }}</span>
            </template>

            <template slot="action" slot-scope="text, record">
              <a-button type="primary" size="small" @click="selectInventoryItem(record)" class="select-btn">
                <a-icon type="plus" />添加
              </a-button>
            </template>
          </a-table>
        </div>
      </a-spin>
      
      <div class="modal-footer">
        <a-button @click="closeInventoryModal">关闭</a-button>
      </div>
    </a-modal>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'
import moment from 'moment'
export default {
  name: 'MaterialBillForm',
  components: {
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data () {
    return {
      products: [],
      filteredProducts: [], // 添加过滤后的产品列表
      loadingProducts: false,
      visibleCheck: true,
      materialList: [],
      searchMaterialName: '',
      inventoryModalVisible: false,
      inventorySearchName: '',
      inventoryList: [],
      loadingInventory: false,
      tempIdCounter: 0,
      model: {
        productId: '',
        productName: '',
        materialBillName: '',
        materialType: 0,
        materialJson: '',
        materialApprovalStatus: '',
        stockEmptyStrategy: '', // 存储逗号分隔的策略值
        imItemPrice : 0
      },
      stockEmptyStrategyArray: [], // 用于多选框的数组
      materialColumns: [
        {
          title: '物料名称',
          dataIndex: 'materialName',
          key: 'materialName',
          scopedSlots: { customRender: 'materialName' },
          width: 220,
          ellipsis: true
        },
        {
          title: '单位',
          dataIndex: 'materialUnit',
          key: 'materialUnit',
          scopedSlots: { customRender: 'materialUnit' },
          width: 120
        },
        {
          title: '数量',
          key: 'materialCount',
          width: 150,
          scopedSlots: { customRender: 'materialCount' }
        },
        {
          title: '操作',
          key: 'action',
          width: 80,
          fixed: 'right',
          scopedSlots: { customRender: 'action' }
        }
      ],
      inventoryColumns: [
        {
          title: '物料名称',
          dataIndex: 'imItemName',
          key: 'imItemName',
          ellipsis: true,
          scopedSlots: { customRender: 'imItemName' }
        },
        {
          title: '单位',
          dataIndex: 'imUnit',
          key: 'imUnit',
          scopedSlots: { customRender: 'imUnit' }
        },
        {
          title: '库存数量',
          dataIndex: 'imInventoryQuantity',
          key: 'imInventoryQuantity',
          scopedSlots: { customRender: 'imQuantity' }
        },
        {
          title: '采购批次',
          dataIndex: 'purchaseBatch',
          key: 'purchaseBatch',
          scopedSlots: { customRender: 'purchaseBatch' }
        },
        {
          title: '采购时间',
          dataIndex: 'purchaseTime',
          key: 'purchaseTime',
          scopedSlots: { customRender: 'purchaseTime' }
        },
        {
          title: '采购单位成本',
          dataIndex: 'purchaseUnitCost',
          key: 'purchaseUnitCost',
          scopedSlots: { customRender: 'purchaseUnitCost' }
        },
        {
          title: '操作',
          key: 'action',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' }
        }
      ],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        productId: [
          { required: true, message: '请选择产品' }
        ],
        materialBillName: [
          { required: true, message: '请输入物料清单名称' },
          { max: 50, message: '物料清单名称不能超过50个字符' }
        ]
      },
      url: {
        add: "/admin/materialBill/add",
        edit: "/admin/materialBill/edit",
        queryById: "/admin/materialBill/queryById",
        inventoryList: "/admin/inventoryManagement/list",
        searchMaterials: "/admin/inventoryManagement/searchMaterials",
        queryProductById: "/sys/sysDepart/queryProductById"
      }
    }
  },
  mounted() {
    this.loadProducts()
    // 处理路由参数
    const routeProductId = this.$route.query.productId
    if (routeProductId) {
      this.model.productId = routeProductId
      this.handleProductChange(routeProductId)
    }
  },
  watch: {
    materialList: {
      handler(newVal) {
        // 当物料列表变化时，更新materialJson
        this.updateMaterialJson()
      },
      deep: true
    }
  },
  computed: {
    formDisabled(){
      return this.disabled
    },
  },
  watch: {
    // 监听多选框数组变化，同步到字符串字段
    stockEmptyStrategyArray: {
      handler(newVal) {
        this.model.stockEmptyStrategy = newVal.join(',');
      },
      deep: true
    },
    // 监听字符串字段变化，同步到多选框数组
    'model.stockEmptyStrategy': {
      handler(newVal) {
        if (newVal && typeof newVal === 'string') {
          this.stockEmptyStrategyArray = newVal.split(',').filter(item => item.trim());
        } else {
          this.stockEmptyStrategyArray = [];
        }
      },
      immediate: true
    }
  },
  created () {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model));
    // 设置默认值为启用
    if (this.model.materialType === undefined) {
      this.model.materialType = 0;
    }
  },
  methods: {
    // 重置表单
    resetForm() {
      this.model = JSON.parse(JSON.stringify(this.modelDefault));
      this.materialList = [];
      this.visibleCheck = true;
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    
    // 更新物料列表
    updateMaterialList() {
      try {
        this.model.materialList = this.materialList.map((item, index) => ({
          id: item.id || item.tempId,
          materialName: item.materialName || '',
          materialUnit: item.materialUnit || '',
          materialCount: String(item.materialCount || 1), // 改为 materialCount
          imItemPrice: item.imItemPrice || 0,
          companyId: item.companyId || '',
          createBy: item.createBy || '',
          createTime: item.createTime || '',
          materialBillId: this.model.id || '',
          productionOrderId: item.productionOrderId || '',
          sysOrgCode: item.sysOrgCode || '',
          updateBy: item.updateBy || '',
          updateTime: item.updateTime || '',
        }));

        console.log('更新后的 materialList:', this.model.materialList);
      } catch (error) {
        console.error('更新 materialList 失败:', error);
      }
    },
    
    onChose(checked) {
      if (checked) {
        this.model.materialType = 0;  // 0表示启用
        this.visibleCheck = true;
      } else {
        this.model.materialType = 1;  // 1表示禁用
        this.visibleCheck = false;
      }
    },

    async loadProducts() {
      try {
        this.loadingProducts = true
        const res = await getAction('/sys/sysDepart/queryProductsByCurrentEnterprise', {})
        if (res.success) {
          this.products = (res.result || [])
            .filter(p => p.id && p.productName)
            .map(p => ({
              id: p.id,
              name: p.productName
            }))
          
          // 初始设置过滤后的产品列表与产品列表相同
          this.filteredProducts = [...this.products]
          
          return this.products
        } else {
          this.$message.error('产品加载失败：' + (res.message || '请求异常'))
          this.products = []
          this.filteredProducts = []
          return []
        }
      } catch (error) {
        console.error('产品加载失败:', error)
        this.$message.error('产品加载失败：' + (error.message || '未知错误'))
        this.products = []
        this.filteredProducts = []
        return []
      } finally {
        this.loadingProducts = false
      }
    },

    add () {
      this.edit(this.modelDefault);
    },

    // 产品选择变更
    handleProductChange(productId) {
      if (!productId) {
        this.model.productId = '';
        this.model.productName = '';
        return;
      }
      
      this.model.productId = productId;
      
      // 查找选中的产品名称并设置
      const selectedProduct = this.products.find(p => p.id === productId);
      if (selectedProduct) {
        this.model.productName = selectedProduct.name;
        console.log('产品已选择:', selectedProduct.name);
      } else {
        // 如果在本地列表中找不到，尝试从服务器获取
        getAction(this.url.queryProductById, { id: productId })
          .then(res => {
            if (res.success && res.result) {
              this.model.productName = res.result.productName || res.result.name;
              console.log('从服务器获取产品名称:', this.model.productName);
            }
          })
          .catch(err => {
            console.error('获取产品详情失败:', err);
          });
      }
    },

    // 处理产品搜索
    handleProductSearch(searchText) {
      this.filteredProducts = this.products.filter(product =>
        product.name.toLowerCase().includes(searchText.toLowerCase())
      );
    },

    // 处理产品选择
    handleProductSelect(value) {
      this.model.productId = value;
      this.model.productName = this.products.find(p => p.id === value).name;
      this.filteredProducts = []; // 清空过滤列表
    },

    // 打开物料搜索弹窗
    searchMaterial() {
      this.inventoryModalVisible = true
      this.inventorySearchName = this.searchMaterialName
      this.searchInventory()
    },

    // 关闭物料搜索弹窗
    closeInventoryModal() {
      this.inventoryModalVisible = false
      this.inventoryList = []
    },

    // 搜索库存物料
    async searchInventory() {
      if (!this.inventorySearchName.trim()) {
        this.$message.warning('请输入搜索关键词')
        return
      }

      try {
        this.loadingInventory = true
        // 使用新的模糊搜索接口
        const res = await getAction(this.url.searchMaterials, {
          keyword: this.inventorySearchName
        })

        if (res.success) {
          this.inventoryList = res.result || []
          if (this.inventoryList.length === 0) {
            this.$message.info('未找到匹配的物料，请尝试其他关键词')
          }
        } else {
          this.$message.error('库存查询失败：' + (res.message || '请求异常'))
          this.inventoryList = []
        }
      } catch (error) {
        console.error('库存查询失败:', error)
        this.$message.error('库存查询失败：' + (error.message || '未知错误'))
        this.inventoryList = []
      } finally {
        this.loadingInventory = false
      }
    },

    // 选择库存物料
    // 选择库存物料
    selectInventoryItem(item) {
      const existingIndex = this.materialList.findIndex(m => m.id === item.id);

      if (existingIndex >= 0) {
        this.$message.warning('该物料已添加到清单中');
        return;
      }

      const newMaterial = {
        id: item.id,
        materialName: item.imItemName,
        materialSpecifications: item.imItemUnit || '个', // 将单位存储到规格字段
        materialCount: 1,
        imItemPrice: item.imItemPrice || 0, // 新增：传递物料价格
        companyId: '', // 默认公司 ID
        createBy: this.model.createBy || '', // 创建人
        createTime: moment().format('YYYY-MM-DD HH:mm:ss'), // 创建时间
        materialBillId: this.model.id || '', // 物料清单 ID
        productionOrderId: '', // 生产订单 ID
        sysOrgCode: this.model.sysOrgCode || '', // 所属部门
        updateBy: '', // 更新人
        updateTime: '', // 更新时间
      };

      this.materialList.push(newMaterial);
      this.updateMaterialList();
      this.$message.success(`已添加物料: ${item.imItemName}`);
      this.closeInventoryModal();
    },

    // 手动添加物料
    // 手动添加物料
    addMaterial() {
      const tempId = 'temp_' + (++this.tempIdCounter);
      this.materialList.push({
        tempId,
        materialName: '', // 空值
        materialUnit: '', // 空值
        materialCount: 1, // 默认数量
        imItemPrice: 0, // 新增：默认物料价格为 0
        companyId: '', // 默认公司 ID
        createBy: '', // 默认创建人
        createTime: moment().format('YYYY-MM-DD HH:mm:ss'), // 默认创建时间
        materialBillId: this.model.id || '', // 物料清单 ID
        productionOrderId: '', // 默认生产订单 ID
        sysOrgCode: '', // 默认所属部门
        updateBy: '', // 默认更新人
        updateTime: '', // 默认更新时间
      });

      this.updateMaterialList();
    },

    // 删除物料
    removeMaterial(index) {
      this.materialList.splice(index, 1); // 从数组中移除
      this.updateMaterialList(); // 更新 materialList
    },

    // 更新数量
    handleCountChange(value, index) {
      if (!this.materialList[index]) return;
      this.materialList[index].materialCount = value; // 更新数量
      this.updateMaterialList(); // 更新 materialList
    },

    // 更新materialJson
    updateMaterialJson() {
      try {
        const jsonData = this.materialList.map(item => ({
          materialName: item.materialName,
          materialUnit: item.materialUnit,
          materialCount: String(item.materialCount),
          imItemPrice: item.imItemPrice || 0, // 新增：传递物料价格
          id: item.id || item.tempId,
          // 保留其他可能需要的字段
          ...(item.originalData ? { originalData: item.originalData } : {})
        }))

        this.model.materialJson = JSON.stringify(jsonData)
      } catch (error) {
        console.error('生成物料JSON失败:', error)
      }
    },
    // 从JSON恢复物料列表
    restoreMaterialListFromJson() {
      try {
        if (!Array.isArray(this.model.materialList)) {
          this.materialList = [];
          return;
        }

        this.materialList = this.model.materialList.map(item => ({
          id: item.id || ('temp_' + (++this.tempIdCounter)),
          materialName: item.materialName || '',
          materialSpecifications: item.materialSpecifications || '',
          materialCount: parseInt(item.materialCount) || 1, // 改为从 materialCount 获取
          imItemPrice: item.imItemPrice || 0,
          companyId: item.companyId || '',
          createBy: item.createBy || '',
          createTime: item.createTime || '',
          materialBillId: item.materialBillId || '',
          productionOrderId: item.productionOrderId || '',
          sysOrgCode: item.sysOrgCode || '',
          updateBy: item.updateBy || '',
          updateTime: item.updateTime || '',
        }));
      } catch (error) {
        console.error('恢复物料列表失败:', error);
        this.materialList = [];
      }
    },

    edit (record) {
      this.model = Object.assign({}, record);
      this.visibleCheck = this.model.materialType === 0 || this.model.materialType === "0";
      this.visible = true; // 设置visible属性为true
      
      // 打印当前编辑记录，方便排查问题
      console.log('编辑记录:', record);
      
      // 确保productId正确设置，防止undefined或null值
      if (!this.model.productId) {
        this.model.productId = '';
        this.model.productName = '';
      }
      
      // 当存在productId但没有productName时，从加载的产品列表中查找产品名称
      if (this.model.productId && !this.model.productName) {
        // 如果产品列表已加载
        if (this.products && this.products.length > 0) {
          const selectedProduct = this.products.find(p => p.id === this.model.productId);
          if (selectedProduct) {
            this.model.productName = selectedProduct.name;
            console.log('从产品列表中获取到产品名称:', this.model.productName);
          } else {
            // 如果在本地找不到，从服务器查询
            this.queryProductNameById(this.model.productId);
          }
        } else {
          // 如果产品列表尚未加载，先加载产品列表
          this.loadProducts().then(() => {
            const selectedProduct = this.products.find(p => p.id === this.model.productId);
            if (selectedProduct) {
              this.model.productName = selectedProduct.name;
              console.log('异步加载产品名称成功:', this.model.productName);
            } else {
              // 如果仍然找不到，尝试单独查询这个产品
              this.queryProductNameById(this.model.productId);
            }
          });
        }
      }
      
      // 恢复物料列表
      if (Array.isArray(this.model.materialList) && this.model.materialList.length > 0) {
        // 直接使用后端返回的物料列表
        this.materialList = this.model.materialList.map(item => ({
          id: item.id,
          materialName: item.materialName || '',
          materialSpecifications: item.materialSpecifications || '',
          materialCount: parseInt(item.materialCount) || 1,
          imItemPrice: item.imItemPrice || 0,
          companyId: item.companyId || '',
          createBy: item.createBy || '',
          createTime: item.createTime || '',
          materialBillId: item.materialBillId || '',
          productionOrderId: item.productionOrderId || '',
          sysOrgCode: item.sysOrgCode || '',
          updateBy: item.updateBy || '',
          updateTime: item.updateTime || '',
        }));
      } else {
        // 如果没有物料列表数据，尝试从materialJson解析
        this.restoreMaterialListFromJson();
      }
      
      console.log('加载的物料列表:', this.materialList);
    },
    
    // 通过产品ID查询产品名称
    queryProductNameById(productId) {
      if (!productId) return;
      
      getAction(this.url.queryProductById, { id: productId })
        .then(res => {
          if (res.success && res.result) {
            this.model.productName = res.result.productName || res.result.name;
            console.log('通过API查询获取产品名称:', this.model.productName);
          } else {
            console.warn('查询产品详情返回空数据');
          }
        })
        .catch(err => {
          console.error('查询产品详情失败:', err);
        });
    },

    submitForm() {
      const invalidItem = this.materialList.find(item =>
        !moment(item.createTime, 'YYYY-MM-DD HH:mm:ss', true).isValid()
      );

      if (invalidItem) {
        this.$message.error(`物料 ${invalidItem.materialName} 时间格式错误`);
        return;
      }

      // 检查是否有物料
      if (this.materialList.length === 0) {
        this.$message.warning('请至少添加一种物料');
        return;
      }

      // 更新最终的 materialList
      this.updateMaterialList();

      // 提取 materialList 中的 imItemPrice
      const imItemPrice = this.materialList.reduce((total, item) => {
        return total + (item.imItemPrice || 0) * (item.materialCount || 1); // 改为 materialCount
      }, 0);

      // 构造最终的请求参数
      const requestData = {
        ...this.model,
        imItemPrice: imItemPrice,
        materialList: this.materialList,
      };

      console.log('最终请求参数:', requestData);

      // 验证表单
      this.$refs.form.validate(valid => {
        if (valid) {
          this.confirmLoading = true;
          const httpurl = this.model.id ? this.url.edit : this.url.add;
          const method = this.model.id ? 'put' : 'post';

          httpAction(httpurl, requestData, method).then(res => {
            if (res.success) {
              this.$message.success(res.message);
              this.$emit('ok');
            } else {
              this.$message.warning(res.message);
            }
          }).finally(() => {
            this.confirmLoading = false;
          });
        }
      });
    },
  }
}
</script>

<style lang="less" scoped>
// 变量定义
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;
@border-radius-base: 8px;
@card-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
@transition-duration: 0.3s;

.ant-input {
  width: 100%;
  max-width: 180px;
}

// 自定义卡片样式
.custom-card {
  margin-bottom: 24px;
  border-radius: @border-radius-base;
  box-shadow: @card-shadow;
  overflow: hidden;
  transition: all @transition-duration;

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  &.product-card {
    background: linear-gradient(to right, #f0f7ff, #ffffff);
    border-left: 4px solid #1890ff;
  }

  &.name-card {
    background: linear-gradient(to right, #f6ffed, #ffffff);
    border-left: 4px solid #52c41a;
  }

  &.material-card {
    background: linear-gradient(to right, #f0fff0, #ffffff);
    border-left: 4px solid #52c41a;
  }

  &.status-card {
    background: linear-gradient(to right, #fffbe6, #ffffff);
    border-left: 4px solid #faad14;
  }
}

// 卡片标题
.card-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 20px;
  color: #1890ff;
  display: flex;
  align-items: center;

  .anticon {
    margin-right: 10px;
    font-size: 18px;
  }
}

// 卡片头部
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.material-count-badge {
  margin-right: 16px;
}

// 选中产品信息
.selected-product-info {
  margin-top: 16px;
}

// 物料搜索容器
.material-search-container {
  display: flex;
  margin-bottom: 16px;
  gap: 12px;
  align-items: center;

  .search-input {
    flex: 1;
  }
}

// 物料列表容器
.material-list-container {
  margin-top: 16px;
  border: 1px solid #f0f0f0;
  border-radius: @border-radius-base;
  padding: 16px;
  background-color: #fafafa;
  
  .count-input {
    width: 90px;
  }
}

// 物料名称单元格
.material-name-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .material-name {
    font-weight: 500;
  }

  .material-spec-tag {
    margin-right: 0;
    border-radius: 12px;
  }
}

// 单位单元格
.unit-cell {
  .ant-tag {
    padding: 0 8px;
    border-radius: 12px;
  }
}

// 数量单元格
.count-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .unit-text {
    color: rgba(0, 0, 0, 0.45);
  }
}

// 操作单元格
.action-cell {
  .delete-btn {
    border-radius: 50%;
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 物料汇总
.material-summary {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #e8e8e8;
  
  .summary-item {
    margin-left: 24px;
    
    .summary-label {
      color: rgba(0, 0, 0, 0.45);
      margin-right: 8px;
    }
    
    .summary-value {
      font-weight: 600;
      color: @primary-color;
    }
  }
}

// 状态开关容器
.status-switch-container {
  display: flex;
  align-items: center;

  .status-text {
    margin-left: 16px;
    font-size: 14px;

    &.status-enabled {
      color: @success-color;
    }

    &.status-disabled {
      color: @error-color;
    }
  }
}

// 策略多选框组
.strategy-checkbox-group {
  width: 100%;

  .strategy-checkbox {
    display: block;
    margin-bottom: 16px;
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: @border-radius-base;
    background-color: #fafafa;
    transition: all @transition-duration;

    &:hover {
      border-color: @primary-color;
      background-color: #f0f7ff;
    }

    &.ant-checkbox-wrapper-checked {
      border-color: @primary-color;
      background-color: #e6f7ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }

    .checkbox-content {
      margin-left: 8px;

      .checkbox-title {
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 4px;
      }

      .checkbox-desc {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        line-height: 1.4;
      }
    }
  }
}

// 自定义选择器
.custom-select {
  width: 100%;
}

// 自定义开关
.custom-switch {
  &.ant-switch-checked {
    background-color: @success-color;
  }
  
  &:not(.ant-switch-checked) {
    background-color: @error-color;
  }
}

// 表单操作区
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  
  .ant-btn {
    min-width: 100px;
  }
}

// 物料库存模态框
.inventory-modal {
  .modal-header {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
    color: @primary-color;
    display: flex;
    align-items: center;
    
    .anticon {
      margin-right: 10px;
      font-size: 20px;
    }
  }
  
  .inventory-search-container {
    margin-bottom: 20px;
    
    .search-input {
      width: 100%;
    }
  }
  
  .inventory-list-container {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;
    border: 1px solid #f0f0f0;
    border-radius: @border-radius-base;
    padding: 16px;
    background-color: #fafafa;
  }
  
  .inventory-name {
    font-weight: 500;
  }
  
  .quantity-value {
    font-weight: 600;
    color: @primary-color;
  }
  
  .select-btn {
    border-radius: 16px;
  }
  
  .modal-footer {
    margin-top: 16px;
    text-align: right;
  }
}

// 表格行样式
.material-table-row {
  transition: all @transition-duration;
  
  &:hover {
    background-color: #f0f7ff;
  }
}

.inventory-table-row {
  transition: all @transition-duration;
  
  &:hover {
    background-color: #f0f7ff;
  }
}

// 库存搜索容器
.inventory-search-container {
  margin-bottom: 16px;
  
  .search-input {
    width: 100%;
  }
}

.product-search-wrapper {
  position: relative;
  
  .ant-select-auto-complete {
    width: 100%;
  }

  .ant-input-suffix {
    cursor: pointer;
    color: @primary-color;
    
    &:hover {
      opacity: 0.8;
    }
  }
}

// 响应式调整
@media screen and (max-width: 576px) {
  .material-search-container {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .material-list-container {
    overflow-x: auto;
    padding: 12px 8px;
  }
  
  .form-actions {
    flex-direction: column;
    
    .ant-btn {
      width: 100%;
    }
  }
  
  .status-switch-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    
    .status-text {
      margin-left: 0;
    }
  }
}
</style>
