{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\InventoryManagementList.vue?vue&type=template&id=7ca16c2e&scoped=true&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\InventoryManagementList.vue", "mtime": 1753843740756}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753423171139}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["\n  <div class=\"inventory-management-container\">\n    <!-- 顶部统计卡片 -->\n    <div class=\"stats-cards\">\n      <a-row :gutter=\"[16, 16]\">\n        <a-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\">\n          <div class=\"stat-card total-items\">\n            <div class=\"stat-icon\">\n              <a-icon type=\"database\" />\n            </div>\n            <div class=\"stat-info\">\n              <div class=\"stat-title\">总物品数</div>\n              <div class=\"stat-value\">{{ totalItems }}</div>\n              <div class=\"stat-trend\">\n                <a-icon type=\"arrow-up\" />\n                <span>{{ trendData.items }}%</span>\n              </div>\n            </div>\n          </div>\n        </a-col>\n        <a-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\">\n          <div class=\"stat-card total-inventory\">\n            <div class=\"stat-icon\">\n              <a-icon type=\"inbox\" />\n            </div>\n            <div class=\"stat-info\">\n              <div class=\"stat-title\">库存总量</div>\n              <div class=\"stat-value\">{{ totalInventory }}</div>\n              <div class=\"stat-trend\">\n                <a-icon type=\"arrow-up\" />\n                <span>{{ trendData.inventory }}%</span>\n              </div>\n            </div>\n          </div>\n        </a-col>\n        <a-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\">\n          <div class=\"stat-card available-inventory\">\n            <div class=\"stat-icon\">\n              <a-icon type=\"check-circle\" />\n            </div>\n            <div class=\"stat-info\">\n              <div class=\"stat-title\">可用库存</div>\n              <div class=\"stat-value\">{{ availableInventory }}</div>\n              <div class=\"stat-trend\">\n                <a-icon type=\"arrow-down\" />\n                <span>{{ trendData.available }}%</span>\n              </div>\n            </div>\n          </div>\n        </a-col>\n        <a-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\">\n          <div class=\"stat-card total-cost\">\n            <div class=\"stat-icon\">\n              <a-icon type=\"dollar\" />\n            </div>\n            <div class=\"stat-info\">\n              <div class=\"stat-title\">总成本</div>\n              <div class=\"stat-value\">¥{{ formatNumber(totalCost) }}</div>\n              <div class=\"stat-trend\">\n                <a-icon type=\"arrow-up\" />\n                <span>{{ trendData.cost }}%</span>\n              </div>\n            </div>\n          </div>\n        </a-col>\n      </a-row>\n    </div>\n\n    <!-- 搜索与过滤栏 -->\n    <div class=\"search-filter-bar\">\n      <div class=\"search-area\">\n        <a-input-search\n          placeholder=\"搜索产品名称或物品名称\"\n          @search=\"onSearch\"\n          allow-clear\n          class=\"search-input\"\n        >\n          <a-icon slot=\"prefix\" type=\"search\" />\n        </a-input-search>\n        <a-button-group class=\"type-filter\">\n<!--          <a-tooltip title=\"全部\">-->\n<!--            <a-button-->\n<!--              :type=\"inventoryTypeFilter === 'all' ? 'primary' : 'default'\"-->\n<!--              @click=\"() => { inventoryTypeFilter = 'all'; loadData(1); }\"-->\n<!--            >-->\n<!--              全部-->\n<!--            </a-button>-->\n<!--          </a-tooltip>-->\n          <a-tooltip title=\"物品\">\n            <a-button\n              :type=\"inventoryTypeFilter === 'material' ? 'primary' : 'default'\"\n              @click=\"() => { inventoryTypeFilter = 'material'; loadData(1); }\"\n            >\n              物料\n            </a-button>\n          </a-tooltip>\n          <a-tooltip title=\"产品\">\n            <a-button\n              :type=\"inventoryTypeFilter === 'product' ? 'primary' : 'default'\"\n              @click=\"() => { inventoryTypeFilter = 'product'; loadData(1); }\"\n            >\n              产品\n            </a-button>\n          </a-tooltip>\n        </a-button-group>\n      </div>\n\n      <div class=\"filter-actions\">\n        <a-button-group class=\"view-toggle\">\n          <a-tooltip title=\"数据表格视图\">\n            <a-button :type=\"viewMode === 'table' ? 'primary' : 'default'\" @click=\"viewMode = 'table'\">\n              <a-icon type=\"table\" />\n            </a-button>\n          </a-tooltip>\n          <a-tooltip title=\"卡片视图\">\n            <a-button :type=\"viewMode === 'card' ? 'primary' : 'default'\" @click=\"viewMode = 'card'\">\n              <a-icon type=\"appstore\" />\n            </a-button>\n          </a-tooltip>\n        </a-button-group>\n      </div>\n    </div>\n\n    <!-- 选择信息提示 -->\n    <div v-if=\"selectedRowKeys.length > 0\" class=\"selection-info\">\n      <a-alert\n        :message=\"`已选择 ${selectedRowKeys.length} 项`\"\n        type=\"info\"\n        show-icon\n        closable\n        @close=\"selectedRowKeys = []\"\n      >\n        <template slot=\"action\">\n          <a-button size=\"small\" type=\"primary\" @click=\"handleBatchAdjust\" style=\"margin-right: 8px;\">\n            <a-icon type=\"tool\" /> 批量调整\n          </a-button>\n          <a-button size=\"small\" type=\"danger\" @click=\"batchDelete\">\n            批量删除\n          </a-button>\n        </template>\n      </a-alert>\n    </div>\n\n    <!-- 卡片视图 -->\n    <div v-if=\"viewMode === 'card'\" class=\"inventory-card-view\">\n      <a-spin :spinning=\"loading\">\n        <a-empty v-if=\"dataSource.length === 0\" description=\"暂无库存数据\" />\n        <a-row :gutter=\"[16, 16]\" v-else>\n          <a-col :xs=\"24\" :sm=\"12\" :md=\"12\" :lg=\"8\" :xl=\"6\" v-for=\"item in dataSource\" :key=\"item.id\">\n            <div\n              class=\"inventory-card\"\n              :class=\"{ 'selected-card': selectedRowKeys.includes(item.id) }\"\n              @click=\"toggleSelection(item.id)\"\n            >\n              <div class=\"card-header\">\n                <div class=\"item-name\" :title=\"item.imItemName\">{{ item.imItemName }}</div>\n                <a-checkbox\n                  :checked=\"selectedRowKeys.includes(item.id)\"\n                  @change=\"(e) => onItemSelect(item.id, e.target.checked)\"\n                  @click.stop\n                />\n              </div>\n\n              <div class=\"card-product\">\n                <span>产品: {{ item.imProductName }}</span>\n                <span class=\"item-id\">ID: {{ item.imItemId }}</span>\n              </div>\n\n              <div class=\"inventory-metrics\">\n                <div class=\"metric-item\">\n                  <div class=\"metric-value\">{{ item.imInventoryQuantity }}</div>\n                  <div class=\"metric-label\">库存数量</div>\n                  <a-progress\n                    :percent=\"100\"\n                    strokeColor=\"#52c41a\"\n                    size=\"small\"\n                    :showInfo=\"false\"\n                  />\n                </div>\n\n                <div class=\"metric-item\">\n                  <div class=\"metric-value\">{{ item.imAvailableStock }}</div>\n                  <div class=\"metric-label\">可用库存</div>\n                  <a-progress\n                    :percent=\"calculateAvailablePercent(item.imInventoryQuantity, item.imAvailableStock)\"\n                    :strokeColor=\"getStockColor(item.imInventoryQuantity, item.imAvailableStock)\"\n                    size=\"small\"\n                    :showInfo=\"false\"\n                  />\n                </div>\n              </div>\n\n              <div class=\"price-info\">\n                <div class=\"price-item\">\n                  <span class=\"price-label\">单价:</span>\n                  <span class=\"price-value\">¥{{ formatNumber(item.imItemPrice) }}</span>\n                  <span class=\"unit-label\">/ {{ item.imItemUnit }}</span>\n                </div>\n                <div class=\"price-item\">\n                  <span class=\"price-label\">总成本:</span>\n                  <span class=\"price-value\">¥{{ formatNumber(item.imTotalCost) }}</span>\n                </div>\n              </div>\n\n              <div class=\"card-actions\">\n                <a-button type=\"link\" @click.stop=\"handleDetail(item)\">\n                  <a-icon type=\"eye\" /> 详情\n                </a-button>\n                <!-- <a-button type=\"link\" class=\"edit-btn\" @click.stop=\"handleEdit(item)\">\n                  <a-icon type=\"edit\" /> 编辑\n                </a-button>\n                <a-button type=\"link\" class=\"delete-btn\" @click.stop=\"handleDelete(item.id)\">\n                  <a-icon type=\"delete\" /> 删除\n                </a-button> -->\n              </div>\n            </div>\n          </a-col>\n        </a-row>\n        <!-- 卡片分页 -->\n        <div class=\"card-pagination\">\n          <a-pagination\n            :current=\"ipagination.current\"\n            :pageSize=\"ipagination.pageSize\"\n            :total=\"ipagination.total\"\n            :pageSizeOptions=\"ipagination.pageSizeOptions\"\n            :showTotal=\"(total, range) => `${range[0]}-${range[1]} 共 ${total} 条`\"\n            showSizeChanger\n            showQuickJumper\n            @change=\"handleCardPageChange\"\n            @showSizeChange=\"handleCardShowSizeChange\"\n          />\n        </div>\n      </a-spin>\n    </div>\n\n    <!-- 表格视图 -->\n    <div v-else-if=\"viewMode === 'table'\" class=\"inventory-table-view\">\n      <a-spin :spinning=\"loading\">\n        <a-table\n          ref=\"table\"\n          size=\"middle\"\n          :scroll=\"{x:true}\"\n          :rowKey=\"record => record.id\"\n          :columns=\"getColumns()\"\n          :dataSource=\"dataSource\"\n          :pagination=\"ipagination\"\n          :rowSelection=\"rowSelection\"\n          class=\"custom-inventory-table\"\n          @change=\"handleTableChange\"\n        >\n          <template slot=\"imInventoryQuantitySlot\" slot-scope=\"text, record\">\n            <div class=\"inventory-cell clickable-cell\" @click=\"handleInventoryClick(record)\">\n              <span class=\"inventory-number\">{{ text }}</span>\n              <a-progress\n                :percent=\"calculateStockPercent(record.imInventoryQuantity, record.imAvailableStock)\"\n                size=\"small\"\n                strokeColor=\"#52c41a\"\n                :showInfo=\"false\"\n              />\n            </div>\n          </template>\n\n          <template slot=\"imAvailableStockSlot\" slot-scope=\"text, record\">\n            <div class=\"inventory-cell clickable-cell\" @click=\"handleAvailableStockClick(record)\">\n              <span class=\"inventory-number\">{{ text }}</span>\n              <a-progress\n                :percent=\"calculateAvailablePercent(record.imInventoryQuantity, record.imAvailableStock)\"\n                :strokeColor=\"getStockColor(record.imInventoryQuantity, record.imAvailableStock)\"\n                size=\"small\"\n                :showInfo=\"false\"\n              />\n            </div>\n          </template>\n\n          <template slot=\"priceSlot\" slot-scope=\"text\">\n            <span class=\"price-text\">¥{{ formatNumber(text) }}</span>\n          </template>\n\n          <template slot=\"action\" slot-scope=\"text, record\">\n            <div class=\"table-actions\">\n              <a-button type=\"link\" @click=\"handleDetail(record)\">\n                <a-icon type=\"eye\" /> 详情\n              </a-button>\n              <a-button v-if=\"record.imInventoryType === '2'\" type=\"link\" @click=\"handleSoldInventory(record)\">\n                <a-icon type=\"shopping-cart\" /> 已售出库存\n              </a-button>\n              <!-- <a-button type=\"link\" class=\"edit-btn\" @click=\"handleEdit(record)\">\n                <a-icon type=\"edit\" /> 编辑\n              </a-button>\n              <a-button type=\"link\" class=\"delete-btn\" @click=\"handleDelete(record.id)\">\n                <a-icon type=\"delete\" /> 删除\n              </a-button> -->\n            </div>\n          </template>\n        </a-table>\n      </a-spin>\n    </div>\n\n    <inventory-management-modal ref=\"modalForm\" @ok=\"modalFormOk\"></inventory-management-modal>\n    <inventory-management-detail ref=\"detailDrawer\" @close=\"closeDetail\" @edit=\"handleEdit\"></inventory-management-detail>\n    <batch-stock-adjust-modal ref=\"batchAdjustModal\" :selected-items=\"getSelectedItems()\" @success=\"handleBatchAdjustSuccess\"></batch-stock-adjust-modal>\n    <inventory-detail-modal ref=\"inventoryDetailModal\"></inventory-detail-modal>\n    <product-inventory-detail-modal ref=\"productInventoryDetailModal\"></product-inventory-detail-modal>\n    <sold-inventory-modal ref=\"soldInventoryModal\"></sold-inventory-modal>\n  </div>\n", null]}