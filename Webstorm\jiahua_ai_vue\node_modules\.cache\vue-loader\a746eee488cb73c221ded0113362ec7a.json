{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\material\\modules\\MaterialBillForm.vue?vue&type=style&index=0&id=03f1e51e&lang=less&scoped=true&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\material\\modules\\MaterialBillForm.vue", "mtime": 1753844909036}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\css-loader\\index.js", "mtime": 1753423166706}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753423171026}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753423169084}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1753423168554}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n// 变量定义\r\n@primary-color: #1890ff;\r\n@success-color: #52c41a;\r\n@warning-color: #faad14;\r\n@error-color: #f5222d;\r\n@border-radius-base: 8px;\r\n@card-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\r\n@transition-duration: 0.3s;\r\n\r\n.ant-input {\r\n  width: 100%;\r\n  max-width: 180px;\r\n}\r\n\r\n// 自定义卡片样式\r\n.custom-card {\r\n  margin-bottom: 24px;\r\n  border-radius: @border-radius-base;\r\n  box-shadow: @card-shadow;\r\n  overflow: hidden;\r\n  transition: all @transition-duration;\r\n\r\n  &:hover {\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\r\n  }\r\n\r\n  &.product-card {\r\n    background: linear-gradient(to right, #f0f7ff, #ffffff);\r\n    border-left: 4px solid #1890ff;\r\n  }\r\n\r\n  &.name-card {\r\n    background: linear-gradient(to right, #f6ffed, #ffffff);\r\n    border-left: 4px solid #52c41a;\r\n  }\r\n\r\n  &.material-card {\r\n    background: linear-gradient(to right, #f0fff0, #ffffff);\r\n    border-left: 4px solid #52c41a;\r\n  }\r\n\r\n  &.status-card {\r\n    background: linear-gradient(to right, #fffbe6, #ffffff);\r\n    border-left: 4px solid #faad14;\r\n  }\r\n}\r\n\r\n// 卡片标题\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  margin-bottom: 20px;\r\n  color: #1890ff;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .anticon {\r\n    margin-right: 10px;\r\n    font-size: 18px;\r\n  }\r\n}\r\n\r\n// 卡片头部\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.material-count-badge {\r\n  margin-right: 16px;\r\n}\r\n\r\n// 选中产品信息\r\n.selected-product-info {\r\n  margin-top: 16px;\r\n}\r\n\r\n// 物料搜索容器\r\n.material-search-container {\r\n  display: flex;\r\n  margin-bottom: 16px;\r\n  gap: 12px;\r\n  align-items: center;\r\n\r\n  .search-input {\r\n    flex: 1;\r\n  }\r\n}\r\n\r\n// 物料列表容器\r\n.material-list-container {\r\n  margin-top: 16px;\r\n  border: 1px solid #f0f0f0;\r\n  border-radius: @border-radius-base;\r\n  padding: 16px;\r\n  background-color: #fafafa;\r\n  \r\n  .count-input {\r\n    width: 90px;\r\n  }\r\n}\r\n\r\n// 物料名称单元格\r\n.material-name-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n\r\n  .material-name {\r\n    font-weight: 500;\r\n  }\r\n\r\n  .material-spec-tag {\r\n    margin-right: 0;\r\n    border-radius: 12px;\r\n  }\r\n}\r\n\r\n// 单位单元格\r\n.unit-cell {\r\n  .ant-tag {\r\n    padding: 0 8px;\r\n    border-radius: 12px;\r\n  }\r\n}\r\n\r\n// 数量单元格\r\n.count-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  \r\n  .unit-text {\r\n    color: rgba(0, 0, 0, 0.45);\r\n  }\r\n}\r\n\r\n// 操作单元格\r\n.action-cell {\r\n  .delete-btn {\r\n    border-radius: 50%;\r\n    width: 28px;\r\n    height: 28px;\r\n    padding: 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n// 物料汇总\r\n.material-summary {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 16px;\r\n  padding-top: 16px;\r\n  border-top: 1px dashed #e8e8e8;\r\n  \r\n  .summary-item {\r\n    margin-left: 24px;\r\n    \r\n    .summary-label {\r\n      color: rgba(0, 0, 0, 0.45);\r\n      margin-right: 8px;\r\n    }\r\n    \r\n    .summary-value {\r\n      font-weight: 600;\r\n      color: @primary-color;\r\n    }\r\n  }\r\n}\r\n\r\n// 状态开关容器\r\n.status-switch-container {\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .status-text {\r\n    margin-left: 16px;\r\n    font-size: 14px;\r\n\r\n    &.status-enabled {\r\n      color: @success-color;\r\n    }\r\n\r\n    &.status-disabled {\r\n      color: @error-color;\r\n    }\r\n  }\r\n}\r\n\r\n// 策略多选框组\r\n.strategy-checkbox-group {\r\n  width: 100%;\r\n\r\n  .strategy-checkbox {\r\n    display: block;\r\n    margin-bottom: 16px;\r\n    padding: 16px;\r\n    border: 1px solid #f0f0f0;\r\n    border-radius: @border-radius-base;\r\n    background-color: #fafafa;\r\n    transition: all @transition-duration;\r\n\r\n    &:hover {\r\n      border-color: @primary-color;\r\n      background-color: #f0f7ff;\r\n    }\r\n\r\n    &.ant-checkbox-wrapper-checked {\r\n      border-color: @primary-color;\r\n      background-color: #e6f7ff;\r\n      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);\r\n    }\r\n\r\n    .checkbox-content {\r\n      margin-left: 8px;\r\n\r\n      .checkbox-title {\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: rgba(0, 0, 0, 0.85);\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .checkbox-desc {\r\n        font-size: 12px;\r\n        color: rgba(0, 0, 0, 0.45);\r\n        line-height: 1.4;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 自定义选择器\r\n.custom-select {\r\n  width: 100%;\r\n}\r\n\r\n// 自定义开关\r\n.custom-switch {\r\n  &.ant-switch-checked {\r\n    background-color: @success-color;\r\n  }\r\n  \r\n  &:not(.ant-switch-checked) {\r\n    background-color: @error-color;\r\n  }\r\n}\r\n\r\n// 表单操作区\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 12px;\r\n  margin-top: 24px;\r\n  \r\n  .ant-btn {\r\n    min-width: 100px;\r\n  }\r\n}\r\n\r\n// 物料库存模态框\r\n.inventory-modal {\r\n  .modal-header {\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    margin-bottom: 20px;\r\n    color: @primary-color;\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    .anticon {\r\n      margin-right: 10px;\r\n      font-size: 20px;\r\n    }\r\n  }\r\n  \r\n  .inventory-search-container {\r\n    margin-bottom: 20px;\r\n    \r\n    .search-input {\r\n      width: 100%;\r\n    }\r\n  }\r\n  \r\n  .inventory-list-container {\r\n    max-height: 400px;\r\n    overflow-y: auto;\r\n    margin-bottom: 20px;\r\n    border: 1px solid #f0f0f0;\r\n    border-radius: @border-radius-base;\r\n    padding: 16px;\r\n    background-color: #fafafa;\r\n  }\r\n  \r\n  .inventory-name {\r\n    font-weight: 500;\r\n  }\r\n  \r\n  .quantity-value {\r\n    font-weight: 600;\r\n    color: @primary-color;\r\n  }\r\n  \r\n  .select-btn {\r\n    border-radius: 16px;\r\n  }\r\n  \r\n  .modal-footer {\r\n    margin-top: 16px;\r\n    text-align: right;\r\n  }\r\n}\r\n\r\n// 表格行样式\r\n.material-table-row {\r\n  transition: all @transition-duration;\r\n  \r\n  &:hover {\r\n    background-color: #f0f7ff;\r\n  }\r\n}\r\n\r\n.inventory-table-row {\r\n  transition: all @transition-duration;\r\n  \r\n  &:hover {\r\n    background-color: #f0f7ff;\r\n  }\r\n}\r\n\r\n// 库存搜索容器\r\n.inventory-search-container {\r\n  margin-bottom: 16px;\r\n  \r\n  .search-input {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.product-search-wrapper {\r\n  position: relative;\r\n  \r\n  .ant-select-auto-complete {\r\n    width: 100%;\r\n  }\r\n\r\n  .ant-input-suffix {\r\n    cursor: pointer;\r\n    color: @primary-color;\r\n    \r\n    &:hover {\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式调整\r\n@media screen and (max-width: 576px) {\r\n  .material-search-container {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .material-list-container {\r\n    overflow-x: auto;\r\n    padding: 12px 8px;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n    \r\n    .ant-btn {\r\n      width: 100%;\r\n    }\r\n  }\r\n  \r\n  .status-switch-container {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n    \r\n    .status-text {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n}\r\n", {"version": 3, "sources": ["MaterialBillForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "MaterialBillForm.vue", "sourceRoot": "src/views/admin/purchaseSalesInventory/material/modules", "sourcesContent": ["<template>\r\n  <a-spin :spinning=\"confirmLoading\">\r\n    <j-form-container :disabled=\"formDisabled\">\r\n      <a-form-model ref=\"form\" :model=\"model\" :rules=\"validatorRules\" slot=\"detail\">\r\n        <!-- 产品选择卡片 -->\r\n        <a-card class=\"custom-card product-card\" :bordered=\"false\">\r\n          <div class=\"card-title\">\r\n            <a-icon type=\"shop\" />\r\n            <span>选择产品</span>\r\n          </div>\r\n          <a-form-model-item prop=\"productId\">\r\n            <!-- 产品搜索选择器 -->\r\n            <div class=\"product-search-wrapper\">\r\n              <a-auto-complete\r\n                v-model=\"model.productName\"\r\n                placeholder=\"请输入产品名称搜索\"\r\n                :dropdownMatchSelectWidth=\"false\"\r\n                @search=\"handleProductSearch\"\r\n                @select=\"handleProductSelect\"\r\n                class=\"custom-select\"\r\n                size=\"large\"\r\n                :filterOption=\"false\"\r\n                :defaultActiveFirstOption=\"false\"\r\n              >\r\n                <template slot=\"dataSource\">\r\n                  <a-select-option v-if=\"loadingProducts\" disabled key=\"loading\">\r\n                    <a-icon type=\"loading\" spin />\r\n                    加载中...\r\n                  </a-select-option>\r\n                  <a-select-option\r\n                    v-for=\"product in filteredProducts\"\r\n                    :key=\"product.id\"\r\n                    :value=\"product.id\"\r\n                  >\r\n                    {{ product.name }}\r\n                  </a-select-option>\r\n                </template>\r\n                <a-input>\r\n                  <a-icon slot=\"suffix\" type=\"search\" @click=\"loadProducts\" />\r\n                </a-input>\r\n              </a-auto-complete>\r\n            </div>\r\n            \r\n            <!-- 隐藏的产品ID字段，用于表单提交 -->\r\n            <a-input type=\"hidden\" v-model=\"model.productId\" />\r\n          </a-form-model-item>\r\n          \r\n          <div v-if=\"model.productId && model.productName\" class=\"selected-product-info\">\r\n            <a-alert type=\"success\" show-icon>\r\n              <template slot=\"message\">\r\n                已选择产品: <strong>{{ model.productName }}</strong>\r\n              </template>\r\n            </a-alert>\r\n          </div>\r\n        </a-card>\r\n\r\n        <!-- 物料清单名称卡片 -->\r\n        <a-card class=\"custom-card name-card\" :bordered=\"false\">\r\n          <div class=\"card-title\">\r\n            <a-icon type=\"edit\" />\r\n            <span>物料清单名称</span>\r\n          </div>\r\n          <a-form-model-item prop=\"materialBillName\">\r\n            <a-input\r\n              v-model=\"model.materialBillName\"\r\n              placeholder=\"请输入物料清单名称\"\r\n              size=\"large\"\r\n              :maxLength=\"50\"\r\n              show-count\r\n            >\r\n              <a-icon slot=\"prefix\" type=\"file-text\" />\r\n            </a-input>\r\n          </a-form-model-item>\r\n        </a-card>\r\n\r\n        <!-- 物料清单卡片 -->\r\n        <a-card class=\"custom-card material-card\" :bordered=\"false\">\r\n          <div class=\"card-header\">\r\n            <div class=\"card-title\">\r\n              <a-icon type=\"database\" />\r\n              <span>物料清单</span>\r\n            </div>\r\n            <a-badge :count=\"materialList.length\" :overflowCount=\"99\" class=\"material-count-badge\" />\r\n          </div>\r\n\r\n          <div class=\"material-search-container\">\r\n            <a-input-search\r\n              placeholder=\"输入物料名称或单位进行模糊搜索\"\r\n              v-model=\"searchMaterialName\"\r\n              @search=\"searchMaterial\"\r\n              enter-button\r\n              class=\"search-input\"\r\n              size=\"large\"\r\n            >\r\n              <a-icon slot=\"prefix\" type=\"search\" />\r\n            </a-input-search>\r\n            \r\n            <a-tooltip title=\"从物料库中添加物料\">\r\n              <a-button type=\"primary\" icon=\"plus\" @click=\"searchMaterial\" size=\"large\">\r\n                添加物料\r\n              </a-button>\r\n            </a-tooltip>\r\n          </div>\r\n\r\n          <div class=\"material-list-container\">\r\n            <a-empty v-if=\"materialList.length === 0\" description=\"暂无物料，请搜索并添加\">\r\n              <a-button type=\"primary\" @click=\"searchMaterial\">添加物料</a-button>\r\n            </a-empty>\r\n            <a-table\r\n              v-else\r\n              :columns=\"materialColumns\"\r\n              :dataSource=\"materialList\"\r\n              :pagination=\"false\"\r\n              :rowKey=\"record => record.id || record.tempId\"\r\n              size=\"middle\"\r\n              :scroll=\"{ x: 800 }\"\r\n              :bordered=\"true\"\r\n              :rowClassName=\"'material-table-row'\"\r\n            >\r\n              <!-- 物料名称列 -->\r\n              <template slot=\"materialName\" slot-scope=\"text, record, index\">\r\n                <div class=\"material-name-cell\">\r\n                  <template v-if=\"record.tempId\">\r\n                    <a-input\r\n                      v-model=\"materialList[index].materialName\"\r\n                      placeholder=\"请输入物料名称\"\r\n                      @change=\"updateMaterialJson\"\r\n                    />\r\n                  </template>\r\n                  <template v-else>\r\n                    <span class=\"material-name\">{{ text }}</span>\r\n                    <a-tag v-if=\"record.imSpecs\" color=\"blue\" class=\"material-spec-tag\">{{ record.imSpecs }}</a-tag>\r\n                  </template>\r\n                </div>\r\n              </template>\r\n\r\n              <!-- 单位列 -->\r\n              <template slot=\"materialUnit\" slot-scope=\"text, record, index\">\r\n                <div class=\"unit-cell\">\r\n                  <template v-if=\"record.tempId\">\r\n                    <a-input\r\n                      v-model=\"materialList[index].materialUnit\"\r\n                      placeholder=\"请输入单位\"\r\n                      @change=\"updateMaterialJson\"\r\n                    />\r\n                  </template>\r\n                  <template v-else>\r\n                    <a-tag color=\"cyan\">{{ text || '个' }}</a-tag>\r\n                  </template>\r\n                </div>\r\n              </template>\r\n\r\n              <!-- 数量列 -->\r\n              <template slot=\"materialCount\" slot-scope=\"text, record, index\">\r\n                <div class=\"count-cell\">\r\n                  <a-input-number\r\n                    v-model=\"materialList[index].materialCount\"\r\n                    :min=\"1\"\r\n                    @change=\"value => handleCountChange(value, index)\"\r\n                    class=\"count-input\"\r\n                    size=\"default\"\r\n                  />\r\n                  <span class=\"unit-text\">{{ record.materialSpecifications || '个' }}</span>\r\n                </div>\r\n              </template>\r\n\r\n              <!-- 操作列 -->\r\n              <template slot=\"action\" slot-scope=\"text, record, index\">\r\n                <div class=\"action-cell\">\r\n                  <a-button type=\"danger\" size=\"small\" @click=\"removeMaterial(index)\" class=\"delete-btn\">\r\n                    <a-icon type=\"delete\" />\r\n                  </a-button>\r\n                </div>\r\n              </template>\r\n            </a-table>\r\n            \r\n            <div v-if=\"materialList.length > 0\" class=\"material-summary\">\r\n              <div class=\"summary-item\">\r\n                <span class=\"summary-label\">总物料数:</span>\r\n                <span class=\"summary-value\">{{ materialList.length }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </a-card>\r\n\r\n        <!-- 物料清单设置卡片 -->\r\n        <a-card class=\"custom-card status-card\" :bordered=\"false\">\r\n          <div class=\"card-title\">\r\n            <a-icon type=\"setting\" />\r\n            <span>物料清单设置</span>\r\n          </div>\r\n          <a-form-model-item label=\"物料清单状态\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"materialType\">\r\n            <div class=\"status-switch-container\">\r\n              <a-switch\r\n                checkedChildren=\"启用\"\r\n                unCheckedChildren=\"禁用\"\r\n                @change=\"onChose\"\r\n                v-model=\"visibleCheck\"\r\n                class=\"custom-switch\"\r\n                size=\"large\"\r\n              />\r\n              <span :class=\"['status-text', visibleCheck ? 'status-enabled' : 'status-disabled']\">\r\n                {{ visibleCheck ? '清单已启用' : '清单已禁用' }}\r\n              </span>\r\n            </div>\r\n          </a-form-model-item>\r\n\r\n          <a-form-model-item label=\"库存用完处理策略\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"stockEmptyStrategy\">\r\n            <a-checkbox-group v-model=\"stockEmptyStrategyArray\" class=\"strategy-checkbox-group\">\r\n              <a-checkbox value=\"1\" class=\"strategy-checkbox\" style=\"margin-left: 7px;\">\r\n                <div class=\"checkbox-content\">\r\n                  <div class=\"checkbox-title\">自动禁用此物料清单</div>\r\n                  <div class=\"checkbox-desc\">当前采购批次库存用完时，自动禁用此物料清单</div>\r\n                </div>\r\n              </a-checkbox>\r\n              <a-checkbox value=\"2\" class=\"strategy-checkbox\">\r\n                <div class=\"checkbox-content\">\r\n                  <div class=\"checkbox-title\">自动切换为同成本物料</div>\r\n                  <div class=\"checkbox-desc\">当前采购批次库存用完时，自动切换为同成本的其他物料</div>\r\n                </div>\r\n              </a-checkbox>\r\n            </a-checkbox-group>\r\n          </a-form-model-item>\r\n        </a-card>\r\n        \r\n        <!-- 提交按钮区域 -->\r\n        <div class=\"form-actions\" v-if=\"!formDisabled\">\r\n          <a-button @click=\"resetForm\" :disabled=\"confirmLoading\">\r\n            <a-icon type=\"undo\" />重置\r\n          </a-button>\r\n          <a-button type=\"primary\" @click=\"submitForm\" :loading=\"confirmLoading\" :disabled=\"materialList.length === 0\">\r\n            <a-icon type=\"check\" />保存\r\n          </a-button>\r\n        </div>\r\n      </a-form-model>\r\n    </j-form-container>\r\n\r\n    <!-- 物料搜索弹窗 -->\r\n    <a-modal\r\n      title=\"搜索物料库存\"\r\n      :visible=\"inventoryModalVisible\"\r\n      :width=\"800\"\r\n      @cancel=\"closeInventoryModal\"\r\n      :footer=\"null\"\r\n      :destroyOnClose=\"true\"\r\n      :maskClosable=\"false\"\r\n      class=\"inventory-modal\"\r\n    >\r\n      <div class=\"modal-header\">\r\n        <a-icon type=\"database\" theme=\"filled\" />\r\n        <span>添加物料到清单</span>\r\n      </div>\r\n      \r\n      <div class=\"inventory-search-container\">\r\n        <a-input-search\r\n          placeholder=\"输入物料名称或单位进行模糊搜索\"\r\n          v-model=\"inventorySearchName\"\r\n          @search=\"searchInventory\"\r\n          enter-button\r\n          class=\"search-input\"\r\n          size=\"large\"\r\n          @pressEnter=\"searchInventory\"\r\n        >\r\n          <a-icon slot=\"prefix\" type=\"search\" />\r\n        </a-input-search>\r\n      </div>\r\n\r\n      <a-spin :spinning=\"loadingInventory\">\r\n        <div class=\"inventory-list-container\">\r\n          <a-empty v-if=\"inventoryList.length === 0 && !loadingInventory\" description=\"未找到物料，请尝试其他关键词\" />\r\n          <a-table\r\n            v-else\r\n            :columns=\"inventoryColumns\"\r\n            :dataSource=\"inventoryList\"\r\n            :pagination=\"{ pageSize: 5, showQuickJumper: true, showSizeChanger: true }\"\r\n            :rowKey=\"record => record.id\"\r\n            size=\"middle\"\r\n            :bordered=\"true\"\r\n            :rowClassName=\"'inventory-table-row'\"\r\n          >\r\n            <template slot=\"imItemName\" slot-scope=\"text\">\r\n              <span class=\"inventory-name\">{{ text }}</span>\r\n            </template>\r\n\r\n            <template slot=\"imUnit\" slot-scope=\"text\">\r\n              <a-tag color=\"blue\">{{ text || '个' }}</a-tag>\r\n            </template>\r\n\r\n            <template slot=\"imQuantity\" slot-scope=\"text\">\r\n              <span class=\"quantity-value\">{{ text }}</span>\r\n            </template>\r\n\r\n            <template slot=\"purchaseBatch\" slot-scope=\"text\">\r\n              <a-tag color=\"green\">{{ text || '批次-001' }}</a-tag>\r\n            </template>\r\n\r\n            <template slot=\"purchaseTime\" slot-scope=\"text\">\r\n              <span class=\"purchase-time\">{{ text || '2025-01-01' }}</span>\r\n            </template>\r\n\r\n            <template slot=\"purchaseUnitCost\" slot-scope=\"text\">\r\n              <span class=\"unit-cost\">¥{{ text || '0.00' }}</span>\r\n            </template>\r\n\r\n            <template slot=\"action\" slot-scope=\"text, record\">\r\n              <a-button type=\"primary\" size=\"small\" @click=\"selectInventoryItem(record)\" class=\"select-btn\">\r\n                <a-icon type=\"plus\" />添加\r\n              </a-button>\r\n            </template>\r\n          </a-table>\r\n        </div>\r\n      </a-spin>\r\n      \r\n      <div class=\"modal-footer\">\r\n        <a-button @click=\"closeInventoryModal\">关闭</a-button>\r\n      </div>\r\n    </a-modal>\r\n  </a-spin>\r\n</template>\r\n\r\n<script>\r\nimport { httpAction, getAction } from '@/api/manage'\r\nimport { validateDuplicateValue } from '@/utils/util'\r\nimport moment from 'moment'\r\nexport default {\r\n  name: 'MaterialBillForm',\r\n  components: {\r\n  },\r\n  props: {\r\n    //表单禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false,\r\n      required: false\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      products: [],\r\n      filteredProducts: [], // 添加过滤后的产品列表\r\n      loadingProducts: false,\r\n      visibleCheck: true,\r\n      materialList: [],\r\n      searchMaterialName: '',\r\n      inventoryModalVisible: false,\r\n      inventorySearchName: '',\r\n      inventoryList: [],\r\n      loadingInventory: false,\r\n      tempIdCounter: 0,\r\n      model: {\r\n        productId: '',\r\n        productName: '',\r\n        materialBillName: '',\r\n        materialType: 0,\r\n        materialJson: '',\r\n        materialApprovalStatus: '',\r\n        stockEmptyStrategy: '', // 存储逗号分隔的策略值\r\n        imItemPrice : 0\r\n      },\r\n      stockEmptyStrategyArray: [], // 用于多选框的数组\r\n      materialColumns: [\r\n        {\r\n          title: '物料名称',\r\n          dataIndex: 'materialName',\r\n          key: 'materialName',\r\n          scopedSlots: { customRender: 'materialName' },\r\n          width: 220,\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '单位',\r\n          dataIndex: 'materialUnit',\r\n          key: 'materialUnit',\r\n          scopedSlots: { customRender: 'materialUnit' },\r\n          width: 120\r\n        },\r\n        {\r\n          title: '数量',\r\n          key: 'materialCount',\r\n          width: 150,\r\n          scopedSlots: { customRender: 'materialCount' }\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: 80,\r\n          fixed: 'right',\r\n          scopedSlots: { customRender: 'action' }\r\n        }\r\n      ],\r\n      inventoryColumns: [\r\n        {\r\n          title: '物料名称',\r\n          dataIndex: 'imItemName',\r\n          key: 'imItemName',\r\n          ellipsis: true,\r\n          scopedSlots: { customRender: 'imItemName' }\r\n        },\r\n        {\r\n          title: '单位',\r\n          dataIndex: 'imUnit',\r\n          key: 'imUnit',\r\n          scopedSlots: { customRender: 'imUnit' }\r\n        },\r\n        {\r\n          title: '库存数量',\r\n          dataIndex: 'imInventoryQuantity',\r\n          key: 'imInventoryQuantity',\r\n          scopedSlots: { customRender: 'imQuantity' }\r\n        },\r\n        {\r\n          title: '采购批次',\r\n          dataIndex: 'purchaseBatch',\r\n          key: 'purchaseBatch',\r\n          scopedSlots: { customRender: 'purchaseBatch' }\r\n        },\r\n        {\r\n          title: '采购时间',\r\n          dataIndex: 'purchaseTime',\r\n          key: 'purchaseTime',\r\n          scopedSlots: { customRender: 'purchaseTime' }\r\n        },\r\n        {\r\n          title: '采购单位成本',\r\n          dataIndex: 'purchaseUnitCost',\r\n          key: 'purchaseUnitCost',\r\n          scopedSlots: { customRender: 'purchaseUnitCost' }\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: 100,\r\n          fixed: 'right',\r\n          scopedSlots: { customRender: 'action' }\r\n        }\r\n      ],\r\n      labelCol: {\r\n        xs: { span: 24 },\r\n        sm: { span: 5 },\r\n      },\r\n      wrapperCol: {\r\n        xs: { span: 24 },\r\n        sm: { span: 16 },\r\n      },\r\n      confirmLoading: false,\r\n      validatorRules: {\r\n        productId: [\r\n          { required: true, message: '请选择产品' }\r\n        ],\r\n        materialBillName: [\r\n          { required: true, message: '请输入物料清单名称' },\r\n          { max: 50, message: '物料清单名称不能超过50个字符' }\r\n        ]\r\n      },\r\n      url: {\r\n        add: \"/admin/materialBill/add\",\r\n        edit: \"/admin/materialBill/edit\",\r\n        queryById: \"/admin/materialBill/queryById\",\r\n        inventoryList: \"/admin/inventoryManagement/list\",\r\n        searchMaterials: \"/admin/inventoryManagement/searchMaterials\",\r\n        queryProductById: \"/sys/sysDepart/queryProductById\"\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadProducts()\r\n    // 处理路由参数\r\n    const routeProductId = this.$route.query.productId\r\n    if (routeProductId) {\r\n      this.model.productId = routeProductId\r\n      this.handleProductChange(routeProductId)\r\n    }\r\n  },\r\n  watch: {\r\n    materialList: {\r\n      handler(newVal) {\r\n        // 当物料列表变化时，更新materialJson\r\n        this.updateMaterialJson()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  computed: {\r\n    formDisabled(){\r\n      return this.disabled\r\n    },\r\n  },\r\n  watch: {\r\n    // 监听多选框数组变化，同步到字符串字段\r\n    stockEmptyStrategyArray: {\r\n      handler(newVal) {\r\n        this.model.stockEmptyStrategy = newVal.join(',');\r\n      },\r\n      deep: true\r\n    },\r\n    // 监听字符串字段变化，同步到多选框数组\r\n    'model.stockEmptyStrategy': {\r\n      handler(newVal) {\r\n        if (newVal && typeof newVal === 'string') {\r\n          this.stockEmptyStrategyArray = newVal.split(',').filter(item => item.trim());\r\n        } else {\r\n          this.stockEmptyStrategyArray = [];\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  created () {\r\n    //备份model原始值\r\n    this.modelDefault = JSON.parse(JSON.stringify(this.model));\r\n    // 设置默认值为启用\r\n    if (this.model.materialType === undefined) {\r\n      this.model.materialType = 0;\r\n    }\r\n  },\r\n  methods: {\r\n    // 重置表单\r\n    resetForm() {\r\n      this.model = JSON.parse(JSON.stringify(this.modelDefault));\r\n      this.materialList = [];\r\n      this.visibleCheck = true;\r\n      if (this.$refs.form) {\r\n        this.$refs.form.resetFields();\r\n      }\r\n    },\r\n    \r\n    // 更新物料列表\r\n    updateMaterialList() {\r\n      try {\r\n        this.model.materialList = this.materialList.map((item, index) => ({\r\n          id: item.id || item.tempId,\r\n          materialName: item.materialName || '',\r\n          materialUnit: item.materialUnit || '',\r\n          materialCount: String(item.materialCount || 1), // 改为 materialCount\r\n          imItemPrice: item.imItemPrice || 0,\r\n          companyId: item.companyId || '',\r\n          createBy: item.createBy || '',\r\n          createTime: item.createTime || '',\r\n          materialBillId: this.model.id || '',\r\n          productionOrderId: item.productionOrderId || '',\r\n          sysOrgCode: item.sysOrgCode || '',\r\n          updateBy: item.updateBy || '',\r\n          updateTime: item.updateTime || '',\r\n        }));\r\n\r\n        console.log('更新后的 materialList:', this.model.materialList);\r\n      } catch (error) {\r\n        console.error('更新 materialList 失败:', error);\r\n      }\r\n    },\r\n    \r\n    onChose(checked) {\r\n      if (checked) {\r\n        this.model.materialType = 0;  // 0表示启用\r\n        this.visibleCheck = true;\r\n      } else {\r\n        this.model.materialType = 1;  // 1表示禁用\r\n        this.visibleCheck = false;\r\n      }\r\n    },\r\n\r\n    async loadProducts() {\r\n      try {\r\n        this.loadingProducts = true\r\n        const res = await getAction('/sys/sysDepart/queryProductsByCurrentEnterprise', {})\r\n        if (res.success) {\r\n          this.products = (res.result || [])\r\n            .filter(p => p.id && p.productName)\r\n            .map(p => ({\r\n              id: p.id,\r\n              name: p.productName\r\n            }))\r\n          \r\n          // 初始设置过滤后的产品列表与产品列表相同\r\n          this.filteredProducts = [...this.products]\r\n          \r\n          return this.products\r\n        } else {\r\n          this.$message.error('产品加载失败：' + (res.message || '请求异常'))\r\n          this.products = []\r\n          this.filteredProducts = []\r\n          return []\r\n        }\r\n      } catch (error) {\r\n        console.error('产品加载失败:', error)\r\n        this.$message.error('产品加载失败：' + (error.message || '未知错误'))\r\n        this.products = []\r\n        this.filteredProducts = []\r\n        return []\r\n      } finally {\r\n        this.loadingProducts = false\r\n      }\r\n    },\r\n\r\n    add () {\r\n      this.edit(this.modelDefault);\r\n    },\r\n\r\n    // 产品选择变更\r\n    handleProductChange(productId) {\r\n      if (!productId) {\r\n        this.model.productId = '';\r\n        this.model.productName = '';\r\n        return;\r\n      }\r\n      \r\n      this.model.productId = productId;\r\n      \r\n      // 查找选中的产品名称并设置\r\n      const selectedProduct = this.products.find(p => p.id === productId);\r\n      if (selectedProduct) {\r\n        this.model.productName = selectedProduct.name;\r\n        console.log('产品已选择:', selectedProduct.name);\r\n      } else {\r\n        // 如果在本地列表中找不到，尝试从服务器获取\r\n        getAction(this.url.queryProductById, { id: productId })\r\n          .then(res => {\r\n            if (res.success && res.result) {\r\n              this.model.productName = res.result.productName || res.result.name;\r\n              console.log('从服务器获取产品名称:', this.model.productName);\r\n            }\r\n          })\r\n          .catch(err => {\r\n            console.error('获取产品详情失败:', err);\r\n          });\r\n      }\r\n    },\r\n\r\n    // 处理产品搜索\r\n    handleProductSearch(searchText) {\r\n      this.filteredProducts = this.products.filter(product =>\r\n        product.name.toLowerCase().includes(searchText.toLowerCase())\r\n      );\r\n    },\r\n\r\n    // 处理产品选择\r\n    handleProductSelect(value) {\r\n      this.model.productId = value;\r\n      this.model.productName = this.products.find(p => p.id === value).name;\r\n      this.filteredProducts = []; // 清空过滤列表\r\n    },\r\n\r\n    // 打开物料搜索弹窗\r\n    searchMaterial() {\r\n      this.inventoryModalVisible = true\r\n      this.inventorySearchName = this.searchMaterialName\r\n      this.searchInventory()\r\n    },\r\n\r\n    // 关闭物料搜索弹窗\r\n    closeInventoryModal() {\r\n      this.inventoryModalVisible = false\r\n      this.inventoryList = []\r\n    },\r\n\r\n    // 搜索库存物料\r\n    async searchInventory() {\r\n      if (!this.inventorySearchName.trim()) {\r\n        this.$message.warning('请输入搜索关键词')\r\n        return\r\n      }\r\n\r\n      try {\r\n        this.loadingInventory = true\r\n        // 使用新的模糊搜索接口\r\n        const res = await getAction(this.url.searchMaterials, {\r\n          keyword: this.inventorySearchName\r\n        })\r\n\r\n        if (res.success) {\r\n          this.inventoryList = res.result || []\r\n          if (this.inventoryList.length === 0) {\r\n            this.$message.info('未找到匹配的物料，请尝试其他关键词')\r\n          }\r\n        } else {\r\n          this.$message.error('库存查询失败：' + (res.message || '请求异常'))\r\n          this.inventoryList = []\r\n        }\r\n      } catch (error) {\r\n        console.error('库存查询失败:', error)\r\n        this.$message.error('库存查询失败：' + (error.message || '未知错误'))\r\n        this.inventoryList = []\r\n      } finally {\r\n        this.loadingInventory = false\r\n      }\r\n    },\r\n\r\n    // 选择库存物料\r\n    // 选择库存物料\r\n    selectInventoryItem(item) {\r\n      const existingIndex = this.materialList.findIndex(m => m.id === item.id);\r\n\r\n      if (existingIndex >= 0) {\r\n        this.$message.warning('该物料已添加到清单中');\r\n        return;\r\n      }\r\n\r\n      const newMaterial = {\r\n        id: item.id,\r\n        materialName: item.imItemName,\r\n        materialSpecifications: item.imItemUnit || '个', // 将单位存储到规格字段\r\n        materialCount: 1,\r\n        imItemPrice: item.imItemPrice || 0, // 新增：传递物料价格\r\n        companyId: '', // 默认公司 ID\r\n        createBy: this.model.createBy || '', // 创建人\r\n        createTime: moment().format('YYYY-MM-DD HH:mm:ss'), // 创建时间\r\n        materialBillId: this.model.id || '', // 物料清单 ID\r\n        productionOrderId: '', // 生产订单 ID\r\n        sysOrgCode: this.model.sysOrgCode || '', // 所属部门\r\n        updateBy: '', // 更新人\r\n        updateTime: '', // 更新时间\r\n      };\r\n\r\n      this.materialList.push(newMaterial);\r\n      this.updateMaterialList();\r\n      this.$message.success(`已添加物料: ${item.imItemName}`);\r\n      this.closeInventoryModal();\r\n    },\r\n\r\n    // 手动添加物料\r\n    // 手动添加物料\r\n    addMaterial() {\r\n      const tempId = 'temp_' + (++this.tempIdCounter);\r\n      this.materialList.push({\r\n        tempId,\r\n        materialName: '', // 空值\r\n        materialUnit: '', // 空值\r\n        materialCount: 1, // 默认数量\r\n        imItemPrice: 0, // 新增：默认物料价格为 0\r\n        companyId: '', // 默认公司 ID\r\n        createBy: '', // 默认创建人\r\n        createTime: moment().format('YYYY-MM-DD HH:mm:ss'), // 默认创建时间\r\n        materialBillId: this.model.id || '', // 物料清单 ID\r\n        productionOrderId: '', // 默认生产订单 ID\r\n        sysOrgCode: '', // 默认所属部门\r\n        updateBy: '', // 默认更新人\r\n        updateTime: '', // 默认更新时间\r\n      });\r\n\r\n      this.updateMaterialList();\r\n    },\r\n\r\n    // 删除物料\r\n    removeMaterial(index) {\r\n      this.materialList.splice(index, 1); // 从数组中移除\r\n      this.updateMaterialList(); // 更新 materialList\r\n    },\r\n\r\n    // 更新数量\r\n    handleCountChange(value, index) {\r\n      if (!this.materialList[index]) return;\r\n      this.materialList[index].materialCount = value; // 更新数量\r\n      this.updateMaterialList(); // 更新 materialList\r\n    },\r\n\r\n    // 更新materialJson\r\n    updateMaterialJson() {\r\n      try {\r\n        const jsonData = this.materialList.map(item => ({\r\n          materialName: item.materialName,\r\n          materialUnit: item.materialUnit,\r\n          materialCount: String(item.materialCount),\r\n          imItemPrice: item.imItemPrice || 0, // 新增：传递物料价格\r\n          id: item.id || item.tempId,\r\n          // 保留其他可能需要的字段\r\n          ...(item.originalData ? { originalData: item.originalData } : {})\r\n        }))\r\n\r\n        this.model.materialJson = JSON.stringify(jsonData)\r\n      } catch (error) {\r\n        console.error('生成物料JSON失败:', error)\r\n      }\r\n    },\r\n    // 从JSON恢复物料列表\r\n    restoreMaterialListFromJson() {\r\n      try {\r\n        if (!Array.isArray(this.model.materialList)) {\r\n          this.materialList = [];\r\n          return;\r\n        }\r\n\r\n        this.materialList = this.model.materialList.map(item => ({\r\n          id: item.id || ('temp_' + (++this.tempIdCounter)),\r\n          materialName: item.materialName || '',\r\n          materialSpecifications: item.materialSpecifications || '',\r\n          materialCount: parseInt(item.materialCount) || 1, // 改为从 materialCount 获取\r\n          imItemPrice: item.imItemPrice || 0,\r\n          companyId: item.companyId || '',\r\n          createBy: item.createBy || '',\r\n          createTime: item.createTime || '',\r\n          materialBillId: item.materialBillId || '',\r\n          productionOrderId: item.productionOrderId || '',\r\n          sysOrgCode: item.sysOrgCode || '',\r\n          updateBy: item.updateBy || '',\r\n          updateTime: item.updateTime || '',\r\n        }));\r\n      } catch (error) {\r\n        console.error('恢复物料列表失败:', error);\r\n        this.materialList = [];\r\n      }\r\n    },\r\n\r\n    edit (record) {\r\n      this.model = Object.assign({}, record);\r\n      this.visibleCheck = this.model.materialType === 0 || this.model.materialType === \"0\";\r\n      this.visible = true; // 设置visible属性为true\r\n      \r\n      // 打印当前编辑记录，方便排查问题\r\n      console.log('编辑记录:', record);\r\n      \r\n      // 确保productId正确设置，防止undefined或null值\r\n      if (!this.model.productId) {\r\n        this.model.productId = '';\r\n        this.model.productName = '';\r\n      }\r\n      \r\n      // 当存在productId但没有productName时，从加载的产品列表中查找产品名称\r\n      if (this.model.productId && !this.model.productName) {\r\n        // 如果产品列表已加载\r\n        if (this.products && this.products.length > 0) {\r\n          const selectedProduct = this.products.find(p => p.id === this.model.productId);\r\n          if (selectedProduct) {\r\n            this.model.productName = selectedProduct.name;\r\n            console.log('从产品列表中获取到产品名称:', this.model.productName);\r\n          } else {\r\n            // 如果在本地找不到，从服务器查询\r\n            this.queryProductNameById(this.model.productId);\r\n          }\r\n        } else {\r\n          // 如果产品列表尚未加载，先加载产品列表\r\n          this.loadProducts().then(() => {\r\n            const selectedProduct = this.products.find(p => p.id === this.model.productId);\r\n            if (selectedProduct) {\r\n              this.model.productName = selectedProduct.name;\r\n              console.log('异步加载产品名称成功:', this.model.productName);\r\n            } else {\r\n              // 如果仍然找不到，尝试单独查询这个产品\r\n              this.queryProductNameById(this.model.productId);\r\n            }\r\n          });\r\n        }\r\n      }\r\n      \r\n      // 恢复物料列表\r\n      if (Array.isArray(this.model.materialList) && this.model.materialList.length > 0) {\r\n        // 直接使用后端返回的物料列表\r\n        this.materialList = this.model.materialList.map(item => ({\r\n          id: item.id,\r\n          materialName: item.materialName || '',\r\n          materialSpecifications: item.materialSpecifications || '',\r\n          materialCount: parseInt(item.materialCount) || 1,\r\n          imItemPrice: item.imItemPrice || 0,\r\n          companyId: item.companyId || '',\r\n          createBy: item.createBy || '',\r\n          createTime: item.createTime || '',\r\n          materialBillId: item.materialBillId || '',\r\n          productionOrderId: item.productionOrderId || '',\r\n          sysOrgCode: item.sysOrgCode || '',\r\n          updateBy: item.updateBy || '',\r\n          updateTime: item.updateTime || '',\r\n        }));\r\n      } else {\r\n        // 如果没有物料列表数据，尝试从materialJson解析\r\n        this.restoreMaterialListFromJson();\r\n      }\r\n      \r\n      console.log('加载的物料列表:', this.materialList);\r\n    },\r\n    \r\n    // 通过产品ID查询产品名称\r\n    queryProductNameById(productId) {\r\n      if (!productId) return;\r\n      \r\n      getAction(this.url.queryProductById, { id: productId })\r\n        .then(res => {\r\n          if (res.success && res.result) {\r\n            this.model.productName = res.result.productName || res.result.name;\r\n            console.log('通过API查询获取产品名称:', this.model.productName);\r\n          } else {\r\n            console.warn('查询产品详情返回空数据');\r\n          }\r\n        })\r\n        .catch(err => {\r\n          console.error('查询产品详情失败:', err);\r\n        });\r\n    },\r\n\r\n    submitForm() {\r\n      const invalidItem = this.materialList.find(item =>\r\n        !moment(item.createTime, 'YYYY-MM-DD HH:mm:ss', true).isValid()\r\n      );\r\n\r\n      if (invalidItem) {\r\n        this.$message.error(`物料 ${invalidItem.materialName} 时间格式错误`);\r\n        return;\r\n      }\r\n\r\n      // 检查是否有物料\r\n      if (this.materialList.length === 0) {\r\n        this.$message.warning('请至少添加一种物料');\r\n        return;\r\n      }\r\n\r\n      // 更新最终的 materialList\r\n      this.updateMaterialList();\r\n\r\n      // 提取 materialList 中的 imItemPrice\r\n      const imItemPrice = this.materialList.reduce((total, item) => {\r\n        return total + (item.imItemPrice || 0) * (item.materialCount || 1); // 改为 materialCount\r\n      }, 0);\r\n\r\n      // 构造最终的请求参数\r\n      const requestData = {\r\n        ...this.model,\r\n        imItemPrice: imItemPrice,\r\n        materialList: this.materialList,\r\n      };\r\n\r\n      console.log('最终请求参数:', requestData);\r\n\r\n      // 验证表单\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          this.confirmLoading = true;\r\n          const httpurl = this.model.id ? this.url.edit : this.url.add;\r\n          const method = this.model.id ? 'put' : 'post';\r\n\r\n          httpAction(httpurl, requestData, method).then(res => {\r\n            if (res.success) {\r\n              this.$message.success(res.message);\r\n              this.$emit('ok');\r\n            } else {\r\n              this.$message.warning(res.message);\r\n            }\r\n          }).finally(() => {\r\n            this.confirmLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n// 变量定义\r\n@primary-color: #1890ff;\r\n@success-color: #52c41a;\r\n@warning-color: #faad14;\r\n@error-color: #f5222d;\r\n@border-radius-base: 8px;\r\n@card-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\r\n@transition-duration: 0.3s;\r\n\r\n.ant-input {\r\n  width: 100%;\r\n  max-width: 180px;\r\n}\r\n\r\n// 自定义卡片样式\r\n.custom-card {\r\n  margin-bottom: 24px;\r\n  border-radius: @border-radius-base;\r\n  box-shadow: @card-shadow;\r\n  overflow: hidden;\r\n  transition: all @transition-duration;\r\n\r\n  &:hover {\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\r\n  }\r\n\r\n  &.product-card {\r\n    background: linear-gradient(to right, #f0f7ff, #ffffff);\r\n    border-left: 4px solid #1890ff;\r\n  }\r\n\r\n  &.name-card {\r\n    background: linear-gradient(to right, #f6ffed, #ffffff);\r\n    border-left: 4px solid #52c41a;\r\n  }\r\n\r\n  &.material-card {\r\n    background: linear-gradient(to right, #f0fff0, #ffffff);\r\n    border-left: 4px solid #52c41a;\r\n  }\r\n\r\n  &.status-card {\r\n    background: linear-gradient(to right, #fffbe6, #ffffff);\r\n    border-left: 4px solid #faad14;\r\n  }\r\n}\r\n\r\n// 卡片标题\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  margin-bottom: 20px;\r\n  color: #1890ff;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .anticon {\r\n    margin-right: 10px;\r\n    font-size: 18px;\r\n  }\r\n}\r\n\r\n// 卡片头部\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.material-count-badge {\r\n  margin-right: 16px;\r\n}\r\n\r\n// 选中产品信息\r\n.selected-product-info {\r\n  margin-top: 16px;\r\n}\r\n\r\n// 物料搜索容器\r\n.material-search-container {\r\n  display: flex;\r\n  margin-bottom: 16px;\r\n  gap: 12px;\r\n  align-items: center;\r\n\r\n  .search-input {\r\n    flex: 1;\r\n  }\r\n}\r\n\r\n// 物料列表容器\r\n.material-list-container {\r\n  margin-top: 16px;\r\n  border: 1px solid #f0f0f0;\r\n  border-radius: @border-radius-base;\r\n  padding: 16px;\r\n  background-color: #fafafa;\r\n  \r\n  .count-input {\r\n    width: 90px;\r\n  }\r\n}\r\n\r\n// 物料名称单元格\r\n.material-name-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n\r\n  .material-name {\r\n    font-weight: 500;\r\n  }\r\n\r\n  .material-spec-tag {\r\n    margin-right: 0;\r\n    border-radius: 12px;\r\n  }\r\n}\r\n\r\n// 单位单元格\r\n.unit-cell {\r\n  .ant-tag {\r\n    padding: 0 8px;\r\n    border-radius: 12px;\r\n  }\r\n}\r\n\r\n// 数量单元格\r\n.count-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  \r\n  .unit-text {\r\n    color: rgba(0, 0, 0, 0.45);\r\n  }\r\n}\r\n\r\n// 操作单元格\r\n.action-cell {\r\n  .delete-btn {\r\n    border-radius: 50%;\r\n    width: 28px;\r\n    height: 28px;\r\n    padding: 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n// 物料汇总\r\n.material-summary {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 16px;\r\n  padding-top: 16px;\r\n  border-top: 1px dashed #e8e8e8;\r\n  \r\n  .summary-item {\r\n    margin-left: 24px;\r\n    \r\n    .summary-label {\r\n      color: rgba(0, 0, 0, 0.45);\r\n      margin-right: 8px;\r\n    }\r\n    \r\n    .summary-value {\r\n      font-weight: 600;\r\n      color: @primary-color;\r\n    }\r\n  }\r\n}\r\n\r\n// 状态开关容器\r\n.status-switch-container {\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .status-text {\r\n    margin-left: 16px;\r\n    font-size: 14px;\r\n\r\n    &.status-enabled {\r\n      color: @success-color;\r\n    }\r\n\r\n    &.status-disabled {\r\n      color: @error-color;\r\n    }\r\n  }\r\n}\r\n\r\n// 策略多选框组\r\n.strategy-checkbox-group {\r\n  width: 100%;\r\n\r\n  .strategy-checkbox {\r\n    display: block;\r\n    margin-bottom: 16px;\r\n    padding: 16px;\r\n    border: 1px solid #f0f0f0;\r\n    border-radius: @border-radius-base;\r\n    background-color: #fafafa;\r\n    transition: all @transition-duration;\r\n\r\n    &:hover {\r\n      border-color: @primary-color;\r\n      background-color: #f0f7ff;\r\n    }\r\n\r\n    &.ant-checkbox-wrapper-checked {\r\n      border-color: @primary-color;\r\n      background-color: #e6f7ff;\r\n      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);\r\n    }\r\n\r\n    .checkbox-content {\r\n      margin-left: 8px;\r\n\r\n      .checkbox-title {\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: rgba(0, 0, 0, 0.85);\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .checkbox-desc {\r\n        font-size: 12px;\r\n        color: rgba(0, 0, 0, 0.45);\r\n        line-height: 1.4;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 自定义选择器\r\n.custom-select {\r\n  width: 100%;\r\n}\r\n\r\n// 自定义开关\r\n.custom-switch {\r\n  &.ant-switch-checked {\r\n    background-color: @success-color;\r\n  }\r\n  \r\n  &:not(.ant-switch-checked) {\r\n    background-color: @error-color;\r\n  }\r\n}\r\n\r\n// 表单操作区\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 12px;\r\n  margin-top: 24px;\r\n  \r\n  .ant-btn {\r\n    min-width: 100px;\r\n  }\r\n}\r\n\r\n// 物料库存模态框\r\n.inventory-modal {\r\n  .modal-header {\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    margin-bottom: 20px;\r\n    color: @primary-color;\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    .anticon {\r\n      margin-right: 10px;\r\n      font-size: 20px;\r\n    }\r\n  }\r\n  \r\n  .inventory-search-container {\r\n    margin-bottom: 20px;\r\n    \r\n    .search-input {\r\n      width: 100%;\r\n    }\r\n  }\r\n  \r\n  .inventory-list-container {\r\n    max-height: 400px;\r\n    overflow-y: auto;\r\n    margin-bottom: 20px;\r\n    border: 1px solid #f0f0f0;\r\n    border-radius: @border-radius-base;\r\n    padding: 16px;\r\n    background-color: #fafafa;\r\n  }\r\n  \r\n  .inventory-name {\r\n    font-weight: 500;\r\n  }\r\n  \r\n  .quantity-value {\r\n    font-weight: 600;\r\n    color: @primary-color;\r\n  }\r\n  \r\n  .select-btn {\r\n    border-radius: 16px;\r\n  }\r\n  \r\n  .modal-footer {\r\n    margin-top: 16px;\r\n    text-align: right;\r\n  }\r\n}\r\n\r\n// 表格行样式\r\n.material-table-row {\r\n  transition: all @transition-duration;\r\n  \r\n  &:hover {\r\n    background-color: #f0f7ff;\r\n  }\r\n}\r\n\r\n.inventory-table-row {\r\n  transition: all @transition-duration;\r\n  \r\n  &:hover {\r\n    background-color: #f0f7ff;\r\n  }\r\n}\r\n\r\n// 库存搜索容器\r\n.inventory-search-container {\r\n  margin-bottom: 16px;\r\n  \r\n  .search-input {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.product-search-wrapper {\r\n  position: relative;\r\n  \r\n  .ant-select-auto-complete {\r\n    width: 100%;\r\n  }\r\n\r\n  .ant-input-suffix {\r\n    cursor: pointer;\r\n    color: @primary-color;\r\n    \r\n    &:hover {\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式调整\r\n@media screen and (max-width: 576px) {\r\n  .material-search-container {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .material-list-container {\r\n    overflow-x: auto;\r\n    padding: 12px 8px;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n    \r\n    .ant-btn {\r\n      width: 100%;\r\n    }\r\n  }\r\n  \r\n  .status-switch-container {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n    \r\n    .status-text {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}