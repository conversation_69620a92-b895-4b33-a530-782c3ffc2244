{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\material\\modules\\MaterialBillForm.vue?vue&type=template&id=03f1e51e&scoped=true&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\material\\modules\\MaterialBillForm.vue", "mtime": 1753844909036}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753423171139}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"a-spin\",\n    { attrs: { spinning: _vm.confirmLoading } },\n    [\n      _c(\n        \"j-form-container\",\n        { attrs: { disabled: _vm.formDisabled } },\n        [\n          _c(\n            \"a-form-model\",\n            {\n              ref: \"form\",\n              attrs: {\n                slot: \"detail\",\n                model: _vm.model,\n                rules: _vm.validatorRules\n              },\n              slot: \"detail\"\n            },\n            [\n              _c(\n                \"a-card\",\n                {\n                  staticClass: \"custom-card product-card\",\n                  attrs: { bordered: false }\n                },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-title\" },\n                    [\n                      _c(\"a-icon\", { attrs: { type: \"shop\" } }),\n                      _c(\"span\", [_vm._v(\"选择产品\")])\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-form-model-item\",\n                    { attrs: { prop: \"productId\" } },\n                    [\n                      _c(\n                        \"div\",\n                        { staticClass: \"product-search-wrapper\" },\n                        [\n                          _c(\n                            \"a-auto-complete\",\n                            {\n                              staticClass: \"custom-select\",\n                              attrs: {\n                                placeholder: \"请输入产品名称搜索\",\n                                dropdownMatchSelectWidth: false,\n                                size: \"large\",\n                                filterOption: false,\n                                defaultActiveFirstOption: false\n                              },\n                              on: {\n                                search: _vm.handleProductSearch,\n                                select: _vm.handleProductSelect\n                              },\n                              model: {\n                                value: _vm.model.productName,\n                                callback: function($$v) {\n                                  _vm.$set(_vm.model, \"productName\", $$v)\n                                },\n                                expression: \"model.productName\"\n                              }\n                            },\n                            [\n                              _c(\n                                \"template\",\n                                { slot: \"dataSource\" },\n                                [\n                                  _vm.loadingProducts\n                                    ? _c(\n                                        \"a-select-option\",\n                                        {\n                                          key: \"loading\",\n                                          attrs: { disabled: \"\" }\n                                        },\n                                        [\n                                          _c(\"a-icon\", {\n                                            attrs: { type: \"loading\", spin: \"\" }\n                                          }),\n                                          _vm._v(\n                                            \"\\n                  加载中...\\n                \"\n                                          )\n                                        ],\n                                        1\n                                      )\n                                    : _vm._e(),\n                                  _vm._l(_vm.filteredProducts, function(\n                                    product\n                                  ) {\n                                    return _c(\n                                      \"a-select-option\",\n                                      {\n                                        key: product.id,\n                                        attrs: { value: product.id }\n                                      },\n                                      [\n                                        _vm._v(\n                                          \"\\n                  \" +\n                                            _vm._s(product.name) +\n                                            \"\\n                \"\n                                        )\n                                      ]\n                                    )\n                                  })\n                                ],\n                                2\n                              ),\n                              _c(\n                                \"a-input\",\n                                [\n                                  _c(\"a-icon\", {\n                                    attrs: { slot: \"suffix\", type: \"search\" },\n                                    on: { click: _vm.loadProducts },\n                                    slot: \"suffix\"\n                                  })\n                                ],\n                                1\n                              )\n                            ],\n                            2\n                          )\n                        ],\n                        1\n                      ),\n                      _c(\"a-input\", {\n                        attrs: { type: \"hidden\" },\n                        model: {\n                          value: _vm.model.productId,\n                          callback: function($$v) {\n                            _vm.$set(_vm.model, \"productId\", $$v)\n                          },\n                          expression: \"model.productId\"\n                        }\n                      })\n                    ],\n                    1\n                  ),\n                  _vm.model.productId && _vm.model.productName\n                    ? _c(\n                        \"div\",\n                        { staticClass: \"selected-product-info\" },\n                        [\n                          _c(\n                            \"a-alert\",\n                            { attrs: { type: \"success\", \"show-icon\": \"\" } },\n                            [\n                              _c(\"template\", { slot: \"message\" }, [\n                                _vm._v(\"\\n              已选择产品: \"),\n                                _c(\"strong\", [\n                                  _vm._v(_vm._s(_vm.model.productName))\n                                ])\n                              ])\n                            ],\n                            2\n                          )\n                        ],\n                        1\n                      )\n                    : _vm._e()\n                ],\n                1\n              ),\n              _c(\n                \"a-card\",\n                {\n                  staticClass: \"custom-card name-card\",\n                  attrs: { bordered: false }\n                },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-title\" },\n                    [\n                      _c(\"a-icon\", { attrs: { type: \"edit\" } }),\n                      _c(\"span\", [_vm._v(\"物料清单名称\")])\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-form-model-item\",\n                    { attrs: { prop: \"materialBillName\" } },\n                    [\n                      _c(\n                        \"a-input\",\n                        {\n                          attrs: {\n                            placeholder: \"请输入物料清单名称\",\n                            size: \"large\",\n                            maxLength: 50,\n                            \"show-count\": \"\"\n                          },\n                          model: {\n                            value: _vm.model.materialBillName,\n                            callback: function($$v) {\n                              _vm.$set(_vm.model, \"materialBillName\", $$v)\n                            },\n                            expression: \"model.materialBillName\"\n                          }\n                        },\n                        [\n                          _c(\"a-icon\", {\n                            attrs: { slot: \"prefix\", type: \"file-text\" },\n                            slot: \"prefix\"\n                          })\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"a-card\",\n                {\n                  staticClass: \"custom-card material-card\",\n                  attrs: { bordered: false }\n                },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-header\" },\n                    [\n                      _c(\n                        \"div\",\n                        { staticClass: \"card-title\" },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"database\" } }),\n                          _c(\"span\", [_vm._v(\"物料清单\")])\n                        ],\n                        1\n                      ),\n                      _c(\"a-badge\", {\n                        staticClass: \"material-count-badge\",\n                        attrs: {\n                          count: _vm.materialList.length,\n                          overflowCount: 99\n                        }\n                      })\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"material-search-container\" },\n                    [\n                      _c(\n                        \"a-input-search\",\n                        {\n                          staticClass: \"search-input\",\n                          attrs: {\n                            placeholder: \"输入物料名称或单位进行模糊搜索\",\n                            \"enter-button\": \"\",\n                            size: \"large\"\n                          },\n                          on: { search: _vm.searchMaterial },\n                          model: {\n                            value: _vm.searchMaterialName,\n                            callback: function($$v) {\n                              _vm.searchMaterialName = $$v\n                            },\n                            expression: \"searchMaterialName\"\n                          }\n                        },\n                        [\n                          _c(\"a-icon\", {\n                            attrs: { slot: \"prefix\", type: \"search\" },\n                            slot: \"prefix\"\n                          })\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"a-tooltip\",\n                        { attrs: { title: \"从物料库中添加物料\" } },\n                        [\n                          _c(\n                            \"a-button\",\n                            {\n                              attrs: {\n                                type: \"primary\",\n                                icon: \"plus\",\n                                size: \"large\"\n                              },\n                              on: { click: _vm.searchMaterial }\n                            },\n                            [_vm._v(\"\\n              添加物料\\n            \")]\n                          )\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"material-list-container\" },\n                    [\n                      _vm.materialList.length === 0\n                        ? _c(\n                            \"a-empty\",\n                            {\n                              attrs: { description: \"暂无物料，请搜索并添加\" }\n                            },\n                            [\n                              _c(\n                                \"a-button\",\n                                {\n                                  attrs: { type: \"primary\" },\n                                  on: { click: _vm.searchMaterial }\n                                },\n                                [_vm._v(\"添加物料\")]\n                              )\n                            ],\n                            1\n                          )\n                        : _c(\"a-table\", {\n                            attrs: {\n                              columns: _vm.materialColumns,\n                              dataSource: _vm.materialList,\n                              pagination: false,\n                              rowKey: function(record) {\n                                return record.id || record.tempId\n                              },\n                              size: \"middle\",\n                              scroll: { x: 800 },\n                              bordered: true,\n                              rowClassName: \"material-table-row\"\n                            },\n                            scopedSlots: _vm._u([\n                              {\n                                key: \"materialName\",\n                                fn: function(text, record, index) {\n                                  return [\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"material-name-cell\" },\n                                      [\n                                        record.tempId\n                                          ? [\n                                              _c(\"a-input\", {\n                                                attrs: {\n                                                  placeholder: \"请输入物料名称\"\n                                                },\n                                                on: {\n                                                  change: _vm.updateMaterialJson\n                                                },\n                                                model: {\n                                                  value:\n                                                    _vm.materialList[index]\n                                                      .materialName,\n                                                  callback: function($$v) {\n                                                    _vm.$set(\n                                                      _vm.materialList[index],\n                                                      \"materialName\",\n                                                      $$v\n                                                    )\n                                                  },\n                                                  expression:\n                                                    \"materialList[index].materialName\"\n                                                }\n                                              })\n                                            ]\n                                          : [\n                                              _c(\n                                                \"span\",\n                                                {\n                                                  staticClass: \"material-name\"\n                                                },\n                                                [_vm._v(_vm._s(text))]\n                                              ),\n                                              record.imSpecs\n                                                ? _c(\n                                                    \"a-tag\",\n                                                    {\n                                                      staticClass:\n                                                        \"material-spec-tag\",\n                                                      attrs: { color: \"blue\" }\n                                                    },\n                                                    [\n                                                      _vm._v(\n                                                        _vm._s(record.imSpecs)\n                                                      )\n                                                    ]\n                                                  )\n                                                : _vm._e()\n                                            ]\n                                      ],\n                                      2\n                                    )\n                                  ]\n                                }\n                              },\n                              {\n                                key: \"materialUnit\",\n                                fn: function(text, record, index) {\n                                  return [\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"unit-cell\" },\n                                      [\n                                        record.tempId\n                                          ? [\n                                              _c(\"a-input\", {\n                                                attrs: {\n                                                  placeholder: \"请输入单位\"\n                                                },\n                                                on: {\n                                                  change: _vm.updateMaterialJson\n                                                },\n                                                model: {\n                                                  value:\n                                                    _vm.materialList[index]\n                                                      .materialUnit,\n                                                  callback: function($$v) {\n                                                    _vm.$set(\n                                                      _vm.materialList[index],\n                                                      \"materialUnit\",\n                                                      $$v\n                                                    )\n                                                  },\n                                                  expression:\n                                                    \"materialList[index].materialUnit\"\n                                                }\n                                              })\n                                            ]\n                                          : [\n                                              _c(\n                                                \"a-tag\",\n                                                { attrs: { color: \"cyan\" } },\n                                                [_vm._v(_vm._s(text || \"个\"))]\n                                              )\n                                            ]\n                                      ],\n                                      2\n                                    )\n                                  ]\n                                }\n                              },\n                              {\n                                key: \"materialCount\",\n                                fn: function(text, record, index) {\n                                  return [\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"count-cell\" },\n                                      [\n                                        _c(\"a-input-number\", {\n                                          staticClass: \"count-input\",\n                                          attrs: { min: 1, size: \"default\" },\n                                          on: {\n                                            change: function(value) {\n                                              return _vm.handleCountChange(\n                                                value,\n                                                index\n                                              )\n                                            }\n                                          },\n                                          model: {\n                                            value:\n                                              _vm.materialList[index]\n                                                .materialCount,\n                                            callback: function($$v) {\n                                              _vm.$set(\n                                                _vm.materialList[index],\n                                                \"materialCount\",\n                                                $$v\n                                              )\n                                            },\n                                            expression:\n                                              \"materialList[index].materialCount\"\n                                          }\n                                        }),\n                                        _c(\n                                          \"span\",\n                                          { staticClass: \"unit-text\" },\n                                          [\n                                            _vm._v(\n                                              _vm._s(\n                                                record.materialSpecifications ||\n                                                  \"个\"\n                                              )\n                                            )\n                                          ]\n                                        )\n                                      ],\n                                      1\n                                    )\n                                  ]\n                                }\n                              },\n                              {\n                                key: \"action\",\n                                fn: function(text, record, index) {\n                                  return [\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"action-cell\" },\n                                      [\n                                        _c(\n                                          \"a-button\",\n                                          {\n                                            staticClass: \"delete-btn\",\n                                            attrs: {\n                                              type: \"danger\",\n                                              size: \"small\"\n                                            },\n                                            on: {\n                                              click: function($event) {\n                                                return _vm.removeMaterial(index)\n                                              }\n                                            }\n                                          },\n                                          [\n                                            _c(\"a-icon\", {\n                                              attrs: { type: \"delete\" }\n                                            })\n                                          ],\n                                          1\n                                        )\n                                      ],\n                                      1\n                                    )\n                                  ]\n                                }\n                              }\n                            ])\n                          }),\n                      _vm.materialList.length > 0\n                        ? _c(\"div\", { staticClass: \"material-summary\" }, [\n                            _c(\"div\", { staticClass: \"summary-item\" }, [\n                              _c(\"span\", { staticClass: \"summary-label\" }, [\n                                _vm._v(\"总物料数:\")\n                              ]),\n                              _c(\"span\", { staticClass: \"summary-value\" }, [\n                                _vm._v(_vm._s(_vm.materialList.length))\n                              ])\n                            ])\n                          ])\n                        : _vm._e()\n                    ],\n                    1\n                  )\n                ]\n              ),\n              _c(\n                \"a-card\",\n                {\n                  staticClass: \"custom-card status-card\",\n                  attrs: { bordered: false }\n                },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-title\" },\n                    [\n                      _c(\"a-icon\", { attrs: { type: \"setting\" } }),\n                      _c(\"span\", [_vm._v(\"物料清单设置\")])\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-form-model-item\",\n                    {\n                      attrs: {\n                        label: \"物料清单状态\",\n                        labelCol: _vm.labelCol,\n                        wrapperCol: _vm.wrapperCol,\n                        prop: \"materialType\"\n                      }\n                    },\n                    [\n                      _c(\n                        \"div\",\n                        { staticClass: \"status-switch-container\" },\n                        [\n                          _c(\"a-switch\", {\n                            staticClass: \"custom-switch\",\n                            attrs: {\n                              checkedChildren: \"启用\",\n                              unCheckedChildren: \"禁用\",\n                              size: \"large\"\n                            },\n                            on: { change: _vm.onChose },\n                            model: {\n                              value: _vm.visibleCheck,\n                              callback: function($$v) {\n                                _vm.visibleCheck = $$v\n                              },\n                              expression: \"visibleCheck\"\n                            }\n                          }),\n                          _c(\n                            \"span\",\n                            {\n                              class: [\n                                \"status-text\",\n                                _vm.visibleCheck\n                                  ? \"status-enabled\"\n                                  : \"status-disabled\"\n                              ]\n                            },\n                            [\n                              _vm._v(\n                                \"\\n              \" +\n                                  _vm._s(\n                                    _vm.visibleCheck\n                                      ? \"清单已启用\"\n                                      : \"清单已禁用\"\n                                  ) +\n                                  \"\\n            \"\n                              )\n                            ]\n                          )\n                        ],\n                        1\n                      )\n                    ]\n                  ),\n                  _c(\n                    \"a-form-model-item\",\n                    {\n                      attrs: {\n                        label: \"库存用完处理策略\",\n                        labelCol: _vm.labelCol,\n                        wrapperCol: _vm.wrapperCol,\n                        prop: \"stockEmptyStrategy\"\n                      }\n                    },\n                    [\n                      _c(\n                        \"a-checkbox-group\",\n                        {\n                          staticClass: \"strategy-checkbox-group\",\n                          model: {\n                            value: _vm.stockEmptyStrategyArray,\n                            callback: function($$v) {\n                              _vm.stockEmptyStrategyArray = $$v\n                            },\n                            expression: \"stockEmptyStrategyArray\"\n                          }\n                        },\n                        [\n                          _c(\n                            \"a-checkbox\",\n                            {\n                              staticClass: \"strategy-checkbox\",\n                              staticStyle: { \"margin-left\": \"7px\" },\n                              attrs: { value: \"1\" }\n                            },\n                            [\n                              _c(\"div\", { staticClass: \"checkbox-content\" }, [\n                                _c(\"div\", { staticClass: \"checkbox-title\" }, [\n                                  _vm._v(\"自动禁用此物料清单\")\n                                ]),\n                                _c(\"div\", { staticClass: \"checkbox-desc\" }, [\n                                  _vm._v(\n                                    \"当前采购批次库存用完时，自动禁用此物料清单\"\n                                  )\n                                ])\n                              ])\n                            ]\n                          ),\n                          _c(\n                            \"a-checkbox\",\n                            {\n                              staticClass: \"strategy-checkbox\",\n                              attrs: { value: \"2\" }\n                            },\n                            [\n                              _c(\"div\", { staticClass: \"checkbox-content\" }, [\n                                _c(\"div\", { staticClass: \"checkbox-title\" }, [\n                                  _vm._v(\"自动切换为同成本物料\")\n                                ]),\n                                _c(\"div\", { staticClass: \"checkbox-desc\" }, [\n                                  _vm._v(\n                                    \"当前采购批次库存用完时，自动切换为同成本的其他物料\"\n                                  )\n                                ])\n                              ])\n                            ]\n                          )\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                ],\n                1\n              ),\n              !_vm.formDisabled\n                ? _c(\n                    \"div\",\n                    { staticClass: \"form-actions\" },\n                    [\n                      _c(\n                        \"a-button\",\n                        {\n                          attrs: { disabled: _vm.confirmLoading },\n                          on: { click: _vm.resetForm }\n                        },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"undo\" } }),\n                          _vm._v(\"重置\\n        \")\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"a-button\",\n                        {\n                          attrs: {\n                            type: \"primary\",\n                            loading: _vm.confirmLoading,\n                            disabled: _vm.materialList.length === 0\n                          },\n                          on: { click: _vm.submitForm }\n                        },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"check\" } }),\n                          _vm._v(\"保存\\n        \")\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                : _vm._e()\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\n        \"a-modal\",\n        {\n          staticClass: \"inventory-modal\",\n          attrs: {\n            title: \"搜索物料库存\",\n            visible: _vm.inventoryModalVisible,\n            width: 800,\n            footer: null,\n            destroyOnClose: true,\n            maskClosable: false\n          },\n          on: { cancel: _vm.closeInventoryModal }\n        },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"modal-header\" },\n            [\n              _c(\"a-icon\", { attrs: { type: \"database\", theme: \"filled\" } }),\n              _c(\"span\", [_vm._v(\"添加物料到清单\")])\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            { staticClass: \"inventory-search-container\" },\n            [\n              _c(\n                \"a-input-search\",\n                {\n                  staticClass: \"search-input\",\n                  attrs: {\n                    placeholder: \"输入物料名称或单位进行模糊搜索\",\n                    \"enter-button\": \"\",\n                    size: \"large\"\n                  },\n                  on: {\n                    search: _vm.searchInventory,\n                    pressEnter: _vm.searchInventory\n                  },\n                  model: {\n                    value: _vm.inventorySearchName,\n                    callback: function($$v) {\n                      _vm.inventorySearchName = $$v\n                    },\n                    expression: \"inventorySearchName\"\n                  }\n                },\n                [\n                  _c(\"a-icon\", {\n                    attrs: { slot: \"prefix\", type: \"search\" },\n                    slot: \"prefix\"\n                  })\n                ],\n                1\n              )\n            ],\n            1\n          ),\n          _c(\"a-spin\", { attrs: { spinning: _vm.loadingInventory } }, [\n            _c(\n              \"div\",\n              { staticClass: \"inventory-list-container\" },\n              [\n                _vm.inventoryList.length === 0 && !_vm.loadingInventory\n                  ? _c(\"a-empty\", {\n                      attrs: { description: \"未找到物料，请尝试其他关键词\" }\n                    })\n                  : _c(\"a-table\", {\n                      attrs: {\n                        columns: _vm.inventoryColumns,\n                        dataSource: _vm.inventoryList,\n                        pagination: {\n                          pageSize: 5,\n                          showQuickJumper: true,\n                          showSizeChanger: true\n                        },\n                        rowKey: function(record) {\n                          return record.id\n                        },\n                        size: \"middle\",\n                        bordered: true,\n                        rowClassName: \"inventory-table-row\"\n                      },\n                      scopedSlots: _vm._u([\n                        {\n                          key: \"imItemName\",\n                          fn: function(text) {\n                            return [\n                              _c(\"span\", { staticClass: \"inventory-name\" }, [\n                                _vm._v(_vm._s(text))\n                              ])\n                            ]\n                          }\n                        },\n                        {\n                          key: \"imUnit\",\n                          fn: function(text) {\n                            return [\n                              _c(\"a-tag\", { attrs: { color: \"blue\" } }, [\n                                _vm._v(_vm._s(text || \"个\"))\n                              ])\n                            ]\n                          }\n                        },\n                        {\n                          key: \"imQuantity\",\n                          fn: function(text) {\n                            return [\n                              _c(\"span\", { staticClass: \"quantity-value\" }, [\n                                _vm._v(_vm._s(text))\n                              ])\n                            ]\n                          }\n                        },\n                        {\n                          key: \"purchaseBatch\",\n                          fn: function(text) {\n                            return [\n                              _c(\"a-tag\", { attrs: { color: \"green\" } }, [\n                                _vm._v(_vm._s(text || \"批次-001\"))\n                              ])\n                            ]\n                          }\n                        },\n                        {\n                          key: \"purchaseTime\",\n                          fn: function(text) {\n                            return [\n                              _c(\"span\", { staticClass: \"purchase-time\" }, [\n                                _vm._v(_vm._s(text || \"2025-01-01\"))\n                              ])\n                            ]\n                          }\n                        },\n                        {\n                          key: \"purchaseUnitCost\",\n                          fn: function(text) {\n                            return [\n                              _c(\"span\", { staticClass: \"unit-cost\" }, [\n                                _vm._v(\"¥\" + _vm._s(text || \"0.00\"))\n                              ])\n                            ]\n                          }\n                        },\n                        {\n                          key: \"action\",\n                          fn: function(text, record) {\n                            return [\n                              _c(\n                                \"a-button\",\n                                {\n                                  staticClass: \"select-btn\",\n                                  attrs: { type: \"primary\", size: \"small\" },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.selectInventoryItem(record)\n                                    }\n                                  }\n                                },\n                                [\n                                  _c(\"a-icon\", { attrs: { type: \"plus\" } }),\n                                  _vm._v(\"添加\\n            \")\n                                ],\n                                1\n                              )\n                            ]\n                          }\n                        }\n                      ])\n                    })\n              ],\n              1\n            )\n          ]),\n          _c(\n            \"div\",\n            { staticClass: \"modal-footer\" },\n            [\n              _c(\"a-button\", { on: { click: _vm.closeInventoryModal } }, [\n                _vm._v(\"关闭\")\n              ])\n            ],\n            1\n          )\n        ],\n        1\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}