{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\material\\modules\\MaterialBillForm.vue?vue&type=template&id=03f1e51e&scoped=true&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\material\\modules\\MaterialBillForm.vue", "mtime": 1753844909036}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753423171139}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["\n<a-spin :spinning=\"confirmLoading\">\n  <j-form-container :disabled=\"formDisabled\">\n    <a-form-model ref=\"form\" :model=\"model\" :rules=\"validatorRules\" slot=\"detail\">\n      <!-- 产品选择卡片 -->\n      <a-card class=\"custom-card product-card\" :bordered=\"false\">\n        <div class=\"card-title\">\n          <a-icon type=\"shop\" />\n          <span>选择产品</span>\n        </div>\n        <a-form-model-item prop=\"productId\">\n          <!-- 产品搜索选择器 -->\n          <div class=\"product-search-wrapper\">\n            <a-auto-complete\n              v-model=\"model.productName\"\n              placeholder=\"请输入产品名称搜索\"\n              :dropdownMatchSelectWidth=\"false\"\n              @search=\"handleProductSearch\"\n              @select=\"handleProductSelect\"\n              class=\"custom-select\"\n              size=\"large\"\n              :filterOption=\"false\"\n              :defaultActiveFirstOption=\"false\"\n            >\n              <template slot=\"dataSource\">\n                <a-select-option v-if=\"loadingProducts\" disabled key=\"loading\">\n                  <a-icon type=\"loading\" spin />\n                  加载中...\n                </a-select-option>\n                <a-select-option\n                  v-for=\"product in filteredProducts\"\n                  :key=\"product.id\"\n                  :value=\"product.id\"\n                >\n                  {{ product.name }}\n                </a-select-option>\n              </template>\n              <a-input>\n                <a-icon slot=\"suffix\" type=\"search\" @click=\"loadProducts\" />\n              </a-input>\n            </a-auto-complete>\n          </div>\n          \n          <!-- 隐藏的产品ID字段，用于表单提交 -->\n          <a-input type=\"hidden\" v-model=\"model.productId\" />\n        </a-form-model-item>\n        \n        <div v-if=\"model.productId && model.productName\" class=\"selected-product-info\">\n          <a-alert type=\"success\" show-icon>\n            <template slot=\"message\">\n              已选择产品: <strong>{{ model.productName }}</strong>\n            </template>\n          </a-alert>\n        </div>\n      </a-card>\n\n      <!-- 物料清单名称卡片 -->\n      <a-card class=\"custom-card name-card\" :bordered=\"false\">\n        <div class=\"card-title\">\n          <a-icon type=\"edit\" />\n          <span>物料清单名称</span>\n        </div>\n        <a-form-model-item prop=\"materialBillName\">\n          <a-input\n            v-model=\"model.materialBillName\"\n            placeholder=\"请输入物料清单名称\"\n            size=\"large\"\n            :maxLength=\"50\"\n            show-count\n          >\n            <a-icon slot=\"prefix\" type=\"file-text\" />\n          </a-input>\n        </a-form-model-item>\n      </a-card>\n\n      <!-- 物料清单卡片 -->\n      <a-card class=\"custom-card material-card\" :bordered=\"false\">\n        <div class=\"card-header\">\n          <div class=\"card-title\">\n            <a-icon type=\"database\" />\n            <span>物料清单</span>\n          </div>\n          <a-badge :count=\"materialList.length\" :overflowCount=\"99\" class=\"material-count-badge\" />\n        </div>\n\n        <div class=\"material-search-container\">\n          <a-input-search\n            placeholder=\"输入物料名称或单位进行模糊搜索\"\n            v-model=\"searchMaterialName\"\n            @search=\"searchMaterial\"\n            enter-button\n            class=\"search-input\"\n            size=\"large\"\n          >\n            <a-icon slot=\"prefix\" type=\"search\" />\n          </a-input-search>\n          \n          <a-tooltip title=\"从物料库中添加物料\">\n            <a-button type=\"primary\" icon=\"plus\" @click=\"searchMaterial\" size=\"large\">\n              添加物料\n            </a-button>\n          </a-tooltip>\n        </div>\n\n        <div class=\"material-list-container\">\n          <a-empty v-if=\"materialList.length === 0\" description=\"暂无物料，请搜索并添加\">\n            <a-button type=\"primary\" @click=\"searchMaterial\">添加物料</a-button>\n          </a-empty>\n          <a-table\n            v-else\n            :columns=\"materialColumns\"\n            :dataSource=\"materialList\"\n            :pagination=\"false\"\n            :rowKey=\"record => record.id || record.tempId\"\n            size=\"middle\"\n            :scroll=\"{ x: 800 }\"\n            :bordered=\"true\"\n            :rowClassName=\"'material-table-row'\"\n          >\n            <!-- 物料名称列 -->\n            <template slot=\"materialName\" slot-scope=\"text, record, index\">\n              <div class=\"material-name-cell\">\n                <template v-if=\"record.tempId\">\n                  <a-input\n                    v-model=\"materialList[index].materialName\"\n                    placeholder=\"请输入物料名称\"\n                    @change=\"updateMaterialJson\"\n                  />\n                </template>\n                <template v-else>\n                  <span class=\"material-name\">{{ text }}</span>\n                  <a-tag v-if=\"record.imSpecs\" color=\"blue\" class=\"material-spec-tag\">{{ record.imSpecs }}</a-tag>\n                </template>\n              </div>\n            </template>\n\n            <!-- 单位列 -->\n            <template slot=\"materialUnit\" slot-scope=\"text, record, index\">\n              <div class=\"unit-cell\">\n                <template v-if=\"record.tempId\">\n                  <a-input\n                    v-model=\"materialList[index].materialUnit\"\n                    placeholder=\"请输入单位\"\n                    @change=\"updateMaterialJson\"\n                  />\n                </template>\n                <template v-else>\n                  <a-tag color=\"cyan\">{{ text || '个' }}</a-tag>\n                </template>\n              </div>\n            </template>\n\n            <!-- 数量列 -->\n            <template slot=\"materialCount\" slot-scope=\"text, record, index\">\n              <div class=\"count-cell\">\n                <a-input-number\n                  v-model=\"materialList[index].materialCount\"\n                  :min=\"1\"\n                  @change=\"value => handleCountChange(value, index)\"\n                  class=\"count-input\"\n                  size=\"default\"\n                />\n                <span class=\"unit-text\">{{ record.materialSpecifications || '个' }}</span>\n              </div>\n            </template>\n\n            <!-- 操作列 -->\n            <template slot=\"action\" slot-scope=\"text, record, index\">\n              <div class=\"action-cell\">\n                <a-button type=\"danger\" size=\"small\" @click=\"removeMaterial(index)\" class=\"delete-btn\">\n                  <a-icon type=\"delete\" />\n                </a-button>\n              </div>\n            </template>\n          </a-table>\n          \n          <div v-if=\"materialList.length > 0\" class=\"material-summary\">\n            <div class=\"summary-item\">\n              <span class=\"summary-label\">总物料数:</span>\n              <span class=\"summary-value\">{{ materialList.length }}</span>\n            </div>\n          </div>\n        </div>\n      </a-card>\n\n      <!-- 物料清单设置卡片 -->\n      <a-card class=\"custom-card status-card\" :bordered=\"false\">\n        <div class=\"card-title\">\n          <a-icon type=\"setting\" />\n          <span>物料清单设置</span>\n        </div>\n        <a-form-model-item label=\"物料清单状态\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"materialType\">\n          <div class=\"status-switch-container\">\n            <a-switch\n              checkedChildren=\"启用\"\n              unCheckedChildren=\"禁用\"\n              @change=\"onChose\"\n              v-model=\"visibleCheck\"\n              class=\"custom-switch\"\n              size=\"large\"\n            />\n            <span :class=\"['status-text', visibleCheck ? 'status-enabled' : 'status-disabled']\">\n              {{ visibleCheck ? '清单已启用' : '清单已禁用' }}\n            </span>\n          </div>\n        </a-form-model-item>\n\n        <a-form-model-item label=\"库存用完处理策略\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"stockEmptyStrategy\">\n          <a-checkbox-group v-model=\"stockEmptyStrategyArray\" class=\"strategy-checkbox-group\">\n            <a-checkbox value=\"1\" class=\"strategy-checkbox\" style=\"margin-left: 7px;\">\n              <div class=\"checkbox-content\">\n                <div class=\"checkbox-title\">自动禁用此物料清单</div>\n                <div class=\"checkbox-desc\">当前采购批次库存用完时，自动禁用此物料清单</div>\n              </div>\n            </a-checkbox>\n            <a-checkbox value=\"2\" class=\"strategy-checkbox\">\n              <div class=\"checkbox-content\">\n                <div class=\"checkbox-title\">自动切换为同成本物料</div>\n                <div class=\"checkbox-desc\">当前采购批次库存用完时，自动切换为同成本的其他物料</div>\n              </div>\n            </a-checkbox>\n          </a-checkbox-group>\n        </a-form-model-item>\n      </a-card>\n      \n      <!-- 提交按钮区域 -->\n      <div class=\"form-actions\" v-if=\"!formDisabled\">\n        <a-button @click=\"resetForm\" :disabled=\"confirmLoading\">\n          <a-icon type=\"undo\" />重置\n        </a-button>\n        <a-button type=\"primary\" @click=\"submitForm\" :loading=\"confirmLoading\" :disabled=\"materialList.length === 0\">\n          <a-icon type=\"check\" />保存\n        </a-button>\n      </div>\n    </a-form-model>\n  </j-form-container>\n\n  <!-- 物料搜索弹窗 -->\n  <a-modal\n    title=\"搜索物料库存\"\n    :visible=\"inventoryModalVisible\"\n    :width=\"800\"\n    @cancel=\"closeInventoryModal\"\n    :footer=\"null\"\n    :destroyOnClose=\"true\"\n    :maskClosable=\"false\"\n    class=\"inventory-modal\"\n  >\n    <div class=\"modal-header\">\n      <a-icon type=\"database\" theme=\"filled\" />\n      <span>添加物料到清单</span>\n    </div>\n    \n    <div class=\"inventory-search-container\">\n      <a-input-search\n        placeholder=\"输入物料名称或单位进行模糊搜索\"\n        v-model=\"inventorySearchName\"\n        @search=\"searchInventory\"\n        enter-button\n        class=\"search-input\"\n        size=\"large\"\n        @pressEnter=\"searchInventory\"\n      >\n        <a-icon slot=\"prefix\" type=\"search\" />\n      </a-input-search>\n    </div>\n\n    <a-spin :spinning=\"loadingInventory\">\n      <div class=\"inventory-list-container\">\n        <a-empty v-if=\"inventoryList.length === 0 && !loadingInventory\" description=\"未找到物料，请尝试其他关键词\" />\n        <a-table\n          v-else\n          :columns=\"inventoryColumns\"\n          :dataSource=\"inventoryList\"\n          :pagination=\"{ pageSize: 5, showQuickJumper: true, showSizeChanger: true }\"\n          :rowKey=\"record => record.id\"\n          size=\"middle\"\n          :bordered=\"true\"\n          :rowClassName=\"'inventory-table-row'\"\n        >\n          <template slot=\"imItemName\" slot-scope=\"text\">\n            <span class=\"inventory-name\">{{ text }}</span>\n          </template>\n\n          <template slot=\"imUnit\" slot-scope=\"text\">\n            <a-tag color=\"blue\">{{ text || '个' }}</a-tag>\n          </template>\n\n          <template slot=\"imQuantity\" slot-scope=\"text\">\n            <span class=\"quantity-value\">{{ text }}</span>\n          </template>\n\n          <template slot=\"purchaseBatch\" slot-scope=\"text\">\n            <a-tag color=\"green\">{{ text || '批次-001' }}</a-tag>\n          </template>\n\n          <template slot=\"purchaseTime\" slot-scope=\"text\">\n            <span class=\"purchase-time\">{{ text || '2025-01-01' }}</span>\n          </template>\n\n          <template slot=\"purchaseUnitCost\" slot-scope=\"text\">\n            <span class=\"unit-cost\">¥{{ text || '0.00' }}</span>\n          </template>\n\n          <template slot=\"action\" slot-scope=\"text, record\">\n            <a-button type=\"primary\" size=\"small\" @click=\"selectInventoryItem(record)\" class=\"select-btn\">\n              <a-icon type=\"plus\" />添加\n            </a-button>\n          </template>\n        </a-table>\n      </div>\n    </a-spin>\n    \n    <div class=\"modal-footer\">\n      <a-button @click=\"closeInventoryModal\">关闭</a-button>\n    </div>\n  </a-modal>\n</a-spin>\n", null]}