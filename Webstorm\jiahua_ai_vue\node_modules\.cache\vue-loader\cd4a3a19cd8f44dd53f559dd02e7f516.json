{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\modules\\InventoryDetailModal.vue?vue&type=template&id=38b42cac&scoped=true&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\modules\\InventoryDetailModal.vue", "mtime": 1753844307810}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753423171139}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["\n<a-modal\n  :title=\"modalTitle\"\n  :width=\"800\"\n  :visible=\"visible\"\n  :confirmLoading=\"confirmLoading\"\n  @ok=\"handleOk\"\n  @cancel=\"handleCancel\"\n  destroyOnClose\n  class=\"inventory-detail-modal\"\n>\n  <a-spin :spinning=\"loading\">\n    <div class=\"detail-content\">\n      <!-- 基本信息 -->\n      <div class=\"detail-section\">\n        <div class=\"section-title\">\n          <a-icon :type=\"record.imInventoryType === '1' ? 'database' : 'appstore'\" />\n          <span>{{ record.imInventoryType === '1' ? '物料信息' : '产品信息' }}</span>\n        </div>\n        <div class=\"info-grid\">\n          <div class=\"info-item\">\n            <div class=\"info-label\">{{ record.imInventoryType === '1' ? '物料名称' : '产品名称' }}</div>\n            <div class=\"info-value\">{{ record.imInventoryType === '1' ? record.imItemName : record.imProductName }}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">单位</div>\n            <div class=\"info-value\">{{ record.imItemUnit || '-' }}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">{{ clickType === 'inventory' ? '总库存数量' : '可用库存数量' }}</div>\n            <div class=\"info-value highlight\">{{ clickType === 'inventory' ? record.imInventoryQuantity : record.imAvailableStock }}</div>\n          </div>\n          <div class=\"info-item\">\n            <div class=\"info-label\">{{ record.imInventoryType === '1' ? '平均单价' : '平均成本' }}</div>\n            <div class=\"info-value price\">¥{{ formatNumber(record.imInventoryType === '1' ? record.imItemPrice : record.imCost) }}</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 物料详情 - 采购信息 -->\n      <div v-if=\"record.imInventoryType === '1'\" class=\"detail-section\">\n        <div class=\"section-title\">\n          <a-icon type=\"shopping\" />\n          <span>采购详情</span>\n        </div>\n        <a-table\n          :columns=\"purchaseColumns\"\n          :dataSource=\"purchaseDetails\"\n          :pagination=\"false\"\n          size=\"small\"\n          :loading=\"detailLoading\"\n          :locale=\"{ emptyText: '暂无采购记录数据' }\"\n          :scroll=\"{ y: 250 }\"\n        >\n          <template slot=\"cost\" slot-scope=\"text\">\n            <span class=\"price-text\">¥{{ formatNumber(text) }}</span>\n          </template>\n          <template slot=\"quantity\" slot-scope=\"text\">\n            <span class=\"quantity-text\">{{ text }}</span>\n          </template>\n        </a-table>\n      </div>\n\n      <!-- 产品详情 - 产品成本 -->\n      <div v-if=\"record.imInventoryType === '2'\" class=\"detail-section\">\n        <div class=\"section-title\">\n          <a-icon type=\"calculator\" />\n          <span>产品成本（根据物料清单）</span>\n        </div>\n        <div v-if=\"productCostLoading\" class=\"loading-container\">\n          <a-spin size=\"large\" />\n        </div>\n        <div v-else-if=\"productCost.materialDetails && productCost.materialDetails.length > 0\">\n          <div class=\"cost-summary\">\n            <div class=\"cost-item\">\n              <span class=\"cost-label\">物料清单：</span>\n              <span class=\"cost-value\">{{ productCost.materialBillName || '未命名' }}</span>\n            </div>\n            <div class=\"cost-item\">\n              <span class=\"cost-label\">总成本：</span>\n              <span class=\"cost-value price\">¥{{ formatNumber(productCost.totalCost) }}</span>\n            </div>\n          </div>\n          <a-table\n            :columns=\"materialCostColumns\"\n            :dataSource=\"productCost.materialDetails\"\n            :pagination=\"false\"\n            size=\"small\"\n            :locale=\"{ emptyText: '暂无物料成本数据' }\"\n            :scroll=\"{ y: 150 }\"\n          >\n            <template slot=\"cost\" slot-scope=\"text\">\n              <span class=\"price-text\">¥{{ formatNumber(text) }}</span>\n            </template>\n            <template slot=\"unitPrice\" slot-scope=\"text\">\n              <span class=\"price-text\">¥{{ formatNumber(text) }}</span>\n            </template>\n          </a-table>\n        </div>\n        <div v-else class=\"empty-state\">\n          <a-empty description=\"暂无物料清单成本数据\" />\n        </div>\n      </div>\n\n      <!-- 产品详情 - 生产信息 -->\n      <div v-if=\"record.imInventoryType === '2'\" class=\"detail-section\">\n        <div class=\"section-title\">\n          <a-icon type=\"tool\" />\n          <span>生产批次详情</span>\n        </div>\n        <a-table\n          :columns=\"productionColumns\"\n          :dataSource=\"productionDetails\"\n          :pagination=\"false\"\n          size=\"small\"\n          :loading=\"detailLoading\"\n          :locale=\"{ emptyText: '暂无生产记录数据' }\"\n          :scroll=\"{ y: 250 }\"\n        >\n          <template slot=\"cost\" slot-scope=\"text\">\n            <span class=\"price-text\">¥{{ formatNumber(text) }}</span>\n          </template>\n          <template slot=\"quantity\" slot-scope=\"text\">\n            <span class=\"quantity-text\">{{ text }}</span>\n          </template>\n          <template slot=\"priority\" slot-scope=\"text, record\">\n            <a-checkbox\n              :checked=\"record.prioritySale\"\n              @change=\"(e) => handlePriorityChange(record, e.target.checked)\"\n            >\n              优先出售此批次\n            </a-checkbox>\n          </template>\n        </a-table>\n      </div>\n    </div>\n  </a-spin>\n</a-modal>\n", null]}