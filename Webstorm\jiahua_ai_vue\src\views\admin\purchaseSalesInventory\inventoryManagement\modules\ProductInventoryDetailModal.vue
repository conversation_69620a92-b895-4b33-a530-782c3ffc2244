<template>
  <a-modal
    :title="modalTitle"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    destroyOnClose
    class="product-inventory-detail-modal"
  >
    <a-spin :spinning="loading">
      <div class="detail-content">
        <!-- 基本信息 -->
        <div class="detail-section">
          <div class="section-title">
            <a-icon type="appstore" />
            <span>产品信息</span>
          </div>
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">产品名称</div>
              <div class="info-value">{{ record.imProductName }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">单位</div>
              <div class="info-value">{{ record.imItemUnit || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">{{ clickType === 'inventory' ? '总库存数量' : '可用库存数量' }}</div>
              <div class="info-value highlight">{{ clickType === 'inventory' ? record.imInventoryQuantity : record.imAvailableStock }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">平均单价成本</div>
              <div class="info-value price">¥{{ formatNumber(record.imCost) }}</div>
            </div>
          </div>
        </div>

        <!-- 产品成本详情 -->
        <div class="detail-section">
          <div class="section-title">
            <a-icon type="calculator" />
            <span>产品成本（根据物料清单）</span>
          </div>
          <div v-if="productCostLoading" class="loading-container">
            <a-spin size="large" />
          </div>
          <div v-else-if="productCost.materialDetails && productCost.materialDetails.length > 0">
            <div class="cost-summary">
              <div class="cost-item">
                <span class="cost-label">物料清单：</span>
                <span class="cost-value">{{ productCost.materialBillName || '未命名' }}</span>
              </div>
              <div class="cost-item">
                <span class="cost-label">总成本：</span>
                <span class="cost-value price">¥{{ formatNumber(productCost.totalCost) }}</span>
              </div>
            </div>
            <a-table
              :columns="materialCostColumns"
              :dataSource="productCost.materialDetails"
              :pagination="false"
              size="small"
              :locale="{ emptyText: '暂无物料成本数据' }"
              :scroll="{ y: 200 }"
            >
              <template slot="cost" slot-scope="text">
                <span class="price-text">¥{{ formatNumber(text) }}</span>
              </template>
              <template slot="unitPrice" slot-scope="text">
                <span class="price-text">¥{{ formatNumber(text) }}</span>
              </template>
            </a-table>
          </div>
          <div v-else class="empty-state">
            <a-empty description="暂无物料清单成本数据" />
          </div>
        </div>

        <!-- 生产批次详情 -->
        <div class="detail-section">
          <div class="section-title">
            <a-icon type="tool" />
            <span>生产批次详情</span>
          </div>
          <a-table
            :columns="productionColumns"
            :dataSource="productionDetails"
            :pagination="false"
            size="small"
            :loading="detailLoading"
            :locale="{ emptyText: '暂无生产记录数据' }"
            :scroll="{ y: 250 }"
          >
            <template slot="cost" slot-scope="text">
              <span class="price-text">¥{{ formatNumber(text) }}</span>
            </template>
            <template slot="quantity" slot-scope="text">
              <span class="quantity-text">{{ text }}</span>
            </template>
            <template slot="priority" slot-scope="text, record">
              <a-checkbox 
                :checked="record.prioritySale" 
                @change="(e) => handlePriorityChange(record, e.target.checked)"
              >
                优先出售此批次
              </a-checkbox>
            </template>
          </a-table>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'ProductInventoryDetailModal',
  data() {
    return {
      visible: false,
      confirmLoading: false,
      loading: false,
      detailLoading: false,
      productCostLoading: false,
      record: {},
      clickType: 'inventory', // 'inventory' 或 'available'
      productionDetails: [],
      productCost: {
        totalCost: 0,
        materialDetails: []
      },
      
      // 物料成本表格列
      materialCostColumns: [
        {
          title: '物料名称',
          dataIndex: 'materialName',
          key: 'materialName',
          width: 150
        },
        {
          title: '用量',
          dataIndex: 'materialCount',
          key: 'materialCount',
          width: 80
        },
        {
          title: '单位/规格',
          dataIndex: 'materialUnit',
          key: 'materialUnit',
          width: 120
        },
        {
          title: '单价',
          dataIndex: 'unitPrice',
          key: 'unitPrice',
          width: 100,
          scopedSlots: { customRender: 'unitPrice' }
        },
        {
          title: '成本',
          dataIndex: 'materialCost',
          key: 'materialCost',
          width: 100,
          scopedSlots: { customRender: 'cost' }
        }
      ],
      
      // 生产详情表格列
      productionColumns: [
        {
          title: '生产批次（生产单号）',
          dataIndex: 'productionOrderNo',
          key: 'productionOrderNo',
          width: 180
        },
        {
          title: '数量',
          dataIndex: 'quantity',
          key: 'quantity',
          width: 80,
          scopedSlots: { customRender: 'quantity' }
        },
        {
          title: '批次成本',
          dataIndex: 'batchCost',
          key: 'batchCost',
          width: 120,
          scopedSlots: { customRender: 'cost' }
        },
        {
          title: '生产日期',
          dataIndex: 'productionDate',
          key: 'productionDate',
          width: 120
        },
        {
          title: '优先出售',
          dataIndex: 'prioritySale',
          key: 'prioritySale',
          width: 120,
          scopedSlots: { customRender: 'priority' }
        }
      ]
    }
  },
  computed: {
    modalTitle() {
      return this.clickType === 'inventory' ? '产品库存详情' : '产品可用库存详情'
    }
  },
  methods: {
    show(record, clickType = 'inventory') {
      this.visible = true
      this.record = record
      this.clickType = clickType
      this.loadDetails()
    },
    
    loadDetails() {
      this.detailLoading = true
      this.productCostLoading = true
      
      // 同时加载生产详情和产品成本
      Promise.all([
        this.loadProductionDetails(),
        this.loadProductCost()
      ]).finally(() => {
        this.detailLoading = false
        this.productCostLoading = false
      })
    },
    
    loadProductionDetails() {
      const url = '/admin/inventoryManagement/getProductionDetails'
      return getAction(url, { inventoryId: this.record.id }).then(res => {
        if (res.success) {
          this.productionDetails = res.result || []
        } else {
          this.$message.error('获取生产详情失败')
        }
      }).catch(() => {
        this.$message.error('获取生产详情失败')
      })
    },
    
    loadProductCost() {
      const url = '/admin/inventoryManagement/getProductCost'
      return getAction(url, { productName: this.record.imProductName }).then(res => {
        if (res.success) {
          this.productCost = res.result || { totalCost: 0, materialDetails: [] }
        } else {
          this.$message.error('获取产品成本失败')
        }
      }).catch(() => {
        this.$message.error('获取产品成本失败')
      })
    },
    
    handlePriorityChange(record, checked) {
      const url = '/admin/inventoryManagement/updatePrioritySale'
      postAction(url, { 
        batchId: record.id, 
        prioritySale: checked 
      }).then(res => {
        if (res.success) {
          record.prioritySale = checked
          this.$message.success('设置成功')
        } else {
          this.$message.error('设置失败')
        }
      }).catch(() => {
        this.$message.error('设置失败')
      })
    },
    
    handleOk() {
      this.visible = false
    },
    
    handleCancel() {
      this.visible = false
    },
    
    formatNumber(num) {
      if (!num || isNaN(num)) return '0.00'
      return parseFloat(num).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
    }
  }
}
</script>

<style lang="less" scoped>
.product-inventory-detail-modal {
  .detail-content {
    .detail-section {
      margin-bottom: 24px;
      
      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        
        .anticon {
          margin-right: 8px;
          color: #1890ff;
        }
      }
      
      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 16px;
        
        .info-item {
          .info-label {
            color: #8c8c8c;
            font-size: 14px;
            margin-bottom: 8px;
          }
          
          .info-value {
            color: #262626;
            font-size: 16px;
            
            &.highlight {
              color: #1890ff;
              font-weight: 600;
              font-size: 18px;
            }
            
            &.price {
              color: #52c41a;
              font-weight: 600;
            }
          }
        }
      }
      
      .cost-summary {
        background: #f5f5f5;
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 16px;
        
        .cost-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .cost-label {
            color: #8c8c8c;
            font-size: 14px;
          }
          
          .cost-value {
            color: #262626;
            font-weight: 500;
            
            &.price {
              color: #52c41a;
              font-weight: 600;
              font-size: 16px;
            }
          }
        }
      }
      
      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
      }
      
      .empty-state {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
      }
    }
  }
  
  .price-text {
    color: #52c41a;
    font-weight: 600;
  }
  
  .quantity-text {
    color: #262626;
    font-weight: 500;
  }
}

@media (max-width: 768px) {
  .product-inventory-detail-modal {
    .detail-content {
      .detail-section {
        .info-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>
