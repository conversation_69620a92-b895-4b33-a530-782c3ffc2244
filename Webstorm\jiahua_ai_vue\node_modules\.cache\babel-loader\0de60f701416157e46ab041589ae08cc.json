{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\babel-loader\\lib\\index.js!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\modules\\ProductInventoryDetailModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\modules\\ProductInventoryDetailModal.vue", "mtime": 1753844673456}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753423167852}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { getAction, postAction } from '@/api/manage';\nexport default {\n  name: 'ProductInventoryDetailModal',\n  data: function data() {\n    return {\n      visible: false,\n      confirmLoading: false,\n      loading: false,\n      detailLoading: false,\n      productCostLoading: false,\n      record: {},\n      clickType: 'inventory',\n      // 'inventory' 或 'available'\n      productionDetails: [],\n      productCost: {\n        totalCost: 0,\n        materialDetails: []\n      },\n      // 物料成本表格列\n      materialCostColumns: [{\n        title: '物料名称',\n        dataIndex: 'materialName',\n        key: 'materialName',\n        width: 150\n      }, {\n        title: '用量',\n        dataIndex: 'materialCount',\n        key: 'materialCount',\n        width: 80\n      }, {\n        title: '单位/规格',\n        dataIndex: 'materialUnit',\n        key: 'materialUnit',\n        width: 120\n      }, {\n        title: '单价',\n        dataIndex: 'unitPrice',\n        key: 'unitPrice',\n        width: 100,\n        scopedSlots: {\n          customRender: 'unitPrice'\n        }\n      }, {\n        title: '成本',\n        dataIndex: 'materialCost',\n        key: 'materialCost',\n        width: 100,\n        scopedSlots: {\n          customRender: 'cost'\n        }\n      }],\n      // 生产详情表格列\n      productionColumns: [{\n        title: '生产批次（生产单号）',\n        dataIndex: 'productionOrderNo',\n        key: 'productionOrderNo',\n        width: 180\n      }, {\n        title: '数量',\n        dataIndex: 'quantity',\n        key: 'quantity',\n        width: 80,\n        scopedSlots: {\n          customRender: 'quantity'\n        }\n      }, {\n        title: '批次成本',\n        dataIndex: 'batchCost',\n        key: 'batchCost',\n        width: 120,\n        scopedSlots: {\n          customRender: 'cost'\n        }\n      }, {\n        title: '生产日期',\n        dataIndex: 'productionDate',\n        key: 'productionDate',\n        width: 120\n      }, {\n        title: '优先出售',\n        dataIndex: 'prioritySale',\n        key: 'prioritySale',\n        width: 120,\n        scopedSlots: {\n          customRender: 'priority'\n        }\n      }]\n    };\n  },\n  computed: {\n    modalTitle: function modalTitle() {\n      return this.clickType === 'inventory' ? '产品库存详情' : '产品可用库存详情';\n    }\n  },\n  methods: {\n    show: function show(record) {\n      var clickType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'inventory';\n      this.visible = true;\n      this.record = record;\n      this.clickType = clickType;\n      this.loadDetails();\n    },\n    loadDetails: function loadDetails() {\n      var _this = this;\n      this.detailLoading = true;\n      this.productCostLoading = true;\n\n      // 同时加载生产详情和产品成本\n      Promise.all([this.loadProductionDetails(), this.loadProductCost()]).finally(function () {\n        _this.detailLoading = false;\n        _this.productCostLoading = false;\n      });\n    },\n    loadProductionDetails: function loadProductionDetails() {\n      var _this2 = this;\n      var url = '/admin/inventoryManagement/getProductionDetails';\n      return getAction(url, {\n        inventoryId: this.record.id\n      }).then(function (res) {\n        if (res.success) {\n          _this2.productionDetails = res.result || [];\n        } else {\n          _this2.$message.error('获取生产详情失败');\n        }\n      }).catch(function () {\n        _this2.$message.error('获取生产详情失败');\n      });\n    },\n    loadProductCost: function loadProductCost() {\n      var _this3 = this;\n      var url = '/admin/inventoryManagement/getProductCost';\n      return getAction(url, {\n        productName: this.record.imProductName\n      }).then(function (res) {\n        if (res.success) {\n          _this3.productCost = res.result || {\n            totalCost: 0,\n            materialDetails: []\n          };\n        } else {\n          _this3.$message.error('获取产品成本失败');\n        }\n      }).catch(function () {\n        _this3.$message.error('获取产品成本失败');\n      });\n    },\n    handlePriorityChange: function handlePriorityChange(record, checked) {\n      var _this4 = this;\n      var url = '/admin/inventoryManagement/updatePrioritySale';\n      postAction(url, {\n        batchId: record.id,\n        prioritySale: checked\n      }).then(function (res) {\n        if (res.success) {\n          record.prioritySale = checked;\n          _this4.$message.success('设置成功');\n        } else {\n          _this4.$message.error('设置失败');\n        }\n      }).catch(function () {\n        _this4.$message.error('设置失败');\n      });\n    },\n    handleOk: function handleOk() {\n      this.visible = false;\n    },\n    handleCancel: function handleCancel() {\n      this.visible = false;\n    },\n    formatNumber: function formatNumber(num) {\n      if (!num || isNaN(num)) return '0.00';\n      return parseFloat(num).toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\n    }\n  }\n};", {"version": 3, "sources": ["ProductInventoryDetailModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA,SAAA,SAAA,EAAA,UAAA,QAAA,cAAA;AAEA,eAAA;EACA,IAAA,EAAA,6BAAA;EACA,IAAA,WAAA,KAAA,EAAA;IACA,OAAA;MACA,OAAA,EAAA,KAAA;MACA,cAAA,EAAA,KAAA;MACA,OAAA,EAAA,KAAA;MACA,aAAA,EAAA,KAAA;MACA,kBAAA,EAAA,KAAA;MACA,MAAA,EAAA,CAAA,CAAA;MACA,SAAA,EAAA,WAAA;MAAA;MACA,iBAAA,EAAA,EAAA;MACA,WAAA,EAAA;QACA,SAAA,EAAA,CAAA;QACA,eAAA,EAAA;MACA,CAAA;MAEA;MACA,mBAAA,EAAA,CACA;QACA,KAAA,EAAA,MAAA;QACA,SAAA,EAAA,cAAA;QACA,GAAA,EAAA,cAAA;QACA,KAAA,EAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,IAAA;QACA,SAAA,EAAA,eAAA;QACA,GAAA,EAAA,eAAA;QACA,KAAA,EAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,OAAA;QACA,SAAA,EAAA,cAAA;QACA,GAAA,EAAA,cAAA;QACA,KAAA,EAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,IAAA;QACA,SAAA,EAAA,WAAA;QACA,GAAA,EAAA,WAAA;QACA,KAAA,EAAA,GAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,IAAA;QACA,SAAA,EAAA,cAAA;QACA,GAAA,EAAA,cAAA;QACA,KAAA,EAAA,GAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA,CACA;MAEA;MACA,iBAAA,EAAA,CACA;QACA,KAAA,EAAA,YAAA;QACA,SAAA,EAAA,mBAAA;QACA,GAAA,EAAA,mBAAA;QACA,KAAA,EAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,IAAA;QACA,SAAA,EAAA,UAAA;QACA,GAAA,EAAA,UAAA;QACA,KAAA,EAAA,EAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,MAAA;QACA,SAAA,EAAA,WAAA;QACA,GAAA,EAAA,WAAA;QACA,KAAA,EAAA,GAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,MAAA;QACA,SAAA,EAAA,gBAAA;QACA,GAAA,EAAA,gBAAA;QACA,KAAA,EAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,MAAA;QACA,SAAA,EAAA,cAAA;QACA,GAAA,EAAA,cAAA;QACA,KAAA,EAAA,GAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA;IAEA,CAAA;EACA,CAAA;EACA,QAAA,EAAA;IACA,UAAA,WAAA,WAAA,EAAA;MACA,OAAA,IAAA,CAAA,SAAA,KAAA,WAAA,GAAA,QAAA,GAAA,UAAA;IACA;EACA,CAAA;EACA,OAAA,EAAA;IACA,IAAA,WAAA,KAAA,MAAA,EAAA;MAAA,IAAA,SAAA,GAAA,SAAA,CAAA,MAAA,QAAA,SAAA,QAAA,SAAA,GAAA,SAAA,MAAA,WAAA;MACA,IAAA,CAAA,OAAA,GAAA,IAAA;MACA,IAAA,CAAA,MAAA,GAAA,MAAA;MACA,IAAA,CAAA,SAAA,GAAA,SAAA;MACA,IAAA,CAAA,WAAA,CAAA,CAAA;IACA,CAAA;IAEA,WAAA,WAAA,YAAA,EAAA;MAAA,IAAA,KAAA;MACA,IAAA,CAAA,aAAA,GAAA,IAAA;MACA,IAAA,CAAA,kBAAA,GAAA,IAAA;;MAEA;MACA,OAAA,CAAA,GAAA,CAAA,CACA,IAAA,CAAA,qBAAA,CAAA,CAAA,EACA,IAAA,CAAA,eAAA,CAAA,CAAA,CACA,CAAA,CAAA,OAAA,CAAA,YAAA;QACA,KAAA,CAAA,aAAA,GAAA,KAAA;QACA,KAAA,CAAA,kBAAA,GAAA,KAAA;MACA,CAAA,CAAA;IACA,CAAA;IAEA,qBAAA,WAAA,sBAAA,EAAA;MAAA,IAAA,MAAA;MACA,IAAA,GAAA,GAAA,iDAAA;MACA,OAAA,SAAA,CAAA,GAAA,EAAA;QAAA,WAAA,EAAA,IAAA,CAAA,MAAA,CAAA;MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;QACA,IAAA,GAAA,CAAA,OAAA,EAAA;UACA,MAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,MAAA,IAAA,EAAA;QACA,CAAA,MAAA;UACA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA,CAAA;QACA;MACA,CAAA,CAAA,CAAA,KAAA,CAAA,YAAA;QACA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA,CAAA;MACA,CAAA,CAAA;IACA,CAAA;IAEA,eAAA,WAAA,gBAAA,EAAA;MAAA,IAAA,MAAA;MACA,IAAA,GAAA,GAAA,2CAAA;MACA,OAAA,SAAA,CAAA,GAAA,EAAA;QAAA,WAAA,EAAA,IAAA,CAAA,MAAA,CAAA;MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;QACA,IAAA,GAAA,CAAA,OAAA,EAAA;UACA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,MAAA,IAAA;YAAA,SAAA,EAAA,CAAA;YAAA,eAAA,EAAA;UAAA,CAAA;QACA,CAAA,MAAA;UACA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA,CAAA;QACA;MACA,CAAA,CAAA,CAAA,KAAA,CAAA,YAAA;QACA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA,CAAA;MACA,CAAA,CAAA;IACA,CAAA;IAEA,oBAAA,WAAA,qBAAA,MAAA,EAAA,OAAA,EAAA;MAAA,IAAA,MAAA;MACA,IAAA,GAAA,GAAA,+CAAA;MACA,UAAA,CAAA,GAAA,EAAA;QACA,OAAA,EAAA,MAAA,CAAA,EAAA;QACA,YAAA,EAAA;MACA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;QACA,IAAA,GAAA,CAAA,OAAA,EAAA;UACA,MAAA,CAAA,YAAA,GAAA,OAAA;UACA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA,CAAA;QACA,CAAA,MAAA;UACA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA,CAAA;QACA;MACA,CAAA,CAAA,CAAA,KAAA,CAAA,YAAA;QACA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA,CAAA;MACA,CAAA,CAAA;IACA,CAAA;IAEA,QAAA,WAAA,SAAA,EAAA;MACA,IAAA,CAAA,OAAA,GAAA,KAAA;IACA,CAAA;IAEA,YAAA,WAAA,aAAA,EAAA;MACA,IAAA,CAAA,OAAA,GAAA,KAAA;IACA,CAAA;IAEA,YAAA,WAAA,aAAA,GAAA,EAAA;MACA,IAAA,CAAA,GAAA,IAAA,KAAA,CAAA,GAAA,CAAA,EAAA,OAAA,MAAA;MACA,OAAA,UAAA,CAAA,GAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,uBAAA,EAAA,GAAA,CAAA;IACA;EACA;AACA,CAAA", "sourcesContent": ["<template>\n  <a-modal\n    :title=\"modalTitle\"\n    :width=\"900\"\n    :visible=\"visible\"\n    :confirmLoading=\"confirmLoading\"\n    @ok=\"handleOk\"\n    @cancel=\"handleCancel\"\n    destroyOnClose\n    class=\"product-inventory-detail-modal\"\n  >\n    <a-spin :spinning=\"loading\">\n      <div class=\"detail-content\">\n        <!-- 基本信息 -->\n        <div class=\"detail-section\">\n          <div class=\"section-title\">\n            <a-icon type=\"appstore\" />\n            <span>产品信息</span>\n          </div>\n          <div class=\"info-grid\">\n            <div class=\"info-item\">\n              <div class=\"info-label\">产品名称</div>\n              <div class=\"info-value\">{{ record.imProductName }}</div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-label\">单位</div>\n              <div class=\"info-value\">{{ record.imItemUnit || '-' }}</div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-label\">{{ clickType === 'inventory' ? '总库存数量' : '可用库存数量' }}</div>\n              <div class=\"info-value highlight\">{{ clickType === 'inventory' ? record.imInventoryQuantity : record.imAvailableStock }}</div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-label\">平均单价成本</div>\n              <div class=\"info-value price\">¥{{ formatNumber(record.imCost) }}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 产品成本详情 -->\n        <div class=\"detail-section\">\n          <div class=\"section-title\">\n            <a-icon type=\"calculator\" />\n            <span>产品成本（根据物料清单）</span>\n          </div>\n          <div v-if=\"productCostLoading\" class=\"loading-container\">\n            <a-spin size=\"large\" />\n          </div>\n          <div v-else-if=\"productCost.materialDetails && productCost.materialDetails.length > 0\">\n            <div class=\"cost-summary\">\n              <div class=\"cost-item\">\n                <span class=\"cost-label\">物料清单：</span>\n                <span class=\"cost-value\">{{ productCost.materialBillName || '未命名' }}</span>\n              </div>\n              <div class=\"cost-item\">\n                <span class=\"cost-label\">总成本：</span>\n                <span class=\"cost-value price\">¥{{ formatNumber(productCost.totalCost) }}</span>\n              </div>\n            </div>\n            <a-table\n              :columns=\"materialCostColumns\"\n              :dataSource=\"productCost.materialDetails\"\n              :pagination=\"false\"\n              size=\"small\"\n              :locale=\"{ emptyText: '暂无物料成本数据' }\"\n              :scroll=\"{ y: 200 }\"\n            >\n              <template slot=\"cost\" slot-scope=\"text\">\n                <span class=\"price-text\">¥{{ formatNumber(text) }}</span>\n              </template>\n              <template slot=\"unitPrice\" slot-scope=\"text\">\n                <span class=\"price-text\">¥{{ formatNumber(text) }}</span>\n              </template>\n            </a-table>\n          </div>\n          <div v-else class=\"empty-state\">\n            <a-empty description=\"暂无物料清单成本数据\" />\n          </div>\n        </div>\n\n        <!-- 生产批次详情 -->\n        <div class=\"detail-section\">\n          <div class=\"section-title\">\n            <a-icon type=\"tool\" />\n            <span>生产批次详情</span>\n          </div>\n          <a-table\n            :columns=\"productionColumns\"\n            :dataSource=\"productionDetails\"\n            :pagination=\"false\"\n            size=\"small\"\n            :loading=\"detailLoading\"\n            :locale=\"{ emptyText: '暂无生产记录数据' }\"\n            :scroll=\"{ y: 250 }\"\n          >\n            <template slot=\"cost\" slot-scope=\"text\">\n              <span class=\"price-text\">¥{{ formatNumber(text) }}</span>\n            </template>\n            <template slot=\"quantity\" slot-scope=\"text\">\n              <span class=\"quantity-text\">{{ text }}</span>\n            </template>\n            <template slot=\"priority\" slot-scope=\"text, record\">\n              <a-checkbox \n                :checked=\"record.prioritySale\" \n                @change=\"(e) => handlePriorityChange(record, e.target.checked)\"\n              >\n                优先出售此批次\n              </a-checkbox>\n            </template>\n          </a-table>\n        </div>\n      </div>\n    </a-spin>\n  </a-modal>\n</template>\n\n<script>\nimport { getAction, postAction } from '@/api/manage'\n\nexport default {\n  name: 'ProductInventoryDetailModal',\n  data() {\n    return {\n      visible: false,\n      confirmLoading: false,\n      loading: false,\n      detailLoading: false,\n      productCostLoading: false,\n      record: {},\n      clickType: 'inventory', // 'inventory' 或 'available'\n      productionDetails: [],\n      productCost: {\n        totalCost: 0,\n        materialDetails: []\n      },\n      \n      // 物料成本表格列\n      materialCostColumns: [\n        {\n          title: '物料名称',\n          dataIndex: 'materialName',\n          key: 'materialName',\n          width: 150\n        },\n        {\n          title: '用量',\n          dataIndex: 'materialCount',\n          key: 'materialCount',\n          width: 80\n        },\n        {\n          title: '单位/规格',\n          dataIndex: 'materialUnit',\n          key: 'materialUnit',\n          width: 120\n        },\n        {\n          title: '单价',\n          dataIndex: 'unitPrice',\n          key: 'unitPrice',\n          width: 100,\n          scopedSlots: { customRender: 'unitPrice' }\n        },\n        {\n          title: '成本',\n          dataIndex: 'materialCost',\n          key: 'materialCost',\n          width: 100,\n          scopedSlots: { customRender: 'cost' }\n        }\n      ],\n      \n      // 生产详情表格列\n      productionColumns: [\n        {\n          title: '生产批次（生产单号）',\n          dataIndex: 'productionOrderNo',\n          key: 'productionOrderNo',\n          width: 180\n        },\n        {\n          title: '数量',\n          dataIndex: 'quantity',\n          key: 'quantity',\n          width: 80,\n          scopedSlots: { customRender: 'quantity' }\n        },\n        {\n          title: '批次成本',\n          dataIndex: 'batchCost',\n          key: 'batchCost',\n          width: 120,\n          scopedSlots: { customRender: 'cost' }\n        },\n        {\n          title: '生产日期',\n          dataIndex: 'productionDate',\n          key: 'productionDate',\n          width: 120\n        },\n        {\n          title: '优先出售',\n          dataIndex: 'prioritySale',\n          key: 'prioritySale',\n          width: 120,\n          scopedSlots: { customRender: 'priority' }\n        }\n      ]\n    }\n  },\n  computed: {\n    modalTitle() {\n      return this.clickType === 'inventory' ? '产品库存详情' : '产品可用库存详情'\n    }\n  },\n  methods: {\n    show(record, clickType = 'inventory') {\n      this.visible = true\n      this.record = record\n      this.clickType = clickType\n      this.loadDetails()\n    },\n    \n    loadDetails() {\n      this.detailLoading = true\n      this.productCostLoading = true\n      \n      // 同时加载生产详情和产品成本\n      Promise.all([\n        this.loadProductionDetails(),\n        this.loadProductCost()\n      ]).finally(() => {\n        this.detailLoading = false\n        this.productCostLoading = false\n      })\n    },\n    \n    loadProductionDetails() {\n      const url = '/admin/inventoryManagement/getProductionDetails'\n      return getAction(url, { inventoryId: this.record.id }).then(res => {\n        if (res.success) {\n          this.productionDetails = res.result || []\n        } else {\n          this.$message.error('获取生产详情失败')\n        }\n      }).catch(() => {\n        this.$message.error('获取生产详情失败')\n      })\n    },\n    \n    loadProductCost() {\n      const url = '/admin/inventoryManagement/getProductCost'\n      return getAction(url, { productName: this.record.imProductName }).then(res => {\n        if (res.success) {\n          this.productCost = res.result || { totalCost: 0, materialDetails: [] }\n        } else {\n          this.$message.error('获取产品成本失败')\n        }\n      }).catch(() => {\n        this.$message.error('获取产品成本失败')\n      })\n    },\n    \n    handlePriorityChange(record, checked) {\n      const url = '/admin/inventoryManagement/updatePrioritySale'\n      postAction(url, { \n        batchId: record.id, \n        prioritySale: checked \n      }).then(res => {\n        if (res.success) {\n          record.prioritySale = checked\n          this.$message.success('设置成功')\n        } else {\n          this.$message.error('设置失败')\n        }\n      }).catch(() => {\n        this.$message.error('设置失败')\n      })\n    },\n    \n    handleOk() {\n      this.visible = false\n    },\n    \n    handleCancel() {\n      this.visible = false\n    },\n    \n    formatNumber(num) {\n      if (!num || isNaN(num)) return '0.00'\n      return parseFloat(num).toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\")\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.product-inventory-detail-modal {\n  .detail-content {\n    .detail-section {\n      margin-bottom: 24px;\n      \n      .section-title {\n        font-size: 16px;\n        font-weight: 500;\n        color: #262626;\n        margin-bottom: 16px;\n        display: flex;\n        align-items: center;\n        \n        .anticon {\n          margin-right: 8px;\n          color: #1890ff;\n        }\n      }\n      \n      .info-grid {\n        display: grid;\n        grid-template-columns: repeat(2, 1fr);\n        gap: 16px;\n        margin-bottom: 16px;\n        \n        .info-item {\n          .info-label {\n            color: #8c8c8c;\n            font-size: 14px;\n            margin-bottom: 8px;\n          }\n          \n          .info-value {\n            color: #262626;\n            font-size: 16px;\n            \n            &.highlight {\n              color: #1890ff;\n              font-weight: 600;\n              font-size: 18px;\n            }\n            \n            &.price {\n              color: #52c41a;\n              font-weight: 600;\n            }\n          }\n        }\n      }\n      \n      .cost-summary {\n        background: #f5f5f5;\n        padding: 16px;\n        border-radius: 8px;\n        margin-bottom: 16px;\n        \n        .cost-item {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 8px;\n          \n          &:last-child {\n            margin-bottom: 0;\n          }\n          \n          .cost-label {\n            color: #8c8c8c;\n            font-size: 14px;\n          }\n          \n          .cost-value {\n            color: #262626;\n            font-weight: 500;\n            \n            &.price {\n              color: #52c41a;\n              font-weight: 600;\n              font-size: 16px;\n            }\n          }\n        }\n      }\n      \n      .loading-container {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        height: 200px;\n      }\n      \n      .empty-state {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        height: 200px;\n      }\n    }\n  }\n  \n  .price-text {\n    color: #52c41a;\n    font-weight: 600;\n  }\n  \n  .quantity-text {\n    color: #262626;\n    font-weight: 500;\n  }\n}\n\n@media (max-width: 768px) {\n  .product-inventory-detail-modal {\n    .detail-content {\n      .detail-section {\n        .info-grid {\n          grid-template-columns: 1fr;\n        }\n      }\n    }\n  }\n}\n</style>\n"], "sourceRoot": "src/views/admin/purchaseSalesInventory/inventoryManagement/modules"}]}