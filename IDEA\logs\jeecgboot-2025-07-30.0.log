2025-07-30 09:04:57.500 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:04:57.538 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication using Java 1.8.0_351 on 技术02 with PID 6580 (D:\developing\java\Code\IDEA\jiahua_ai_java\jeecg-module-system\jeecg-system-start\target\classes started by zyd in D:\developing\java\Code\IDEA\jiahua_ai_java)
2025-07-30 09:04:57.539 [main] INFO  org.jeecg.JeecgSystemApplication:637 - The following 1 profile is active: "dev"
2025-07-30 09:05:01.035 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:05:01.041 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:05:01.212 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 140 ms. Found 0 Redis repository interfaces.
2025-07-30 09:05:01.464 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-30 09:05:01.465 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*,org.jeecg.modules.drag.*
2025-07-30 09:05:01.467 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-30 09:05:03.549 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-30 09:05:03.550 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-30 09:05:03.551 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-30 09:05:03.551 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-30 09:05:03.551 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-30 09:05:03.552 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-30 09:05:03.552 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-30 09:05:03.552 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-30 09:05:03.552 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-30 09:05:03.552 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-30 09:05:03.553 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragCompDao }
2025-07-30 09:05:03.553 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetHeadDao }
2025-07-30 09:05:03.553 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetItemDao }
2025-07-30 09:05:03.553 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetParamDao }
2025-07-30 09:05:03.553 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDataSourceDao }
2025-07-30 09:05:03.554 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageCompDao }
2025-07-30 09:05:03.554 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageDao }
2025-07-30 09:05:03.558 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor:426 - Cannot enhance @Configuration bean definition 'com.yomahub.liteflow.springboot.config.LiteflowMainAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-07-30 09:05:04.371 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#16' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.381 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragPageDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.382 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#15' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.384 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragPageCompDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.385 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#14' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.386 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.387 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#13' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.388 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragDatasetParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.391 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#12' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.392 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragDatasetItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.394 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#11' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.396 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragDatasetHeadDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.397 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#10' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.398 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragCompDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.399 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.400 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.401 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.402 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.403 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.405 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.408 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.410 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.410 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.411 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.412 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.413 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.413 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.414 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.415 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.416 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.417 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.418 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.419 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4add4dff' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.419 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.452 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.457 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.935 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.937 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.943 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.947 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusConfig' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.949 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'collectorRegistry' of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.950 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.951 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerClock' of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.985 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusMeterRegistry' of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.989 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerOptions' of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:04.991 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceMetrics' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$509/2038670805] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:05.213 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:05.434 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:05.439 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jeecgBaseConfig' of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:05.442 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$6557daa3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.151 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.159 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration$$EnhancerBySpringCGLIB$$878d49fe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.179 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.238 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.541 [main] INFO  org.jeecg.config.shiro.ShiroConfig:242 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-30 09:05:06.545 [main] INFO  org.jeecg.config.shiro.ShiroConfig:253 - ===============(2)创建RedisManager,连接Redis..
2025-07-30 09:05:06.550 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.557 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.589 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.630 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.yomahub.liteflow.springboot.config.LiteflowPropertyAutoConfiguration' of type [com.yomahub.liteflow.springboot.config.LiteflowPropertyAutoConfiguration$$EnhancerBySpringCGLIB$$cc8d6b26] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.641 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'liteflow-com.yomahub.liteflow.springboot.LiteflowProperty' of type [com.yomahub.liteflow.springboot.LiteflowProperty] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.648 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'liteflow.monitor-com.yomahub.liteflow.springboot.LiteflowMonitorProperty' of type [com.yomahub.liteflow.springboot.LiteflowMonitorProperty] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.654 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'liteflowConfig' of type [com.yomahub.liteflow.property.LiteflowConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.662 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.yomahub.liteflow.spi.spring.SpringAware' of type [com.yomahub.liteflow.spi.spring.SpringAware] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.677 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$1934705a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:06.681 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:05:07.369 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8891 (http)
2025-07-30 09:05:07.402 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8891"]
2025-07-30 09:05:07.403 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-30 09:05:07.404 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-30 09:05:07.568 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/zggp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-30 09:05:07.568 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 9700 ms
2025-07-30 09:05:07.907 [main] INFO  o.j.m.jmreport.config.init.JimuReportConfiguration:90 -  Init JimuReport Config [ Token Interceptor & Resource Locations ] 
2025-07-30 09:05:21.176 [main] INFO  com.alibaba.druid.pool.DruidDataSource:1010 - {dataSource-1,master} inited
2025-07-30 09:05:21.179 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:154 - dynamic-datasource - add a datasource named [master] success
2025-07-30 09:05:21.181 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-30 09:05:23.926 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper:347 - Can not find table primary key in Class: "org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatPermanentCode".
2025-07-30 09:05:23.926 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatPermanentCode ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 09:05:24.660 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper:347 - Can not find table primary key in Class: "org.jeecg.modules.xinghuo.vo.ExportSysUser".
2025-07-30 09:05:24.660 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class org.jeecg.modules.xinghuo.vo.ExportSysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 09:05:25.344 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:56 -  --- redis config init --- 
2025-07-30 09:05:30.770 [main] INFO  o.j.m.admin.client.impl.XunfeiVoiceCloneClientImpl:101 - OkHttpClient初始化成功，使用TLSv1.2协议
2025-07-30 09:05:44.592 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper:347 - Can not find table primary key in Class: "org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatCustomer".
2025-07-30 09:05:44.592 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatCustomer ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 09:05:48.459 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-30 09:05:48.479 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-30 09:05:48.594 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 09:05:48.595 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-30 09:05:48.628 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-30 09:05:48.641 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-30 09:05:48.647 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '技术***************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-30 09:05:48.647 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-30 09:05:48.647 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-30 09:05:48.647 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2ee16a4e
2025-07-30 09:05:50.037 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:94 - 已从文件加载私钥: classpath:coze/coze_private_key.pem
2025-07-30 09:05:50.037 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:171 - 使用API基础URL: https://api.coze.cn
2025-07-30 09:05:50.260 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:184 - JWT OAuth客户端初始化成功
2025-07-30 09:05:53.628 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:215 - 获取访问令牌成功，有效期至: Wed Jul 30 09:09:13 CST 2025
2025-07-30 09:05:54.033 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:194 - CozeAPI客户端初始化成功
2025-07-30 09:05:56.702 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper:347 - Can not find table primary key in Class: "org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatSchedule".
2025-07-30 09:05:56.703 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatSchedule ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 09:05:59.376 [main] INFO  o.jeecg.modules.message.handle.utils.BaiduSmsUtil:52 - 百度短信客户端初始化成功
2025-07-30 09:06:07.014 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:42 -  Init JimuReport Config [ 线程池 ] 
2025-07-30 09:06:07.983 [main] INFO  c.y.l.process.impl.CmpAroundAspectBeanProcess:32 - component aspect implement[nodeProcessAspect] has been found
2025-07-30 09:06:09.901 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[classifier] has been found
2025-07-30 09:06:09.914 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[end] has been found
2025-07-30 09:06:09.922 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[enhanceJava] has been found
2025-07-30 09:06:09.931 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[http] has been found
2025-07-30 09:06:09.940 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[knowledge] has been found
2025-07-30 09:06:09.950 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[llm] has been found
2025-07-30 09:06:09.957 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[reply] has been found
2025-07-30 09:06:09.964 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[start] has been found
2025-07-30 09:06:09.972 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[subflow] has been found
2025-07-30 09:06:09.979 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[switch] has been found
2025-07-30 09:06:13.051 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-30 09:06:13.268 [main] INFO  org.jeecg.config.TaskExecutorConfig:24 - 初始化异步任务线程池 taskExecutor
2025-07-30 09:06:13.313 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  Init CodeGenerate Config [ Get Db Config From application.yml ] 
2025-07-30 09:06:19.081 [main] INFO  c.y.liteflow.parser.factory.FlowParserProvider:193 - flow info loaded from class config with el,class=com.yomahub.liteflow.parser.sql.SQLXmlELParser
2025-07-30 09:06:19.751 [main] INFO  c.yomahub.liteflow.parser.sql.read.AbstractSqlRead:186 - query sql: select id, application_name, chain from airag_flow where status = 'enable' and chain is not null
2025-07-30 09:06:21.472 [main] INFO  c.y.liteflow.parser.sql.util.LiteFlowJdbcUtil:43 - use dataSourceName[dataSource],has found liteflow config
2025-07-30 09:06:27.308 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8891"]
2025-07-30 09:06:27.517 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8891 (http) with context path '/zggp'
2025-07-30 09:06:44.935 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 108.355 seconds (JVM running for 110.812)
2025-07-30 09:06:44.990 [main] INFO  org.jeecg.modules.admin.job.RegisterCrmJobs:28 - ==== 开始注册CRM定时任务 ====
2025-07-30 09:06:46.740 [main] INFO  org.jeecg.modules.admin.job.RegisterCrmJobs:85 - 定时任务[客户自动回收任务]已存在，无需重复创建
2025-07-30 09:06:46.769 [main] INFO  org.jeecg.modules.admin.job.RegisterCrmJobs:85 - 定时任务[客户回收预警任务]已存在，无需重复创建
2025-07-30 09:06:46.769 [main] INFO  org.jeecg.modules.admin.job.RegisterCrmJobs:49 - ==== CRM定时任务注册完成 ====
2025-07-30 09:06:46.775 [main] INFO  org.jeecg.config.init.CodeTemplateInitListener:29 -  Init Code Generate Template [ 检测如果是JAR启动环境，Copy模板到config目录 ] 
2025-07-30 09:06:46.856 [main] INFO  org.jeecg.JeecgSystemApplication:38 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8891/zggp/
	External: 	http://************:8891/zggp/
	Swagger文档: 	http://************:8891/zggp/doc.html
----------------------------------------------------------
2025-07-30 09:06:47.897 [RMI TCP Connection(3)-************] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/zggp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:06:47.898 [RMI TCP Connection(3)-************] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:06:47.940 [RMI TCP Connection(3)-************] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 42 ms
2025-07-30 09:07:02.136 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:07:02.197 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:07:15.345 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:07:15.345 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:07:15.374 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:07:15.375 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:07:15.375 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:07:15.399 [http-nio-8891-exec-9] INFO  o.j.m.admin.controller.SystemSettingsController:170 - 开始获取用户设置...
2025-07-30 09:07:15.400 [http-nio-8891-exec-9] INFO  o.j.m.admin.controller.SystemSettingsController:181 - 当前用户ID: e9ca23d68d884d4ebb19d07889727dae
2025-07-30 09:07:15.645 [http-nio-8891-exec-9] INFO  o.j.m.admin.controller.SystemSettingsController:185 - 获取到的设置: SystemSettingsDto(navTheme=light, primaryColor=#0050b3, layoutMode=sidemenu, contentWidth=Fixed, fixedHeader=true, autoHideHeader=false, fixSiderbar=true, colorWeak=false, multipage=false)
2025-07-30 09:07:16.540 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:07:27.646 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:07:27.646 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:07:27.771 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:07:27.776 [http-nio-8891-exec-6] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:07:27.897 [http-nio-8891-exec-6] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:07:27.897 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:07:27.996 [http-nio-8891-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:1085 - query auth sql is:
2025-07-30 09:07:27.996 [http-nio-8891-exec-6] INFO  org.jeecg.common.system.query.QueryGenerator:1085 - query auth sql is:
2025-07-30 09:07:27.996 [http-nio-8891-exec-4] INFO  o.j.m.a.controller.OrderManagementSystemController:344 - 原始权限SQL: 
2025-07-30 09:07:27.996 [http-nio-8891-exec-6] INFO  o.j.m.a.controller.OrderManagementSystemController:344 - 原始权限SQL: 
2025-07-30 09:07:27.997 [http-nio-8891-exec-6] WARN  o.j.m.a.controller.OrderManagementSystemController:348 - 数据权限SQL为空，请检查权限规则配置
2025-07-30 09:07:27.997 [http-nio-8891-exec-4] WARN  o.j.m.a.controller.OrderManagementSystemController:348 - 数据权限SQL为空，请检查权限规则配置
2025-07-30 09:07:27.997 [http-nio-8891-exec-6] INFO  o.j.m.a.controller.OrderManagementSystemController:353 - 当前用户: admin，ID: e9ca23d68d884d4ebb19d07889727dae, 部门: A02
2025-07-30 09:07:27.997 [http-nio-8891-exec-4] INFO  o.j.m.a.controller.OrderManagementSystemController:353 - 当前用户: admin，ID: e9ca23d68d884d4ebb19d07889727dae, 部门: A02
2025-07-30 09:07:28.341 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:07:28.341 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:07:31.004 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:07:31.146 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.OrderManagementSystemController:2161 - 找到产品 卓尔互动产品 的库存批次 1 个
2025-07-30 09:07:36.173 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:07:36.251 [http-nio-8891-exec-6] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:07:36.355 [http-nio-8891-exec-6] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:07:56.210 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:08:00.086 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:08:00.159 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:08:01.270 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:08:01.271 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:08:01.297 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:08:01.298 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:08:01.298 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:08:01.300 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:08:21.303 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:08:21.389 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:08:21.494 [http-nio-8891-exec-4] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:08:41.316 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:08:46.375 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:08:46.375 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:08:46.401 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:08:46.401 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:08:46.402 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:08:46.404 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:09:00.061 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:09:00.123 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:09:06.420 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:09:06.516 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:09:06.610 [http-nio-8891-exec-1] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:09:26.436 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:09:31.508 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:09:31.508 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:09:31.537 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:09:31.538 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:09:31.540 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:09:31.543 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:09:51.552 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:09:51.641 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:09:51.742 [http-nio-8891-exec-9] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:10:00.836 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:10:00.896 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:10:11.563 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:10:16.636 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:10:16.637 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:10:16.665 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:10:16.672 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:10:16.674 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:10:16.677 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:10:36.690 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:10:36.797 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:10:36.897 [http-nio-8891-exec-2] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:10:56.696 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:11:00.699 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:11:00.760 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:11:01.771 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:11:01.772 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:11:01.799 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:11:01.800 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:11:01.802 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:11:01.806 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:11:21.859 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:11:21.938 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:11:21.943 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:11:22.033 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.OrderManagementSystemController:2161 - 找到产品 cppm注册采购经理证书 的库存批次 1 个
2025-07-30 09:11:22.081 [http-nio-8891-exec-1] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:11:24.980 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:11:25.096 [http-nio-8891-exec-5] INFO  o.j.m.a.controller.OrderManagementSystemController:2161 - 找到产品 cppm注册采购经理证书 的库存批次 1 个
2025-07-30 09:11:29.350 [http-nio-8891-exec-6] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:11:29.462 [http-nio-8891-exec-6] INFO  o.j.m.a.controller.OrderManagementSystemController:2161 - 找到产品 cppm注册采购经理证书 的库存批次 1 个
2025-07-30 09:11:41.871 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:11:46.944 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:11:46.945 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:11:46.972 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:11:46.973 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:11:46.975 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:11:46.978 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:12:00.080 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:12:00.149 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:12:06.985 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:12:07.074 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:12:07.184 [http-nio-8891-exec-2] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:12:26.987 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:12:32.056 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:12:32.057 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:12:32.083 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:12:32.084 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:12:32.085 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:12:32.086 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:12:52.093 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:12:52.184 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:12:52.288 [http-nio-8891-exec-9] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:13:00.066 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:13:00.130 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:13:12.106 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:13:17.168 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:13:17.169 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:13:17.195 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:13:17.196 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:13:17.197 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:13:17.199 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:13:37.211 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:13:37.297 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:13:37.402 [http-nio-8891-exec-4] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:13:57.221 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:14:00.801 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:14:00.870 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:14:02.288 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:14:02.288 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:14:02.315 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:14:02.316 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:14:02.317 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:14:02.319 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:14:22.334 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:14:22.424 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:14:22.542 [http-nio-8891-exec-8] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:14:42.337 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:14:47.400 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:14:47.400 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:14:47.426 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:14:47.427 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:14:47.427 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:14:47.429 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:15:00.077 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:15:00.148 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:15:07.443 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:15:07.538 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:15:07.650 [http-nio-8891-exec-5] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:15:27.452 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:15:32.530 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:15:32.531 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:15:32.560 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:15:32.561 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:15:32.563 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:15:32.566 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:15:52.572 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:15:52.666 [http-nio-8891-exec-3] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:15:52.780 [http-nio-8891-exec-3] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:16:01.505 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:16:01.574 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:16:12.580 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:16:17.654 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:16:17.655 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:16:17.681 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:16:17.684 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:16:17.686 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:16:17.690 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:16:37.702 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:16:37.798 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:16:37.914 [http-nio-8891-exec-1] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:16:57.705 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:17:01.226 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:17:01.298 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:17:02.780 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:17:02.780 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:17:02.808 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:17:02.809 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:17:02.809 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:17:02.812 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:17:22.825 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:17:22.909 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:17:23.013 [http-nio-8891-exec-8] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:17:42.826 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:17:48.232 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:17:48.232 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:17:48.258 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:17:48.259 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:17:48.259 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:17:48.261 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:18:01.386 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:18:01.454 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:18:09.181 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:18:09.268 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:18:09.373 [http-nio-8891-exec-2] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:18:22.028 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:18:22.140 [http-nio-8891-exec-1] INFO  o.j.m.a.controller.OrderManagementSystemController:2161 - 找到产品 cppm注册采购经理证书 的库存批次 1 个
2025-07-30 09:18:28.265 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:18:28.379 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.OrderManagementSystemController:2161 - 找到产品 卓尔互动产品 的库存批次 1 个
2025-07-30 09:18:29.196 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:18:34.263 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:18:34.264 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:18:34.290 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:18:34.291 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:18:34.292 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:18:34.296 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:18:38.511 [http-nio-8891-exec-4] ERROR druid.sql.Statement:148 - {conn-10001, stmt-20090} execute error. SELECT 1
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 436,422 milliseconds ago. The last packet sent successfully to the server was 436,447 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1206)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2866)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeQuery(FilterAdapter.java:2513)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeQuery(FilterEventAdapter.java:296)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.wall.WallFilter.statement_executeQuery(WallFilter.java:547)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeQuery(FilterAdapter.java:2513)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeQuery(FilterEventAdapter.java:296)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeQuery(StatementProxyImpl.java:217)
	at com.alibaba.druid.pool.vendor.MySqlValidConnectionChecker.isValidConnection(MySqlValidConnectionChecker.java:143)
	at com.alibaba.druid.pool.DruidAbstractDataSource.testConnectionInternal(DruidAbstractDataSource.java:1514)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1549)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5058)
	at com.alibaba.druid.filter.logging.LogFilter.dataSource_getConnection(LogFilter.java:909)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.filter.FilterAdapter.dataSource_getConnection(FilterAdapter.java:2759)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.filter.stat.StatFilter.dataSource_getConnection(StatFilter.java:704)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1469)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1459)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at com.baomidou.dynamic.datasource.ds.ItemDataSource.getConnection(ItemDataSource.java:55)
	at com.baomidou.dynamic.datasource.ds.AbstractRoutingDataSource.getConnection(AbstractRoutingDataSource.java:54)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at sun.reflect.GeneratedMethodAccessor96.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy695.query(Unknown Source)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy695.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor150.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy191.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy194.selectList(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.list(IService.java:370)
	at com.baomidou.mybatisplus.extension.service.IService$$FastClassBySpringCGLIB$$f8525d18.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at org.jeecg.modules.system.service.impl.SysAnnouncementServiceImpl$$EnhancerBySpringCGLIB$$ec371aea.list(<generated>)
	at com.baomidou.mybatisplus.extension.service.IService$$FastClassBySpringCGLIB$$f8525d18.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at org.jeecg.modules.system.service.impl.SysAnnouncementServiceImpl$$EnhancerBySpringCGLIB$$ca2d9178.list(<generated>)
	at org.jeecg.modules.system.controller.SysAnnouncementController.listByUser(SysAnnouncementController.java:353)
	at org.jeecg.modules.system.controller.SysAnnouncementController$$FastClassBySpringCGLIB$$15943ec.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:62)
	at sun.reflect.GeneratedMethodAccessor318.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.system.controller.SysAnnouncementController$$EnhancerBySpringCGLIB$$25192190.listByUser(<generated>)
	at sun.reflect.GeneratedMethodAccessor383.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:502)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:596)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:492)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 436,422 milliseconds ago. The last packet sent successfully to the server was 436,447 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:520)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:700)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:639)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:987)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryString(NativeProtocol.java:933)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:664)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1174)
	... 174 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:514)
	... 180 common frames omitted
2025-07-30 09:18:54.314 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:18:54.408 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:18:54.539 [http-nio-8891-exec-2] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:19:00.081 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:19:00.154 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:19:14.325 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:19:19.399 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:19:19.399 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:19:19.424 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:19:19.426 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:19:19.426 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:19:19.428 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:19:39.444 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:19:39.529 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:19:39.646 [http-nio-8891-exec-4] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:19:59.456 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:20:01.715 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:20:01.788 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:20:04.521 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:20:04.521 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:20:04.547 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:20:04.548 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:20:04.548 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:20:04.549 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:20:24.555 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:20:24.642 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:20:24.770 [http-nio-8891-exec-7] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:20:44.561 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:20:49.633 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:20:49.633 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:20:49.660 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:20:49.661 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:20:49.661 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:20:49.663 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:21:01.533 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:21:01.608 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:21:09.676 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:21:09.762 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:21:09.884 [http-nio-8891-exec-4] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:21:29.691 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:21:34.757 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:21:34.758 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:21:34.783 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:21:34.784 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:21:34.785 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:21:34.787 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:21:54.794 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:21:54.879 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:21:54.995 [http-nio-8891-exec-9] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:22:00.847 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:22:00.920 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:22:14.802 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:22:19.872 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:22:19.872 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:22:19.898 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:22:19.900 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:22:19.900 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:22:19.901 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:22:40.181 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:22:40.267 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:22:40.380 [http-nio-8891-exec-2] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:22:59.629 [SpringApplicationShutdownHook] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:22:59.779 [SpringApplicationShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:847 - Shutting down Quartz Scheduler
2025-07-30 09:22:59.781 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_技术*************** shutting down.
2025-07-30 09:22:59.782 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_技术*************** paused.
2025-07-30 09:22:59.786 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_技术*************** shutdown complete.
2025-07-30 09:22:59.843 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:211 - dynamic-datasource start closing ....
2025-07-30 09:22:59.848 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2175 - {dataSource-1} closing ...
2025-07-30 09:22:59.868 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2248 - {dataSource-1} closed
2025-07-30 09:22:59.869 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:215 - dynamic-datasource all closed success,bye
2025-07-30 09:27:49.943 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 09:27:49.982 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication using Java 1.8.0_351 on 技术02 with PID 17536 (D:\developing\java\Code\IDEA\jiahua_ai_java\jeecg-module-system\jeecg-system-start\target\classes started by zyd in D:\developing\java\Code\IDEA\jiahua_ai_java)
2025-07-30 09:27:49.983 [main] INFO  org.jeecg.JeecgSystemApplication:637 - The following 1 profile is active: "dev"
2025-07-30 09:27:53.198 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 09:27:53.204 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:27:53.349 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 118 ms. Found 0 Redis repository interfaces.
2025-07-30 09:27:53.552 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-30 09:27:53.552 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*,org.jeecg.modules.drag.*
2025-07-30 09:27:53.554 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-30 09:27:55.418 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-30 09:27:55.419 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-30 09:27:55.419 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-30 09:27:55.420 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-30 09:27:55.420 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-30 09:27:55.420 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-30 09:27:55.420 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-30 09:27:55.421 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-30 09:27:55.421 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-30 09:27:55.421 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-30 09:27:55.421 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragCompDao }
2025-07-30 09:27:55.421 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetHeadDao }
2025-07-30 09:27:55.421 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetItemDao }
2025-07-30 09:27:55.421 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetParamDao }
2025-07-30 09:27:55.421 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDataSourceDao }
2025-07-30 09:27:55.422 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageCompDao }
2025-07-30 09:27:55.422 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageDao }
2025-07-30 09:27:55.426 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor:426 - Cannot enhance @Configuration bean definition 'com.yomahub.liteflow.springboot.config.LiteflowMainAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-07-30 09:27:55.957 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#16' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.962 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragPageDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.963 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#15' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.964 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragPageCompDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.965 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#14' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.966 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.967 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#13' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.967 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragDatasetParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.968 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#12' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.970 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragDatasetItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.971 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#11' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.972 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragDatasetHeadDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.973 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#10' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.973 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragCompDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.974 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.974 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.975 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.976 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.977 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.977 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.978 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.979 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.979 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.980 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.981 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.982 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.983 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.983 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.984 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.985 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.986 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.987 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.987 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#611a91df' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:55.988 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.011 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.014 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.352 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.353 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.358 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.361 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusConfig' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.362 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'collectorRegistry' of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.363 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.364 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerClock' of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.396 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusMeterRegistry' of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.402 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerOptions' of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.404 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceMetrics' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$509/1393377460] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.609 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.813 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.818 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jeecgBaseConfig' of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:56.820 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$57e05504] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.465 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.472 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration$$EnhancerBySpringCGLIB$$7a15c45f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.492 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.550 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.824 [main] INFO  org.jeecg.config.shiro.ShiroConfig:242 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-30 09:27:57.828 [main] INFO  org.jeecg.config.shiro.ShiroConfig:253 - ===============(2)创建RedisManager,连接Redis..
2025-07-30 09:27:57.834 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.841 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.870 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.907 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.yomahub.liteflow.springboot.config.LiteflowPropertyAutoConfiguration' of type [com.yomahub.liteflow.springboot.config.LiteflowPropertyAutoConfiguration$$EnhancerBySpringCGLIB$$bf15e587] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.917 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'liteflow-com.yomahub.liteflow.springboot.LiteflowProperty' of type [com.yomahub.liteflow.springboot.LiteflowProperty] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.924 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'liteflow.monitor-com.yomahub.liteflow.springboot.LiteflowMonitorProperty' of type [com.yomahub.liteflow.springboot.LiteflowMonitorProperty] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.927 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'liteflowConfig' of type [com.yomahub.liteflow.property.LiteflowConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.932 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.yomahub.liteflow.spi.spring.SpringAware' of type [com.yomahub.liteflow.spi.spring.SpringAware] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.947 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$bbceabb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:57.951 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:27:58.592 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8891 (http)
2025-07-30 09:27:58.619 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8891"]
2025-07-30 09:27:58.620 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-30 09:27:58.620 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-30 09:27:58.796 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/zggp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-30 09:27:58.796 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 8501 ms
2025-07-30 09:27:59.124 [main] INFO  o.j.m.jmreport.config.init.JimuReportConfiguration:90 -  Init JimuReport Config [ Token Interceptor & Resource Locations ] 
2025-07-30 09:28:02.213 [main] INFO  com.alibaba.druid.pool.DruidDataSource:1010 - {dataSource-1,master} inited
2025-07-30 09:28:02.219 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:154 - dynamic-datasource - add a datasource named [master] success
2025-07-30 09:28:02.219 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-30 09:28:04.853 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper:347 - Can not find table primary key in Class: "org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatPermanentCode".
2025-07-30 09:28:04.854 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatPermanentCode ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 09:28:05.586 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper:347 - Can not find table primary key in Class: "org.jeecg.modules.xinghuo.vo.ExportSysUser".
2025-07-30 09:28:05.586 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class org.jeecg.modules.xinghuo.vo.ExportSysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 09:28:06.267 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:56 -  --- redis config init --- 
2025-07-30 09:28:11.464 [main] INFO  o.j.m.admin.client.impl.XunfeiVoiceCloneClientImpl:101 - OkHttpClient初始化成功，使用TLSv1.2协议
2025-07-30 09:28:25.974 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper:347 - Can not find table primary key in Class: "org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatCustomer".
2025-07-30 09:28:25.975 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatCustomer ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 09:28:29.776 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-30 09:28:29.801 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-30 09:28:29.956 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 09:28:29.957 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-30 09:28:30.011 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-30 09:28:30.029 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-30 09:28:30.034 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '技术***************'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-30 09:28:30.035 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-30 09:28:30.035 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-30 09:28:30.035 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@66a75eab
2025-07-30 09:28:31.402 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:94 - 已从文件加载私钥: classpath:coze/coze_private_key.pem
2025-07-30 09:28:31.403 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:171 - 使用API基础URL: https://api.coze.cn
2025-07-30 09:28:31.544 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:184 - JWT OAuth客户端初始化成功
2025-07-30 09:28:33.909 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:215 - 获取访问令牌成功，有效期至: Wed Jul 30 09:31:53 CST 2025
2025-07-30 09:28:34.280 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:194 - CozeAPI客户端初始化成功
2025-07-30 09:28:37.373 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper:347 - Can not find table primary key in Class: "org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatSchedule".
2025-07-30 09:28:37.374 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatSchedule ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 09:28:39.905 [main] INFO  o.jeecg.modules.message.handle.utils.BaiduSmsUtil:52 - 百度短信客户端初始化成功
2025-07-30 09:28:48.048 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:42 -  Init JimuReport Config [ 线程池 ] 
2025-07-30 09:28:49.004 [main] INFO  c.y.l.process.impl.CmpAroundAspectBeanProcess:32 - component aspect implement[nodeProcessAspect] has been found
2025-07-30 09:28:51.241 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[classifier] has been found
2025-07-30 09:28:51.258 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[end] has been found
2025-07-30 09:28:51.269 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[enhanceJava] has been found
2025-07-30 09:28:51.282 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[http] has been found
2025-07-30 09:28:51.293 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[knowledge] has been found
2025-07-30 09:28:51.305 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[llm] has been found
2025-07-30 09:28:51.313 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[reply] has been found
2025-07-30 09:28:51.320 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[start] has been found
2025-07-30 09:28:51.327 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[subflow] has been found
2025-07-30 09:28:51.336 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[switch] has been found
2025-07-30 09:28:55.506 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-30 09:28:55.779 [main] INFO  org.jeecg.config.TaskExecutorConfig:24 - 初始化异步任务线程池 taskExecutor
2025-07-30 09:28:55.826 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  Init CodeGenerate Config [ Get Db Config From application.yml ] 
2025-07-30 09:28:59.528 [main] INFO  c.y.liteflow.parser.factory.FlowParserProvider:193 - flow info loaded from class config with el,class=com.yomahub.liteflow.parser.sql.SQLXmlELParser
2025-07-30 09:28:59.654 [main] INFO  c.yomahub.liteflow.parser.sql.read.AbstractSqlRead:186 - query sql: select id, application_name, chain from airag_flow where status = 'enable' and chain is not null
2025-07-30 09:29:00.131 [main] INFO  c.y.liteflow.parser.sql.util.LiteFlowJdbcUtil:43 - use dataSourceName[dataSource],has found liteflow config
2025-07-30 09:29:01.250 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8891"]
2025-07-30 09:29:01.334 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8891 (http) with context path '/zggp'
2025-07-30 09:29:14.113 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 84.946 seconds (JVM running for 87.037)
2025-07-30 09:29:14.170 [main] INFO  org.jeecg.modules.admin.job.RegisterCrmJobs:28 - ==== 开始注册CRM定时任务 ====
2025-07-30 09:29:15.233 [main] INFO  org.jeecg.modules.admin.job.RegisterCrmJobs:85 - 定时任务[客户自动回收任务]已存在，无需重复创建
2025-07-30 09:29:15.260 [main] INFO  org.jeecg.modules.admin.job.RegisterCrmJobs:85 - 定时任务[客户回收预警任务]已存在，无需重复创建
2025-07-30 09:29:15.260 [main] INFO  org.jeecg.modules.admin.job.RegisterCrmJobs:49 - ==== CRM定时任务注册完成 ====
2025-07-30 09:29:15.267 [main] INFO  org.jeecg.config.init.CodeTemplateInitListener:29 -  Init Code Generate Template [ 检测如果是JAR启动环境，Copy模板到config目录 ] 
2025-07-30 09:29:15.329 [main] INFO  org.jeecg.JeecgSystemApplication:38 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8891/zggp/
	External: 	http://************:8891/zggp/
	Swagger文档: 	http://************:8891/zggp/doc.html
----------------------------------------------------------
2025-07-30 09:29:16.171 [RMI TCP Connection(3)-************] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/zggp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:29:16.173 [RMI TCP Connection(3)-************] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-30 09:29:16.191 [RMI TCP Connection(3)-************] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 18 ms
2025-07-30 09:30:01.716 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:30:01.774 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:31:00.069 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:31:00.123 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:32:01.200 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:32:01.255 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:33:01.223 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:33:01.282 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:34:01.627 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:34:01.681 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:34:04.477 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:34:04.479 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:34:04.503 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:34:04.504 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:34:04.505 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:34:04.518 [http-nio-8891-exec-7] INFO  o.j.m.admin.controller.SystemSettingsController:170 - 开始获取用户设置...
2025-07-30 09:34:04.518 [http-nio-8891-exec-7] INFO  o.j.m.admin.controller.SystemSettingsController:181 - 当前用户ID: e9ca23d68d884d4ebb19d07889727dae
2025-07-30 09:34:04.638 [http-nio-8891-exec-7] INFO  o.j.m.admin.controller.SystemSettingsController:185 - 获取到的设置: SystemSettingsDto(navTheme=light, primaryColor=#0050b3, layoutMode=sidemenu, contentWidth=Fixed, fixedHeader=true, autoHideHeader=false, fixSiderbar=true, colorWeak=false, multipage=false)
2025-07-30 09:34:05.152 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:34:08.941 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:34:08.941 [http-nio-8891-exec-3] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:34:09.089 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:34:09.185 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:34:09.237 [http-nio-8891-exec-7] INFO  org.jeecg.common.system.query.QueryGenerator:1085 - query auth sql is:
2025-07-30 09:34:09.238 [http-nio-8891-exec-7] INFO  o.j.m.a.controller.OrderManagementSystemController:344 - 原始权限SQL: 
2025-07-30 09:34:09.238 [http-nio-8891-exec-7] WARN  o.j.m.a.controller.OrderManagementSystemController:348 - 数据权限SQL为空，请检查权限规则配置
2025-07-30 09:34:09.238 [http-nio-8891-exec-7] INFO  o.j.m.a.controller.OrderManagementSystemController:353 - 当前用户: admin，ID: e9ca23d68d884d4ebb19d07889727dae, 部门: A02
2025-07-30 09:34:09.444 [http-nio-8891-exec-10] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:34:09.444 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:34:15.482 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:34:15.582 [http-nio-8891-exec-9] INFO  o.j.m.a.controller.OrderManagementSystemController:2161 - 找到产品 卓尔互动产品 的库存批次 1 个
2025-07-30 09:34:19.046 [http-nio-8891-exec-2] ERROR druid.sql.Statement:148 - {conn-10003, stmt-20025} execute error. SELECT 1
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 377,305 milliseconds ago. The last packet sent successfully to the server was 377,325 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1206)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2866)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeQuery(FilterAdapter.java:2513)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeQuery(FilterEventAdapter.java:296)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.wall.WallFilter.statement_executeQuery(WallFilter.java:547)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeQuery(FilterAdapter.java:2513)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeQuery(FilterEventAdapter.java:296)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeQuery(StatementProxyImpl.java:217)
	at com.alibaba.druid.pool.vendor.MySqlValidConnectionChecker.isValidConnection(MySqlValidConnectionChecker.java:143)
	at com.alibaba.druid.pool.DruidAbstractDataSource.testConnectionInternal(DruidAbstractDataSource.java:1514)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1549)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5058)
	at com.alibaba.druid.filter.logging.LogFilter.dataSource_getConnection(LogFilter.java:909)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.filter.FilterAdapter.dataSource_getConnection(FilterAdapter.java:2759)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.filter.stat.StatFilter.dataSource_getConnection(StatFilter.java:704)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1469)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1459)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at com.baomidou.dynamic.datasource.ds.ItemDataSource.getConnection(ItemDataSource.java:55)
	at com.baomidou.dynamic.datasource.ds.AbstractRoutingDataSource.getConnection(AbstractRoutingDataSource.java:54)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at sun.reflect.GeneratedMethodAccessor96.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy695.query(Unknown Source)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy695.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor132.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy191.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy203.selectList(Unknown Source)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl.queryPermissionDataRule(SysBaseApiImpl.java:146)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$FastClassBySpringCGLIB$$93fd2f03.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$EnhancerBySpringCGLIB$$32e4b6a0.queryPermissionDataRule(<generated>)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$FastClassBySpringCGLIB$$93fd2f03.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$EnhancerBySpringCGLIB$$ae5942a0.queryPermissionDataRule(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:208)
	at com.sun.proxy.$Proxy161.queryPermissionDataRule(Unknown Source)
	at org.jeecg.common.aspect.PermissionDataAspect.arround(PermissionDataAspect.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:62)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.admin.controller.OrderManagementSystemController$$EnhancerBySpringCGLIB$$2d3bc4b0.queryPageList(<generated>)
	at org.jeecg.modules.admin.controller.OrderManagementSystemController$$FastClassBySpringCGLIB$$138fee5a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at org.jeecg.modules.admin.controller.OrderManagementSystemController$$EnhancerBySpringCGLIB$$98d11e88.queryPageList(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:502)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:596)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:492)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 377,305 milliseconds ago. The last packet sent successfully to the server was 377,325 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:520)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:700)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:639)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:987)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryString(NativeProtocol.java:933)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:664)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1174)
	... 193 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:514)
	... 199 common frames omitted
2025-07-30 09:34:19.103 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:34:19.184 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:34:19.237 [http-nio-8891-exec-2] INFO  org.jeecg.common.system.query.QueryGenerator:1085 - query auth sql is:
2025-07-30 09:34:19.237 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.OrderManagementSystemController:344 - 原始权限SQL: 
2025-07-30 09:34:19.238 [http-nio-8891-exec-2] WARN  o.j.m.a.controller.OrderManagementSystemController:348 - 数据权限SQL为空，请检查权限规则配置
2025-07-30 09:34:19.238 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.OrderManagementSystemController:353 - 当前用户: admin，ID: e9ca23d68d884d4ebb19d07889727dae, 部门: A02
2025-07-30 09:34:24.970 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:34:25.037 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:34:25.118 [http-nio-8891-exec-8] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:34:44.999 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:34:50.052 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:34:50.052 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:34:50.075 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:34:50.076 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:34:50.076 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:34:50.078 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:35:00.502 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:35:00.544 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:35:10.172 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:35:10.248 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:35:10.317 [http-nio-8891-exec-7] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:35:30.178 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:35:36.230 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:35:36.231 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:35:36.255 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:35:36.257 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:35:36.259 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:35:36.261 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:35:57.177 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:35:57.252 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:35:57.320 [http-nio-8891-exec-4] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:36:00.488 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:36:00.531 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:36:18.186 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:36:24.227 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:36:24.227 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:36:24.249 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:36:24.250 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:36:24.251 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:36:24.252 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:36:45.179 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:36:45.261 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:36:45.334 [http-nio-8891-exec-2] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:37:00.489 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:37:00.534 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:37:06.174 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:37:12.230 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:37:12.231 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:37:12.254 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:37:12.254 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:37:12.255 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:37:12.257 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:37:33.172 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:37:33.250 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:37:33.325 [http-nio-8891-exec-1] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:37:54.183 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:38:00.220 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:38:00.220 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:38:00.243 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:38:00.243 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:38:00.244 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:38:00.246 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:38:00.960 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:38:01.005 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:38:21.175 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:38:21.255 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:38:21.331 [http-nio-8891-exec-9] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:38:42.171 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:38:48.233 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:38:48.233 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:38:48.256 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:38:48.257 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:38:48.258 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:38:48.260 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:39:00.551 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:39:00.593 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:39:09.170 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:39:09.247 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:39:09.318 [http-nio-8891-exec-4] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:39:29.188 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:39:35.217 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:39:35.217 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:39:35.240 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:39:35.240 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:39:35.240 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:39:35.241 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:39:56.176 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:39:56.261 [http-nio-8891-exec-6] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:39:56.334 [http-nio-8891-exec-6] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:40:00.052 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:40:00.092 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:40:17.179 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:40:23.234 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:40:23.234 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:40:23.256 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:40:23.256 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:40:23.257 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:40:23.258 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:40:44.179 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:40:44.253 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:40:44.325 [http-nio-8891-exec-8] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:41:00.051 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:41:00.103 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:41:05.184 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:41:11.228 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:41:11.230 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:41:11.252 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:41:11.253 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:41:11.253 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:41:11.254 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:41:32.183 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:41:32.266 [http-nio-8891-exec-6] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:41:32.337 [http-nio-8891-exec-6] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:41:53.176 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:41:59.274 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:41:59.274 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:41:59.296 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:41:59.300 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:41:59.301 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:41:59.303 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:42:00.573 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:42:00.631 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:42:20.174 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:42:20.257 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:42:20.326 [http-nio-8891-exec-1] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:42:40.177 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:42:46.226 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:42:46.228 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:42:46.250 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:42:46.251 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:42:46.252 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:42:46.253 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:43:00.526 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:43:00.574 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:43:07.171 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:43:07.249 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:43:07.320 [http-nio-8891-exec-1] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:43:28.176 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:43:34.231 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:43:34.231 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:43:34.255 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:43:34.256 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:43:34.257 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:43:34.259 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:43:55.182 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:43:55.258 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:43:55.327 [http-nio-8891-exec-7] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:44:00.531 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:44:00.612 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:44:16.180 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:44:22.241 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:44:22.242 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:44:22.283 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:44:22.284 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:44:22.284 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:44:22.285 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:44:43.176 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:44:43.249 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:44:43.328 [http-nio-8891-exec-7] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:45:00.053 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:45:00.096 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:45:04.180 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:45:10.220 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:45:10.221 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:45:10.244 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:45:10.244 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:45:10.244 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:45:10.245 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:45:31.173 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:45:31.249 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:45:31.320 [http-nio-8891-exec-5] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:45:52.184 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:45:58.227 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:45:58.227 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:45:58.249 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:45:58.250 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:45:58.251 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:45:58.255 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:46:00.515 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:46:00.558 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:46:19.174 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:46:19.246 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:46:19.312 [http-nio-8891-exec-9] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:46:39.190 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:46:45.227 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:46:45.228 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:46:45.250 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:46:45.251 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:46:45.252 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:46:45.254 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:47:00.047 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:47:00.092 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:47:06.182 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:47:06.258 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:47:06.326 [http-nio-8891-exec-1] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:47:26.193 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:47:32.225 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:47:32.226 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:47:32.249 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:47:32.250 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:47:32.252 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:47:32.254 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:47:53.188 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:47:53.260 [http-nio-8891-exec-10] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:47:53.340 [http-nio-8891-exec-10] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:48:00.514 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:48:00.558 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:48:03.270 [http-nio-8891-exec-2] ERROR druid.sql.Statement:148 - {conn-10002, stmt-20069} execute error. SELECT 1
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 824,032 milliseconds ago. The last packet sent successfully to the server was 824,053 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1206)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2866)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeQuery(FilterAdapter.java:2513)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeQuery(FilterEventAdapter.java:296)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.wall.WallFilter.statement_executeQuery(WallFilter.java:547)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeQuery(FilterAdapter.java:2513)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeQuery(FilterEventAdapter.java:296)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeQuery(StatementProxyImpl.java:217)
	at com.alibaba.druid.pool.vendor.MySqlValidConnectionChecker.isValidConnection(MySqlValidConnectionChecker.java:143)
	at com.alibaba.druid.pool.DruidAbstractDataSource.testConnectionInternal(DruidAbstractDataSource.java:1514)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1549)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5058)
	at com.alibaba.druid.filter.logging.LogFilter.dataSource_getConnection(LogFilter.java:909)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.filter.FilterAdapter.dataSource_getConnection(FilterAdapter.java:2759)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.filter.stat.StatFilter.dataSource_getConnection(StatFilter.java:704)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1469)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1459)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at com.baomidou.dynamic.datasource.ds.ItemDataSource.getConnection(ItemDataSource.java:55)
	at com.baomidou.dynamic.datasource.ds.AbstractRoutingDataSource.getConnection(AbstractRoutingDataSource.java:54)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at sun.reflect.GeneratedMethodAccessor96.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy695.query(Unknown Source)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy695.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor132.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy191.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy194.selectList(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.list(IService.java:370)
	at com.baomidou.mybatisplus.extension.service.IService$$FastClassBySpringCGLIB$$f8525d18.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at org.jeecg.modules.system.service.impl.SysAnnouncementServiceImpl$$EnhancerBySpringCGLIB$$1ccda2.list(<generated>)
	at com.baomidou.mybatisplus.extension.service.IService$$FastClassBySpringCGLIB$$f8525d18.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at org.jeecg.modules.system.service.impl.SysAnnouncementServiceImpl$$EnhancerBySpringCGLIB$$de134430.list(<generated>)
	at org.jeecg.modules.system.controller.SysAnnouncementController.listByUser(SysAnnouncementController.java:353)
	at org.jeecg.modules.system.controller.SysAnnouncementController$$FastClassBySpringCGLIB$$15943ec.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:62)
	at sun.reflect.GeneratedMethodAccessor191.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.system.controller.SysAnnouncementController$$EnhancerBySpringCGLIB$$d5e06280.listByUser(<generated>)
	at sun.reflect.GeneratedMethodAccessor327.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:502)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:596)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:492)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 824,032 milliseconds ago. The last packet sent successfully to the server was 824,053 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:520)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:700)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:639)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:987)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryString(NativeProtocol.java:933)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:664)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1174)
	... 174 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:514)
	... 180 common frames omitted
2025-07-30 09:48:14.176 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:48:20.222 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:48:20.223 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:48:20.265 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:48:20.266 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:48:20.266 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:48:20.267 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:48:41.174 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:48:41.247 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:48:41.325 [http-nio-8891-exec-8] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:49:00.553 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:49:00.599 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:49:02.172 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:49:08.233 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:49:08.233 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:49:08.256 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:49:08.256 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:49:08.257 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:49:08.258 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:49:29.184 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:49:29.265 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:49:29.354 [http-nio-8891-exec-1] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:49:50.179 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:49:56.228 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:49:56.229 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:49:56.252 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:49:56.253 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:49:56.254 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:49:56.256 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:50:00.057 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:50:00.106 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:50:17.179 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:50:17.251 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:50:17.328 [http-nio-8891-exec-9] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:50:38.177 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:50:44.219 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:50:44.219 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:50:44.241 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:50:44.242 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:50:44.243 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:50:44.244 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:51:00.546 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:51:00.595 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:51:05.183 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:51:05.261 [http-nio-8891-exec-10] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:51:05.342 [http-nio-8891-exec-10] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:51:26.179 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:51:32.234 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:51:32.235 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:51:32.257 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:51:32.258 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:51:32.259 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:51:32.260 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:51:53.181 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:51:53.260 [http-nio-8891-exec-3] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:51:53.336 [http-nio-8891-exec-3] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:52:00.519 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:52:00.560 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:52:14.172 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:52:20.231 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:52:20.231 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:52:20.253 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:52:20.254 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:52:20.254 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:52:20.255 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:52:41.183 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:52:41.260 [http-nio-8891-exec-6] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:52:41.334 [http-nio-8891-exec-6] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:53:00.573 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:53:00.622 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:53:02.183 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:53:08.232 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:53:08.232 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:53:08.255 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:53:08.256 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:53:08.256 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:53:08.258 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:53:29.184 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:53:29.259 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:53:29.340 [http-nio-8891-exec-5] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:53:49.198 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:53:54.253 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:53:54.254 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:53:54.276 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:53:54.277 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:53:54.278 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:53:54.279 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:54:00.995 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:54:01.048 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:54:14.293 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:54:14.367 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:54:14.438 [http-nio-8891-exec-2] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:54:17.405 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:54:34.300 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:54:39.365 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:54:39.365 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:54:39.387 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:54:39.389 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:54:39.389 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:54:39.390 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:54:47.738 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:54:50.420 [http-nio-8891-exec-10] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:54:59.402 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:54:59.533 [http-nio-8891-exec-6] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:54:59.665 [http-nio-8891-exec-6] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:55:01.017 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:55:01.102 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:55:18.185 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:55:18.185 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:55:18.444 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:55:18.445 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:55:19.403 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:55:24.472 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:55:24.472 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:55:24.496 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:55:24.499 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:55:24.500 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:55:24.501 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:55:28.210 [http-nio-8891-exec-8] ERROR druid.sql.Statement:148 - {conn-10004, stmt-20096} execute error. SELECT 1
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1,278,656 milliseconds ago. The last packet sent successfully to the server was 1,278,686 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1206)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2866)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeQuery(FilterAdapter.java:2513)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeQuery(FilterEventAdapter.java:296)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.wall.WallFilter.statement_executeQuery(WallFilter.java:547)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeQuery(FilterAdapter.java:2513)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeQuery(FilterEventAdapter.java:296)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeQuery(StatementProxyImpl.java:217)
	at com.alibaba.druid.pool.vendor.MySqlValidConnectionChecker.isValidConnection(MySqlValidConnectionChecker.java:143)
	at com.alibaba.druid.pool.DruidAbstractDataSource.testConnectionInternal(DruidAbstractDataSource.java:1514)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1549)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5058)
	at com.alibaba.druid.filter.logging.LogFilter.dataSource_getConnection(LogFilter.java:909)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.filter.FilterAdapter.dataSource_getConnection(FilterAdapter.java:2759)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.filter.stat.StatFilter.dataSource_getConnection(StatFilter.java:704)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1469)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1459)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at com.baomidou.dynamic.datasource.ds.ItemDataSource.getConnection(ItemDataSource.java:55)
	at com.baomidou.dynamic.datasource.ds.AbstractRoutingDataSource.getConnection(AbstractRoutingDataSource.java:54)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at sun.reflect.GeneratedMethodAccessor96.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy695.query(Unknown Source)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy695.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor132.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy191.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy203.selectList(Unknown Source)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl.queryPermissionDataRule(SysBaseApiImpl.java:146)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$FastClassBySpringCGLIB$$93fd2f03.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$EnhancerBySpringCGLIB$$32e4b6a0.queryPermissionDataRule(<generated>)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$FastClassBySpringCGLIB$$93fd2f03.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$EnhancerBySpringCGLIB$$ae5942a0.queryPermissionDataRule(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:208)
	at com.sun.proxy.$Proxy161.queryPermissionDataRule(Unknown Source)
	at org.jeecg.common.aspect.PermissionDataAspect.arround(PermissionDataAspect.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:62)
	at sun.reflect.GeneratedMethodAccessor191.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.admin.controller.OrderManagementSystemController$$EnhancerBySpringCGLIB$$2d3bc4b0.queryPageList(<generated>)
	at org.jeecg.modules.admin.controller.OrderManagementSystemController$$FastClassBySpringCGLIB$$138fee5a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at org.jeecg.modules.admin.controller.OrderManagementSystemController$$EnhancerBySpringCGLIB$$98d11e88.queryPageList(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:502)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:596)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:492)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 1,278,656 milliseconds ago. The last packet sent successfully to the server was 1,278,686 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:520)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:700)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:639)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:987)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryString(NativeProtocol.java:933)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:664)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1174)
	... 192 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:514)
	... 198 common frames omitted
2025-07-30 09:55:28.210 [http-nio-8891-exec-10] ERROR druid.sql.Statement:148 - {conn-10005, stmt-20097} execute error. SELECT 1
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1,278,661 milliseconds ago. The last packet sent successfully to the server was 1,278,686 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1206)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2866)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeQuery(FilterAdapter.java:2513)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeQuery(FilterEventAdapter.java:296)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.wall.WallFilter.statement_executeQuery(WallFilter.java:547)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.filter.FilterAdapter.statement_executeQuery(FilterAdapter.java:2513)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_executeQuery(FilterEventAdapter.java:296)
	at com.alibaba.druid.filter.FilterChainImpl.statement_executeQuery(FilterChainImpl.java:2863)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.executeQuery(StatementProxyImpl.java:217)
	at com.alibaba.druid.pool.vendor.MySqlValidConnectionChecker.isValidConnection(MySqlValidConnectionChecker.java:143)
	at com.alibaba.druid.pool.DruidAbstractDataSource.testConnectionInternal(DruidAbstractDataSource.java:1514)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1549)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5058)
	at com.alibaba.druid.filter.logging.LogFilter.dataSource_getConnection(LogFilter.java:909)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.filter.FilterAdapter.dataSource_getConnection(FilterAdapter.java:2759)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.filter.stat.StatFilter.dataSource_getConnection(StatFilter.java:704)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:5054)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1469)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1459)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at com.baomidou.dynamic.datasource.ds.ItemDataSource.getConnection(ItemDataSource.java:55)
	at com.baomidou.dynamic.datasource.ds.AbstractRoutingDataSource.getConnection(AbstractRoutingDataSource.java:54)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:337)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:86)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at sun.reflect.GeneratedMethodAccessor96.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy695.query(Unknown Source)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy695.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor132.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy191.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy203.selectList(Unknown Source)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl.queryPermissionDataRule(SysBaseApiImpl.java:146)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$FastClassBySpringCGLIB$$93fd2f03.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$EnhancerBySpringCGLIB$$32e4b6a0.queryPermissionDataRule(<generated>)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$FastClassBySpringCGLIB$$93fd2f03.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$EnhancerBySpringCGLIB$$ae5942a0.queryPermissionDataRule(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:208)
	at com.sun.proxy.$Proxy161.queryPermissionDataRule(Unknown Source)
	at org.jeecg.common.aspect.PermissionDataAspect.arround(PermissionDataAspect.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:62)
	at sun.reflect.GeneratedMethodAccessor191.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.admin.controller.OrderManagementSystemController$$EnhancerBySpringCGLIB$$2d3bc4b0.queryPageList(<generated>)
	at org.jeecg.modules.admin.controller.OrderManagementSystemController$$FastClassBySpringCGLIB$$138fee5a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at org.jeecg.modules.admin.controller.OrderManagementSystemController$$EnhancerBySpringCGLIB$$98d11e88.queryPageList(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:502)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:596)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:492)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 1,278,661 milliseconds ago. The last packet sent successfully to the server was 1,278,686 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:520)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:700)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:639)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:987)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryString(NativeProtocol.java:933)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:664)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1174)
	... 192 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:514)
	... 198 common frames omitted
2025-07-30 09:55:28.267 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:55:28.268 [http-nio-8891-exec-10] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:55:28.347 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:55:28.365 [http-nio-8891-exec-10] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:55:28.394 [http-nio-8891-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:1085 - query auth sql is:
2025-07-30 09:55:28.395 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.OrderManagementSystemController:344 - 原始权限SQL: 
2025-07-30 09:55:28.395 [http-nio-8891-exec-8] WARN  o.j.m.a.controller.OrderManagementSystemController:348 - 数据权限SQL为空，请检查权限规则配置
2025-07-30 09:55:28.395 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.OrderManagementSystemController:353 - 当前用户: admin，ID: e9ca23d68d884d4ebb19d07889727dae, 部门: A02
2025-07-30 09:55:28.428 [http-nio-8891-exec-10] INFO  org.jeecg.common.system.query.QueryGenerator:1085 - query auth sql is:
2025-07-30 09:55:28.428 [http-nio-8891-exec-10] INFO  o.j.m.a.controller.OrderManagementSystemController:344 - 原始权限SQL: 
2025-07-30 09:55:28.429 [http-nio-8891-exec-10] WARN  o.j.m.a.controller.OrderManagementSystemController:348 - 数据权限SQL为空，请检查权限规则配置
2025-07-30 09:55:28.429 [http-nio-8891-exec-10] INFO  o.j.m.a.controller.OrderManagementSystemController:353 - 当前用户: admin，ID: e9ca23d68d884d4ebb19d07889727dae, 部门: A02
2025-07-30 09:55:33.217 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:55:33.311 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.OrderManagementSystemController:2161 - 找到产品 卓尔互动产品 的库存批次 1 个
2025-07-30 09:55:44.507 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:55:44.590 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:55:44.683 [http-nio-8891-exec-8] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:56:00.062 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:56:00.118 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:56:05.177 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:56:11.226 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:56:11.226 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:56:11.249 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:56:11.249 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:56:11.250 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:56:11.251 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:56:31.267 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:56:31.343 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:56:31.431 [http-nio-8891-exec-4] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:56:51.269 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:56:56.336 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:56:56.336 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:56:56.361 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:56:56.361 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:56:56.362 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:56:56.364 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:57:01.201 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:57:01.254 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:57:16.375 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:57:16.457 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:57:16.551 [http-nio-8891-exec-7] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:57:36.383 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:57:41.449 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:57:41.449 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:57:41.473 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:57:41.474 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:57:41.474 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:57:41.476 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:58:00.625 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:58:00.676 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:58:01.491 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:58:01.566 [http-nio-8891-exec-10] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:58:01.643 [http-nio-8891-exec-10] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:58:21.493 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:58:26.563 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:58:26.564 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:58:26.588 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:58:26.588 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:58:26.589 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:58:26.590 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:58:47.173 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:58:47.275 [http-nio-8891-exec-6] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:58:47.349 [http-nio-8891-exec-6] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:59:00.643 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 09:59:00.699 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 09:59:08.181 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 09:59:14.232 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:59:14.232 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 09:59:14.255 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 09:59:14.256 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 09:59:14.256 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 09:59:14.257 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 09:59:35.177 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 09:59:35.271 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 09:59:35.379 [http-nio-8891-exec-9] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 09:59:56.180 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:00:01.356 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:00:01.412 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:00:02.234 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:00:02.235 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:00:02.260 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:00:02.260 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:00:02.261 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:00:02.261 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:00:22.265 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:00:22.366 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:00:22.455 [http-nio-8891-exec-7] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:00:43.211 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:00:49.235 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:00:49.235 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:00:49.261 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:00:49.262 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:00:49.262 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:00:49.264 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:01:01.260 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:01:01.305 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:01:10.249 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:01:10.427 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:01:10.547 [http-nio-8891-exec-2] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:01:30.203 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:01:34.070 [http-nio-8891-exec-3] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:01:34.178 [http-nio-8891-exec-3] INFO  o.j.m.a.controller.OrderManagementSystemController:2161 - 找到产品 cppm注册采购经理证书 的库存批次 1 个
2025-07-30 10:01:35.285 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:01:35.286 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:01:35.315 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:01:35.317 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:01:35.317 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:01:35.318 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:01:37.050 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:01:37.156 [http-nio-8891-exec-1] INFO  o.j.m.a.controller.OrderManagementSystemController:2161 - 找到产品 cppm注册采购经理证书 的库存批次 1 个
2025-07-30 10:01:39.338 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:01:39.429 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.OrderManagementSystemController:2161 - 找到产品 cppm注册采购经理证书 的库存批次 1 个
2025-07-30 10:01:55.325 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:01:55.423 [http-nio-8891-exec-6] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:01:55.509 [http-nio-8891-exec-6] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:02:01.283 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:02:01.337 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:02:15.332 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:02:20.395 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:02:20.396 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:02:20.425 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:02:20.425 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:02:20.425 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:02:20.426 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:02:20.732 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:02:20.827 [http-nio-8891-exec-7] INFO  o.j.m.a.controller.OrderManagementSystemController:2161 - 找到产品 卓尔互动产品 的库存批次 1 个
2025-07-30 10:02:28.357 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:02:31.788 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:02:31.796 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:02:32.384 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:02:40.432 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:02:40.521 [http-nio-8891-exec-10] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:02:40.623 [http-nio-8891-exec-10] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:02:44.403 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:02:46.219 [http-nio-8891-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:648 - ---查询过滤器，Query规则---field:im_inventory_type, rule:=, value:1
2025-07-30 10:02:46.223 [http-nio-8891-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:648 - ---查询过滤器，Query规则---field:sys_org_code, rule:=, value:A02
2025-07-30 10:02:46.223 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:02:46.413 [http-nio-8891-exec-1] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 ces 的采购记录，使用原有单价
2025-07-30 10:02:46.453 [http-nio-8891-exec-1] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 测试11 的采购记录，使用原有单价
2025-07-30 10:02:46.490 [http-nio-8891-exec-1] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 测试11 的采购记录，使用原有单价
2025-07-30 10:02:46.526 [http-nio-8891-exec-1] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 11 的采购记录，使用原有单价
2025-07-30 10:02:46.566 [http-nio-8891-exec-1] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 请问 的采购记录，使用原有单价
2025-07-30 10:02:46.603 [http-nio-8891-exec-1] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 十大股东符号 的采购记录，使用原有单价
2025-07-30 10:02:46.638 [http-nio-8891-exec-1] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 打底请问 的采购记录，使用原有单价
2025-07-30 10:02:46.674 [http-nio-8891-exec-1] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 测额 的采购记录，使用原有单价
2025-07-30 10:02:46.710 [http-nio-8891-exec-1] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 请大师的 的采购记录，使用原有单价
2025-07-30 10:02:46.746 [http-nio-8891-exec-1] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 泰山 的采购记录，使用原有单价
2025-07-30 10:02:48.207 [http-nio-8891-exec-4] INFO  o.j.m.a.controller.InventoryManagementController:380 - 未找到物料 ces 的采购记录
2025-07-30 10:02:50.443 [http-nio-8891-exec-10] INFO  o.j.m.a.controller.InventoryManagementController:380 - 未找到物料 ces 的采购记录
2025-07-30 10:03:00.433 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:03:01.325 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:03:01.382 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:03:01.978 [http-nio-8891-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:648 - ---查询过滤器，Query规则---field:im_inventory_type, rule:=, value:2
2025-07-30 10:03:01.979 [http-nio-8891-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:648 - ---查询过滤器，Query规则---field:sys_org_code, rule:=, value:A02
2025-07-30 10:03:01.979 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:03:02.173 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 123 的生产订单记录，使用原有成本
2025-07-30 10:03:02.205 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 CPPM注册采购经理证书 的生产订单记录，使用原有成本
2025-07-30 10:03:02.237 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 打个雷上岛咖啡 的生产订单记录，使用原有成本
2025-07-30 10:03:02.270 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 99 的生产订单记录，使用原有成本
2025-07-30 10:03:02.304 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 3333 的生产订单记录，使用原有成本
2025-07-30 10:03:02.337 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 2222 的生产订单记录，使用原有成本
2025-07-30 10:03:02.373 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:733 - 产品 橙子 计算平均成本成功: 20000.00 (基于 1 条生产订单记录)
2025-07-30 10:03:02.409 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 测试1111111 的生产订单记录，使用原有成本
2025-07-30 10:03:02.443 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 采购工程师 的生产订单记录，使用原有成本
2025-07-30 10:03:02.477 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 大蒜 的生产订单记录，使用原有成本
2025-07-30 10:03:03.419 [http-nio-8891-exec-5] INFO  o.j.m.a.controller.InventoryManagementController:451 - 未找到产品 123 的生产订单记录
2025-07-30 10:03:05.326 [http-nio-8891-exec-6] INFO  o.j.m.a.controller.InventoryManagementController:451 - 未找到产品 123 的生产订单记录
2025-07-30 10:03:05.486 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:03:05.487 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:03:05.512 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:03:05.513 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:03:05.513 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:03:05.514 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:03:06.812 [http-nio-8891-exec-4] INFO  o.j.m.a.controller.InventoryManagementController:451 - 未找到产品 CPPM注册采购经理证书 的生产订单记录
2025-07-30 10:03:08.807 [http-nio-8891-exec-10] INFO  o.j.m.a.controller.InventoryManagementController:451 - 未找到产品 CPPM注册采购经理证书 的生产订单记录
2025-07-30 10:03:11.928 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:451 - 未找到产品 123 的生产订单记录
2025-07-30 10:03:25.529 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:03:25.629 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:03:25.737 [http-nio-8891-exec-9] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:03:45.542 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:03:50.614 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:03:50.614 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:03:50.643 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:03:50.644 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:03:50.644 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:03:50.645 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:04:00.784 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:04:00.852 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:04:10.661 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:04:10.763 [http-nio-8891-exec-3] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:04:10.863 [http-nio-8891-exec-3] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:04:30.664 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:04:35.737 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:04:35.737 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:04:35.766 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:04:35.767 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:04:35.767 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:04:35.768 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:04:55.777 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:04:55.867 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:04:55.975 [http-nio-8891-exec-5] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:05:01.408 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:05:01.474 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:05:15.785 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:05:20.852 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:05:20.853 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:05:20.877 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:05:20.878 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:05:20.879 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:05:20.879 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:05:40.889 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:05:40.985 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:05:41.074 [http-nio-8891-exec-4] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:06:00.067 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:06:00.130 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:06:00.904 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:06:05.987 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:06:05.987 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:06:06.017 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:06:06.017 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:06:06.018 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:06:06.019 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:06:26.181 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:06:26.264 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:06:26.374 [http-nio-8891-exec-8] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:06:47.174 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:06:53.248 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:06:53.249 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:06:53.282 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:06:53.283 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:06:53.283 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:06:53.284 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:07:01.459 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:07:01.514 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:07:14.182 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:07:14.295 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:07:14.399 [http-nio-8891-exec-5] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:07:35.172 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:07:41.240 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:07:41.241 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:07:41.273 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:07:41.274 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:07:41.274 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:07:41.275 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:08:01.328 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:08:01.390 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:08:02.175 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:08:02.500 [http-nio-8891-exec-10] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:08:02.593 [http-nio-8891-exec-10] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:08:23.178 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:08:29.235 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:08:29.236 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:08:29.265 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:08:29.266 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:08:29.266 [http-nio-8891-exec-4] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:08:29.268 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:08:50.180 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:08:50.269 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:08:50.360 [http-nio-8891-exec-5] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:09:00.647 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:09:00.701 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:09:11.176 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:09:17.222 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:09:17.222 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:09:17.248 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:09:17.248 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:09:17.248 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:09:17.249 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:09:38.185 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:09:38.279 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:09:38.385 [http-nio-8891-exec-9] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:09:59.176 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:10:00.655 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:10:00.710 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:10:05.228 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:10:05.228 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:10:05.254 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:10:05.255 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:10:05.256 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:10:05.257 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:10:26.183 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:10:26.262 [http-nio-8891-exec-6] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:10:26.352 [http-nio-8891-exec-6] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:10:46.194 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:10:52.228 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:10:52.229 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:10:52.255 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:10:52.256 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:10:52.256 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:10:52.258 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:11:00.689 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:11:00.748 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:11:13.171 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:11:13.254 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:11:13.354 [http-nio-8891-exec-1] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:11:30.052 [http-nio-8891-exec-1] INFO  o.j.m.a.controller.InventoryManagementController:451 - 未找到产品 123 的生产订单记录
2025-07-30 10:11:33.184 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:11:38.240 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:11:38.241 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:11:38.262 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:11:38.263 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:11:38.263 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:11:38.264 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:11:59.180 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:11:59.262 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:11:59.350 [http-nio-8891-exec-5] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:12:00.585 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:12:00.630 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:12:20.178 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:12:26.233 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:12:26.234 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:12:26.261 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:12:26.261 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:12:26.262 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:12:26.263 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:12:47.173 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:12:47.269 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:12:47.356 [http-nio-8891-exec-1] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:13:00.063 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:13:00.117 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:13:08.183 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:13:14.235 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:13:14.235 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:13:14.264 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:13:14.264 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:13:14.264 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:13:14.265 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:13:35.178 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:13:35.282 [http-nio-8891-exec-6] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:13:35.379 [http-nio-8891-exec-6] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:13:56.170 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:14:01.275 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:14:01.337 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:14:02.248 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:14:02.249 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:14:02.279 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:14:02.279 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:14:02.280 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:14:02.280 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:14:23.184 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:14:23.275 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:14:23.380 [http-nio-8891-exec-1] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:14:44.178 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:14:50.225 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:14:50.225 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:14:50.249 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:14:50.249 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:14:50.250 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:14:50.252 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:15:00.645 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:15:00.701 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:15:11.183 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:15:11.274 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:15:11.372 [http-nio-8891-exec-5] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:15:32.173 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:15:38.232 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:15:38.233 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:15:38.263 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:15:38.264 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:15:38.265 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:15:38.267 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:15:59.176 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:15:59.274 [http-nio-8891-exec-3] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:15:59.386 [http-nio-8891-exec-3] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:16:00.078 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:16:00.146 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:16:20.176 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:16:26.242 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:16:26.243 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:16:26.271 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:16:26.272 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:16:26.274 [http-nio-8891-exec-9] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:16:26.276 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:16:47.180 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:16:47.271 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:16:47.374 [http-nio-8891-exec-1] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:17:00.720 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:17:00.787 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:17:08.177 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:17:14.244 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:17:14.244 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:17:14.273 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:17:14.274 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:17:14.274 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:17:14.278 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:17:35.182 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:17:35.282 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:17:35.388 [http-nio-8891-exec-5] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:17:47.297 [http-nio-8891-exec-2] INFO  org.jeecg.common.system.query.QueryGenerator:648 - ---查询过滤器，Query规则---field:im_inventory_type, rule:=, value:1
2025-07-30 10:17:47.297 [http-nio-8891-exec-2] INFO  org.jeecg.common.system.query.QueryGenerator:648 - ---查询过滤器，Query规则---field:sys_org_code, rule:=, value:A02
2025-07-30 10:17:47.298 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:17:47.492 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 ces 的采购记录，使用原有单价
2025-07-30 10:17:47.533 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 测试11 的采购记录，使用原有单价
2025-07-30 10:17:47.572 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 测试11 的采购记录，使用原有单价
2025-07-30 10:17:47.611 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 11 的采购记录，使用原有单价
2025-07-30 10:17:47.647 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 请问 的采购记录，使用原有单价
2025-07-30 10:17:47.682 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 十大股东符号 的采购记录，使用原有单价
2025-07-30 10:17:47.719 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 打底请问 的采购记录，使用原有单价
2025-07-30 10:17:47.756 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 测额 的采购记录，使用原有单价
2025-07-30 10:17:47.792 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 请大师的 的采购记录，使用原有单价
2025-07-30 10:17:47.828 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 泰山 的采购记录，使用原有单价
2025-07-30 10:17:48.783 [http-nio-8891-exec-9] INFO  o.j.m.a.controller.InventoryManagementController:380 - 未找到物料 ces 的采购记录
2025-07-30 10:17:55.187 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:18:00.258 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:18:00.259 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:18:00.287 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:18:00.287 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:18:00.288 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:18:00.288 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:18:01.166 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:18:01.225 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:18:20.292 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:18:20.372 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:18:20.463 [http-nio-8891-exec-5] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:18:41.173 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:18:47.241 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:18:47.243 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:18:47.268 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:18:47.269 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:18:47.270 [http-nio-8891-exec-5] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:18:47.271 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:19:00.058 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:19:00.112 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:19:08.174 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:19:08.261 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:19:08.355 [http-nio-8891-exec-4] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:19:29.171 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:19:35.233 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:19:35.234 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:19:35.260 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:19:35.261 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:19:35.262 [http-nio-8891-exec-7] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:19:35.263 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:19:56.181 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:19:56.257 [http-nio-8891-exec-9] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:19:56.347 [http-nio-8891-exec-9] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:20:01.282 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:20:01.339 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:20:17.185 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:20:23.228 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:20:23.229 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:20:23.256 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:20:23.257 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:20:23.258 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:20:23.259 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:20:44.171 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:20:44.248 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:20:44.339 [http-nio-8891-exec-8] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:21:00.716 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:21:00.775 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:21:05.172 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:21:11.241 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:21:11.241 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:21:11.272 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:21:11.272 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:21:11.274 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:21:11.276 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:21:32.171 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:21:32.253 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:21:32.364 [http-nio-8891-exec-7] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:21:53.178 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:21:59.246 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:21:59.247 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:21:59.275 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:21:59.275 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:21:59.278 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:21:59.280 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:22:01.166 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:22:01.223 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:22:20.182 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:22:20.291 [http-nio-8891-exec-10] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:22:20.414 [http-nio-8891-exec-10] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:22:41.185 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:22:47.236 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:22:47.237 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:22:47.264 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:22:47.265 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:22:47.267 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:22:47.269 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:23:01.360 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:23:01.429 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:23:08.173 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:23:08.257 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:23:08.368 [http-nio-8891-exec-8] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:23:25.677 [http-nio-8891-exec-5] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:23:28.186 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:23:33.270 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:23:33.271 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:23:33.302 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:23:33.303 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:23:33.303 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:23:33.305 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:23:54.173 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:23:54.312 [http-nio-8891-exec-6] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:23:54.434 [http-nio-8891-exec-6] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:24:01.316 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:24:01.411 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:24:15.179 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:24:21.239 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:24:21.239 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:24:21.270 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:24:21.271 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:24:21.272 [http-nio-8891-exec-6] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:24:21.273 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:24:42.178 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:24:42.262 [http-nio-8891-exec-3] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:24:42.364 [http-nio-8891-exec-3] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:25:00.695 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:25:00.766 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:25:03.175 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:25:09.235 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:25:09.235 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:25:09.265 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:25:09.266 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:25:09.267 [http-nio-8891-exec-10] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:25:09.267 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:25:30.181 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:25:30.295 [http-nio-8891-exec-4] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:25:30.404 [http-nio-8891-exec-4] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:25:51.171 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:25:57.231 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:25:57.232 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:25:57.260 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:25:57.261 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:25:57.262 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:25:57.264 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:26:00.070 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:26:00.128 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:26:18.172 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:26:18.259 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:26:18.361 [http-nio-8891-exec-8] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:26:39.181 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:26:45.241 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:26:45.242 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:26:45.269 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:26:45.271 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:26:45.272 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:26:45.274 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:26:51.713 [http-nio-8891-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:648 - ---查询过滤器，Query规则---field:im_inventory_type, rule:=, value:1
2025-07-30 10:26:51.714 [http-nio-8891-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:648 - ---查询过滤器，Query规则---field:sys_org_code, rule:=, value:A02
2025-07-30 10:26:51.714 [http-nio-8891-exec-8] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:26:51.903 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 ces 的采购记录，使用原有单价
2025-07-30 10:26:51.941 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 测试11 的采购记录，使用原有单价
2025-07-30 10:26:51.981 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 测试11 的采购记录，使用原有单价
2025-07-30 10:26:52.018 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 11 的采购记录，使用原有单价
2025-07-30 10:26:52.052 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 请问 的采购记录，使用原有单价
2025-07-30 10:26:52.087 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 十大股东符号 的采购记录，使用原有单价
2025-07-30 10:26:52.125 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 打底请问 的采购记录，使用原有单价
2025-07-30 10:26:52.161 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 测额 的采购记录，使用原有单价
2025-07-30 10:26:52.196 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 请大师的 的采购记录，使用原有单价
2025-07-30 10:26:52.231 [http-nio-8891-exec-8] INFO  o.j.m.a.controller.InventoryManagementController:636 - 未找到物料 泰山 的采购记录，使用原有单价
2025-07-30 10:26:53.276 [http-nio-8891-exec-2] INFO  org.jeecg.common.system.query.QueryGenerator:648 - ---查询过滤器，Query规则---field:im_inventory_type, rule:=, value:2
2025-07-30 10:26:53.277 [http-nio-8891-exec-2] INFO  org.jeecg.common.system.query.QueryGenerator:648 - ---查询过滤器，Query规则---field:sys_org_code, rule:=, value:A02
2025-07-30 10:26:53.277 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:26:53.476 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 123 的生产订单记录，使用原有成本
2025-07-30 10:26:53.517 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 CPPM注册采购经理证书 的生产订单记录，使用原有成本
2025-07-30 10:26:53.556 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 打个雷上岛咖啡 的生产订单记录，使用原有成本
2025-07-30 10:26:53.596 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 99 的生产订单记录，使用原有成本
2025-07-30 10:26:53.635 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 3333 的生产订单记录，使用原有成本
2025-07-30 10:26:53.674 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 2222 的生产订单记录，使用原有成本
2025-07-30 10:26:53.716 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:733 - 产品 橙子 计算平均成本成功: 20000.00 (基于 1 条生产订单记录)
2025-07-30 10:26:53.754 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 测试1111111 的生产订单记录，使用原有成本
2025-07-30 10:26:53.792 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 采购工程师 的生产订单记录，使用原有成本
2025-07-30 10:26:53.830 [http-nio-8891-exec-2] INFO  o.j.m.a.controller.InventoryManagementController:705 - 未找到产品 大蒜 的生产订单记录，使用原有成本
2025-07-30 10:27:00.844 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:27:00.914 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:27:05.284 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:27:05.371 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:27:05.481 [http-nio-8891-exec-1] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:27:25.293 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:27:30.367 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:27:30.367 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:27:30.399 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:27:30.400 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:27:30.400 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:27:30.401 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:27:30.556 [http-nio-8891-exec-5] INFO  o.j.m.a.controller.InventoryManagementController:451 - 未找到产品 123 的生产订单记录
2025-07-30 10:27:50.411 [http-nio-8891-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:27:50.515 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:27:50.617 [http-nio-8891-exec-7] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:28:00.067 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:28:00.174 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:28:10.413 [http-nio-8891-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:28:15.479 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:28:15.479 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:28:15.505 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:28:15.506 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:28:15.506 [http-nio-8891-exec-1] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:28:15.507 [http-nio-8891-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:28:36.179 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:28:36.257 [http-nio-8891-exec-10] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:28:36.341 [http-nio-8891-exec-10] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:28:57.171 [http-nio-8891-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:29:00.641 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:29:00.700 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:29:03.238 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:29:03.238 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:29:03.266 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:29:03.267 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:29:03.268 [http-nio-8891-exec-3] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:29:03.270 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:29:23.279 [http-nio-8891-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:29:23.372 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:29:23.458 [http-nio-8891-exec-2] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:29:43.292 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:29:49.221 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:29:49.222 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:29:49.245 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:29:49.246 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:29:49.246 [http-nio-8891-exec-2] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:29:49.247 [http-nio-8891-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:30:01.128 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:30:01.184 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:30:10.173 [http-nio-8891-exec-5] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:30:10.254 [http-nio-8891-exec-7] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:30:10.349 [http-nio-8891-exec-7] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:30:30.175 [http-nio-8891-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:30:35.430 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:130 -  -- token --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:30:35.431 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:131 -  -- user： --：admin，a8b88c0c7a3f4c96
2025-07-30 10:30:35.457 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:133 -  -- cacheToken --：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTM5NzcxNTAsInVzZXJuYW1lIjoiYWRtaW4ifQ.VCNeKg5yHJW8D80uaNOyeAwO0GF9IxYknvtbu-ci_1g
2025-07-30 10:30:35.457 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:134 -  -- 验证是否成功1 --：true
2025-07-30 10:30:35.457 [http-nio-8891-exec-8] INFO  org.jeecg.common.util.TokenUtils:135 -  -- 验证是否成功2 --：true
2025-07-30 10:30:35.458 [http-nio-8891-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-07-30 10:30:55.469 [http-nio-8891-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:109 - 【系统 WebSocket】收到客户端消息:HeartBeat
2025-07-30 10:30:55.552 [http-nio-8891-exec-2] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:30:55.643 [http-nio-8891-exec-2] INFO  o.j.modules.system.controller.SysDepartController:1039 - 【获取企业过期状态】企业ID：6d35e179cd814e3299bd588ea7daed3f, 企业：企智芯（后台），状态：正常，过期日期：Thu Dec 31 23:59:59 CST 2099
2025-07-30 10:31:01.056 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:31:01.108 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:31:13.106 [SpringApplicationShutdownHook] INFO  org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-07-30 10:31:13.282 [SpringApplicationShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:847 - Shutting down Quartz Scheduler
2025-07-30 10:31:13.283 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_技术*************** shutting down.
2025-07-30 10:31:13.285 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_技术*************** paused.
2025-07-30 10:31:13.287 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_技术*************** shutdown complete.
2025-07-30 10:31:13.320 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:211 - dynamic-datasource start closing ....
2025-07-30 10:31:13.324 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2175 - {dataSource-1} closing ...
2025-07-30 10:31:13.343 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2248 - {dataSource-1} closed
2025-07-30 10:31:13.344 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:215 - dynamic-datasource all closed success,bye
2025-07-30 10:31:20.415 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 10:31:20.468 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication using Java 1.8.0_351 on 技术02 with PID 5340 (D:\developing\java\Code\IDEA\jiahua_ai_java\jeecg-module-system\jeecg-system-start\target\classes started by zyd in D:\developing\java\Code\IDEA\jiahua_ai_java)
2025-07-30 10:31:20.469 [main] INFO  org.jeecg.JeecgSystemApplication:637 - The following 1 profile is active: "dev"
2025-07-30 10:31:23.678 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 10:31:23.683 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 10:31:23.836 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 125 ms. Found 0 Redis repository interfaces.
2025-07-30 10:31:24.001 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-30 10:31:24.002 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*,org.jeecg.modules.drag.*
2025-07-30 10:31:24.003 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-30 10:31:25.459 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-30 10:31:25.459 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-30 10:31:25.460 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-30 10:31:25.460 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-30 10:31:25.460 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-30 10:31:25.460 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-30 10:31:25.461 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-30 10:31:25.461 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-30 10:31:25.461 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-30 10:31:25.461 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-30 10:31:25.461 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragCompDao }
2025-07-30 10:31:25.462 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetHeadDao }
2025-07-30 10:31:25.462 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetItemDao }
2025-07-30 10:31:25.462 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetParamDao }
2025-07-30 10:31:25.462 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDataSourceDao }
2025-07-30 10:31:25.462 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageCompDao }
2025-07-30 10:31:25.462 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageDao }
2025-07-30 10:31:25.466 [main] INFO  o.s.c.annotation.ConfigurationClassPostProcessor:426 - Cannot enhance @Configuration bean definition 'com.yomahub.liteflow.springboot.config.LiteflowMainAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-07-30 10:31:25.925 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#16' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.930 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragPageDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.931 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#15' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.932 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragPageCompDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.932 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#14' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.933 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.934 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#13' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.934 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragDatasetParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.935 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#12' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.936 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragDatasetItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.937 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#11' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.938 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragDatasetHeadDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.938 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#10' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.939 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'onlDragCompDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.939 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.940 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.941 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.941 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.942 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.942 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.943 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.944 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.944 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.945 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.945 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.946 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.946 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.947 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.948 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.948 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.949 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.950 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.950 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#426940f9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.951 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.973 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:25.975 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.283 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.285 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.290 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.292 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusConfig' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.293 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'collectorRegistry' of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.294 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.295 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerClock' of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.321 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusMeterRegistry' of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.324 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerOptions' of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.325 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceMetrics' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$509/894818308] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.508 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.692 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.697 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jeecgBaseConfig' of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:26.698 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$621d08f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.275 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.281 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration$$EnhancerBySpringCGLIB$$28573fea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.302 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.356 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.601 [main] INFO  org.jeecg.config.shiro.ShiroConfig:242 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-30 10:31:27.605 [main] INFO  org.jeecg.config.shiro.ShiroConfig:253 - ===============(2)创建RedisManager,连接Redis..
2025-07-30 10:31:27.612 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.619 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.649 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.685 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.yomahub.liteflow.springboot.config.LiteflowPropertyAutoConfiguration' of type [com.yomahub.liteflow.springboot.config.LiteflowPropertyAutoConfiguration$$EnhancerBySpringCGLIB$$6d576112] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.696 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'liteflow-com.yomahub.liteflow.springboot.LiteflowProperty' of type [com.yomahub.liteflow.springboot.LiteflowProperty] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.703 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'liteflow.monitor-com.yomahub.liteflow.springboot.LiteflowMonitorProperty' of type [com.yomahub.liteflow.springboot.LiteflowMonitorProperty] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.707 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'liteflowConfig' of type [com.yomahub.liteflow.property.LiteflowConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.711 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.yomahub.liteflow.spi.spring.SpringAware' of type [com.yomahub.liteflow.spi.spring.SpringAware] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.725 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$b9fe6646] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:27.729 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 10:31:28.350 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8891 (http)
2025-07-30 10:31:28.372 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8891"]
2025-07-30 10:31:28.372 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-30 10:31:28.372 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-30 10:31:28.520 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/zggp]:173 - Initializing Spring embedded WebApplicationContext
2025-07-30 10:31:28.521 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 7719 ms
2025-07-30 10:31:28.810 [main] INFO  o.j.m.jmreport.config.init.JimuReportConfiguration:90 -  Init JimuReport Config [ Token Interceptor & Resource Locations ] 
2025-07-30 10:31:31.862 [main] INFO  com.alibaba.druid.pool.DruidDataSource:1010 - {dataSource-1,master} inited
2025-07-30 10:31:31.865 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:154 - dynamic-datasource - add a datasource named [master] success
2025-07-30 10:31:31.865 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-30 10:31:34.283 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper:347 - Can not find table primary key in Class: "org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatPermanentCode".
2025-07-30 10:31:34.283 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatPermanentCode ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 10:31:35.000 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper:347 - Can not find table primary key in Class: "org.jeecg.modules.xinghuo.vo.ExportSysUser".
2025-07-30 10:31:35.001 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class org.jeecg.modules.xinghuo.vo.ExportSysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 10:31:35.613 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:56 -  --- redis config init --- 
2025-07-30 10:31:40.061 [main] INFO  o.j.m.admin.client.impl.XunfeiVoiceCloneClientImpl:101 - OkHttpClient初始化成功，使用TLSv1.2协议
2025-07-30 10:31:52.193 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper:347 - Can not find table primary key in Class: "org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatCustomer".
2025-07-30 10:31:52.194 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatCustomer ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 10:31:55.138 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-30 10:31:55.152 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-30 10:31:55.237 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 10:31:55.238 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-30 10:31:55.266 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-30 10:31:55.277 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-30 10:31:55.282 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId '技术021753842715141'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-30 10:31:55.282 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-30 10:31:55.282 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-30 10:31:55.283 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2463a9ec
2025-07-30 10:31:56.161 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:94 - 已从文件加载私钥: classpath:coze/coze_private_key.pem
2025-07-30 10:31:56.161 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:171 - 使用API基础URL: https://api.coze.cn
2025-07-30 10:31:56.265 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:184 - JWT OAuth客户端初始化成功
2025-07-30 10:31:57.920 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:215 - 获取访问令牌成功，有效期至: Wed Jul 30 10:35:17 CST 2025
2025-07-30 10:31:58.169 [main] INFO  org.jeecg.modules.coze.util.JWTOAuthUtil:194 - CozeAPI客户端初始化成功
2025-07-30 10:32:00.187 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper:347 - Can not find table primary key in Class: "org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatSchedule".
2025-07-30 10:32:00.188 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class org.jeecg.modules.enterpriseWechat.entity.EnterpriseWechatSchedule ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 10:32:02.050 [main] INFO  o.jeecg.modules.message.handle.utils.BaiduSmsUtil:52 - 百度短信客户端初始化成功
2025-07-30 10:32:08.080 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:42 -  Init JimuReport Config [ 线程池 ] 
2025-07-30 10:32:08.852 [main] INFO  c.y.l.process.impl.CmpAroundAspectBeanProcess:32 - component aspect implement[nodeProcessAspect] has been found
2025-07-30 10:32:10.518 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[classifier] has been found
2025-07-30 10:32:10.532 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[end] has been found
2025-07-30 10:32:10.539 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[enhanceJava] has been found
2025-07-30 10:32:10.548 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[http] has been found
2025-07-30 10:32:10.557 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[knowledge] has been found
2025-07-30 10:32:10.566 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[llm] has been found
2025-07-30 10:32:10.572 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[reply] has been found
2025-07-30 10:32:10.578 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[start] has been found
2025-07-30 10:32:10.583 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[subflow] has been found
2025-07-30 10:32:10.589 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[switch] has been found
2025-07-30 10:32:13.762 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-30 10:32:13.951 [main] INFO  org.jeecg.config.TaskExecutorConfig:24 - 初始化异步任务线程池 taskExecutor
2025-07-30 10:32:13.993 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  Init CodeGenerate Config [ Get Db Config From application.yml ] 
2025-07-30 10:32:16.988 [main] INFO  c.y.liteflow.parser.factory.FlowParserProvider:193 - flow info loaded from class config with el,class=com.yomahub.liteflow.parser.sql.SQLXmlELParser
2025-07-30 10:32:17.103 [main] INFO  c.yomahub.liteflow.parser.sql.read.AbstractSqlRead:186 - query sql: select id, application_name, chain from airag_flow where status = 'enable' and chain is not null
2025-07-30 10:32:17.512 [main] INFO  c.y.liteflow.parser.sql.util.LiteFlowJdbcUtil:43 - use dataSourceName[dataSource],has found liteflow config
2025-07-30 10:32:18.159 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8891"]
2025-07-30 10:32:18.212 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8891 (http) with context path '/zggp'
2025-07-30 10:32:28.577 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 68.983 seconds (JVM running for 70.049)
2025-07-30 10:32:28.663 [main] INFO  org.jeecg.modules.admin.job.RegisterCrmJobs:28 - ==== 开始注册CRM定时任务 ====
2025-07-30 10:32:30.171 [http-nio-8891-exec-2] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/zggp]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:32:30.171 [http-nio-8891-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-30 10:32:30.186 [http-nio-8891-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 15 ms
2025-07-30 10:32:30.258 [main] INFO  org.jeecg.modules.admin.job.RegisterCrmJobs:85 - 定时任务[客户自动回收任务]已存在，无需重复创建
2025-07-30 10:32:30.284 [main] INFO  org.jeecg.modules.admin.job.RegisterCrmJobs:85 - 定时任务[客户回收预警任务]已存在，无需重复创建
2025-07-30 10:32:30.284 [main] INFO  org.jeecg.modules.admin.job.RegisterCrmJobs:49 - ==== CRM定时任务注册完成 ====
2025-07-30 10:32:30.288 [main] INFO  org.jeecg.config.init.CodeTemplateInitListener:29 -  Init Code Generate Template [ 检测如果是JAR启动环境，Copy模板到config目录 ] 
2025-07-30 10:32:30.363 [main] INFO  org.jeecg.JeecgSystemApplication:38 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8891/zggp/
	External: 	http://************:8891/zggp/
	Swagger文档: 	http://************:8891/zggp/doc.html
----------------------------------------------------------
2025-07-30 10:32:34.549 [http-nio-8891-exec-3] INFO  o.j.m.a.controller.InventoryManagementController:451 - 未找到产品 123 的生产订单记录
2025-07-30 10:33:01.604 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:33:01.656 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:34:00.059 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:34:00.116 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:35:01.027 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:35:01.070 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:35:24.802 [http-nio-8891-exec-10] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:35:40.028 [http-nio-8891-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:648 - ---查询过滤器，Query规则---field:material_approval_status, rule:=, value:3
2025-07-30 10:35:40.031 [http-nio-8891-exec-1] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
2025-07-30 10:36:00.053 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:36:00.096 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:37:01.200 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:37:01.253 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:38:00.079 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:38:00.140 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:39:01.275 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:39:01.337 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:40:00.077 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:40:00.145 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:41:01.426 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:286 - 【项目管理-排期模式】自动开始任务数: 0
2025-07-30 10:41:01.490 [scheduling-1] INFO  o.j.m.admin.service.impl.WorkOrderTaskServiceImpl:293 - 【项目管理-排期模式】自动结束任务数: 0
2025-07-30 10:41:14.835 [lettuce-eventExecutorLoop-1-13] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was /116.255.206.98:6379
2025-07-30 10:41:15.000 [lettuce-nioEventLoop-4-4] INFO  io.lettuce.core.protocol.ReconnectionHandler:174 - Reconnected to 116.255.206.98:6379
2025-07-30 10:41:15.351 [http-nio-8891-exec-10] INFO  o.j.m.system.service.impl.SysDepartServiceImpl:1147 - 【根据用户ID获取所在企业ID】：用户：LoginUser(id=e9ca23d68d884d4ebb19d07889727dae, username=admin, realname=企智芯, password=a8b88c0c7a3f4c96, orgCode=A02, avatar=https://qianchengbangai.oss-cn-beijing.aliyuncs.com/temp/企智芯定稿_1744354039998.jpg, birthday=Wed Dec 05 00:00:00 CST 2018, sex=1, email=<EMAIL>, phone=18611111111, status=1, delFlag=0, activitiSync=1, createTime=Fri Jun 21 17:54:10 CST 2019, userIdentity=2, departIds=, post=总经理, telephone=null, relTenantIds=, clientId=a0cb6448ba001d1dbd5e8c6f011482dd)
