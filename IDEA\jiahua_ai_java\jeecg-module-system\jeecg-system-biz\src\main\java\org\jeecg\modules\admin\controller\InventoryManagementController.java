package org.jeecg.modules.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.aspect.annotation.PermissionData;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.admin.entity.InventoryManagement;
import org.jeecg.modules.admin.entity.PurchasedItems;
import org.jeecg.modules.admin.entity.PurchaseOrderManagement;
import org.jeecg.modules.admin.entity.OrderManagementSystem;
import org.jeecg.modules.admin.entity.ProductionOrder;
import org.jeecg.modules.admin.service.IInventoryManagementService;
import org.jeecg.modules.admin.service.IPurchasedItemsService;
import org.jeecg.modules.admin.service.IPurchaseOrderManagementService;
import org.jeecg.modules.admin.service.IOrderManagementSystemService;
import org.jeecg.modules.admin.service.IProductionOrderService;
import org.jeecg.modules.admin.service.IMaterialBillService;
import org.jeecg.modules.admin.entity.MaterialBill;
import org.jeecg.modules.admin.entity.Material;
import org.jeecg.modules.admin.mapper.MaterialMapper;
import org.jeecg.modules.system.service.ISysDepartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @Description: 库存管理表
 * @Author: jeecg-boot
 * @Date:   2025-02-21
 * @Version: V1.0
 */
@Api(tags="库存管理表")
@RestController
@RequestMapping("/admin/inventoryManagement")
@Slf4j
public class InventoryManagementController extends JeecgController<InventoryManagement, IInventoryManagementService> {
	@Autowired
	private IInventoryManagementService inventoryManagementService;
	@Autowired
	private ISysDepartService sysDepartService;
	@Autowired
	private IPurchasedItemsService purchasedItemsService;
	@Autowired
	private IPurchaseOrderManagementService purchaseOrderManagementService;
	@Autowired
	private IOrderManagementSystemService orderManagementSystemService;
	@Autowired
	private IProductionOrderService productionOrderService;
	@Autowired
	private IMaterialBillService materialBillService;
	@Autowired
	private MaterialMapper materialMapper;

	 /**
	  * 在生产订单保存时需根据物料名称查询库存是否充足
	  * 在物料清单保存时需根据物料名称查询在库存是不是存在
	  */
	 @ApiOperation(value="库存管理表-根据物品名称查询库存表", notes="库存管理表-根据物品名称查询库存表")
	 @GetMapping(value = "/findInventoryByWpName")
	 public Result<InventoryManagement> findInventoryByWpName(@RequestParam(name="wpName",required=true)String wpName){
	 	QueryWrapper<InventoryManagement> queryWrapper=new QueryWrapper<InventoryManagement>();
	 	if (null!=wpName && !wpName.isEmpty()){
	 		queryWrapper.eq("im_item_name",wpName);
		}
		 String currentEnterpriseId = sysDepartService.getCurrentEnterpriseId();
		 if (null!=currentEnterpriseId && !currentEnterpriseId.isEmpty()){
			 queryWrapper.eq("company_id",currentEnterpriseId);
		 }
		 InventoryManagement inventoryManagement = inventoryManagementService.getOne(queryWrapper);
		 return Result.OK(inventoryManagement);
	 }

	 /**
	  * 退货时根据采购订单id查询库存物品
	  */
	 @ApiOperation(value="库存管理表-退货时根据采购订单id查询库存物品", notes="库存管理表-退货时根据采购订单id查询库存物品")
	 @GetMapping(value = "/findInventoryByWpId")
	 public Result<List<InventoryManagement>> findInventoryByWpId(@RequestParam(name="wpId",required=true)String wpId){
		 QueryWrapper<InventoryManagement> queryWrapper=new QueryWrapper<InventoryManagement>();
		 if (null!=wpId && !wpId.isEmpty()){
			 queryWrapper.eq("purchase_order_id",wpId);
		 }
		 String currentEnterpriseId = sysDepartService.getCurrentEnterpriseId();
		 if (null!=currentEnterpriseId && !currentEnterpriseId.isEmpty()){
			 queryWrapper.eq("company_id",currentEnterpriseId);
		 }
		 List<InventoryManagement> list = inventoryManagementService.list(queryWrapper);
		 return Result.OK(list);
	 }




	 /**
	 * 分页列表查询
	 *
	 * @param inventoryManagement
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "库存管理表-分页列表查询")
	@ApiOperation(value="库存管理表-分页列表查询", notes="库存管理表-分页列表查询")
	@GetMapping(value = "/list")
	@PermissionData(pageComponent = "admin/purchaseSalesInventory/inventoryManagement/InventoryManagementList")
	public Result<IPage<InventoryManagement>> queryPageList(InventoryManagement inventoryManagement,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<InventoryManagement> queryWrapper = QueryGenerator.initQueryWrapper(inventoryManagement, req.getParameterMap());
		Page<InventoryManagement> page = new Page<InventoryManagement>(pageNo, pageSize);
		String currentEnterpriseId = sysDepartService.getCurrentEnterpriseId();
		queryWrapper.eq("company_id",currentEnterpriseId);
//		if (null!=inventoryManagement.getImInventoryType() && !inventoryManagement.getImInventoryType().equals("")){
//			queryWrapper.eq("im_inventory_type",inventoryManagement.getImInventoryType());
//		}
		String imItemName = inventoryManagement.getImItemName() != null ? inventoryManagement.getImItemName().trim() : null;
		String imProductName = inventoryManagement.getImProductName() != null ? inventoryManagement.getImProductName().trim() : null;

		if (imItemName != null && !imItemName.isEmpty() && imProductName != null && !imProductName.isEmpty()) {
			queryWrapper.like("im_item_name", imItemName)
					.or()
					.like("im_product_name", imProductName);
		} else if (imItemName != null && !imItemName.isEmpty()) {
			queryWrapper.like("im_item_name", imItemName);
		} else if (imProductName != null && !imProductName.isEmpty()) {
			queryWrapper.like("im_product_name", imProductName);
		}
		IPage<InventoryManagement> pageList = inventoryManagementService.page(page, queryWrapper);
		for (InventoryManagement record : pageList.getRecords()) {
			if (record.getImInventoryType().equals("1")){//物料
				// 计算物料平均单价和总成本
				calculateMaterialAveragePrice(record);
			}else if (record.getImInventoryType().equals("2")){//产品
				// 计算产品平均成本和总成本
				calculateProductAverageCost(record);
			}
		}
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param inventoryManagement
	 * @return
	 */
	@AutoLog(value = "库存管理表-添加")
	@ApiOperation(value="库存管理表-添加", notes="库存管理表-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InventoryManagement inventoryManagement) {
		inventoryManagement.setCompanyId(sysDepartService.getCurrentEnterpriseId());
		inventoryManagementService.save(inventoryManagement);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param inventoryManagement
	 * @return
	 */
	@AutoLog(value = "库存管理表-编辑")
	@ApiOperation(value="库存管理表-编辑", notes="库存管理表-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InventoryManagement inventoryManagement) {
		inventoryManagementService.updateById(inventoryManagement);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "库存管理表-通过id删除")
	@ApiOperation(value="库存管理表-通过id删除", notes="库存管理表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inventoryManagementService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "库存管理表-批量删除")
	@ApiOperation(value="库存管理表-批量删除", notes="库存管理表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inventoryManagementService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 模糊搜索物料库存
	 *
	 * @param keyword 搜索关键词
	 * @return 匹配的物料库存列表
	 */
	@ApiOperation(value="库存管理表-模糊搜索物料", notes="库存管理表-模糊搜索物料")
	@GetMapping(value = "/searchMaterials")
	public Result<List<Map<String, Object>>> searchMaterials(@RequestParam(name="keyword", required=false) String keyword) {
		try {
			QueryWrapper<InventoryManagement> queryWrapper = new QueryWrapper<>();

			// 获取当前企业ID
			String currentEnterpriseId = sysDepartService.getCurrentEnterpriseId();
			if (currentEnterpriseId != null && !currentEnterpriseId.isEmpty()) {
				queryWrapper.eq("company_id", currentEnterpriseId);
			}

			// 添加物料类型条件（1表示物料库存）
			queryWrapper.eq("im_inventory_type", "1");

			// 添加关键词搜索条件
			if (keyword != null && !keyword.trim().isEmpty()) {
				queryWrapper.and(wrapper ->
					wrapper.like("im_item_name", keyword)
						.or()
						.like("im_item_unit", keyword)  // 使用正确的字段名
				);
			}

			// 限制返回数量
			queryWrapper.last("LIMIT 20");

			List<InventoryManagement> list = inventoryManagementService.list(queryWrapper);

			// 转换为包含采购信息的Map列表
			List<Map<String, Object>> resultList = new ArrayList<>();
			for (InventoryManagement inventory : list) {
				Map<String, Object> item = new HashMap<>();
				item.put("id", inventory.getId());
				item.put("imItemName", inventory.getImItemName());
				item.put("imUnit", inventory.getImItemUnit());
				item.put("imInventoryQuantity", inventory.getImInventoryQuantity());
				item.put("imItemPrice", inventory.getImItemPrice());

				// 根据物料名称查询采购信息
				String materialName = inventory.getImItemName();
				if (materialName != null && !materialName.trim().isEmpty()) {
					// 查询最近的采购记录
					QueryWrapper<PurchasedItems> purchaseQuery = new QueryWrapper<>();
					purchaseQuery.eq("item", materialName.trim());
					purchaseQuery.eq("company_id", currentEnterpriseId);
					purchaseQuery.eq("yon_return", "0"); // 未退货
					purchaseQuery.orderByDesc("create_time");
					purchaseQuery.last("LIMIT 1");

					PurchasedItems latestPurchase = purchasedItemsService.getOne(purchaseQuery);
					if (latestPurchase != null) {
						// 生成采购批次号
						String purchaseBatch = "BATCH-" + (latestPurchase.getPurchaseOrderId() != null ?
							latestPurchase.getPurchaseOrderId().substring(0, Math.min(8, latestPurchase.getPurchaseOrderId().length())) :
							"DEFAULT");
						item.put("purchaseBatch", purchaseBatch);

						// 格式化采购时间
						String purchaseTime = "未知时间";
						if (latestPurchase.getCreateTime() != null) {
							try {
								java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
								purchaseTime = sdf.format(latestPurchase.getCreateTime());
							} catch (Exception e) {
								purchaseTime = latestPurchase.getCreateTime().toString();
							}
						}
						item.put("purchaseTime", purchaseTime);
						item.put("purchaseUnitCost", latestPurchase.getPurchaseOrderPrice() != null ?
							latestPurchase.getPurchaseOrderPrice() : BigDecimal.ZERO);
					} else {
						// 默认值
						item.put("purchaseBatch", "BATCH-DEFAULT");
						item.put("purchaseTime", "未知时间");
						item.put("purchaseUnitCost", BigDecimal.ZERO);
					}
				} else {
					// 默认值
					item.put("purchaseBatch", "BATCH-DEFAULT");
					item.put("purchaseTime", "未知时间");
					item.put("purchaseUnitCost", BigDecimal.ZERO);
				}

				resultList.add(item);
			}

			return Result.OK(resultList);
		} catch (Exception e) {
			log.error("搜索物料失败", e);
			return Result.error("搜索物料失败: " + e.getMessage());
		}
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "库存管理表-通过id查询")
	@ApiOperation(value="库存管理表-通过id查询", notes="库存管理表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InventoryManagement> queryById(@RequestParam(name="id",required=true) String id) {
		InventoryManagement inventoryManagement = inventoryManagementService.getById(id);
		if(inventoryManagement==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inventoryManagement);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inventoryManagement
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InventoryManagement inventoryManagement) {
        return super.exportXls(request, inventoryManagement, InventoryManagement.class, "库存管理表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InventoryManagement.class);
    }

    /**
     * 获取物料采购详情
     *
     * @param inventoryId 库存ID
     * @return 采购详情列表
     */
    @ApiOperation(value="库存管理表-获取物料采购详情", notes="库存管理表-获取物料采购详情")
    @GetMapping(value = "/getPurchaseDetails")
    public Result<List<Map<String, Object>>> getPurchaseDetails(@RequestParam(name="inventoryId",required=true) String inventoryId) {
        try {
            // 根据库存ID获取采购详情
            InventoryManagement inventory = inventoryManagementService.getById(inventoryId);
            if (inventory == null) {
                return Result.error("库存记录不存在");
            }

            String itemName = inventory.getImItemName();
            String companyId = inventory.getCompanyId();

            if (itemName == null || itemName.trim().isEmpty()) {
                return Result.error("物料名称为空");
            }

            List<Map<String, Object>> purchaseDetails = new ArrayList<>();

            // 根据物料名称查询所有相关的采购物品记录
            QueryWrapper<PurchasedItems> itemsQueryWrapper = new QueryWrapper<>();
            itemsQueryWrapper.eq("item", itemName.trim());
            itemsQueryWrapper.eq("company_id", companyId);
            itemsQueryWrapper.eq("yon_return", "0"); // 只查询未退货的记录
            itemsQueryWrapper.orderByDesc("create_time");

            List<PurchasedItems> purchasedItemsList = purchasedItemsService.list(itemsQueryWrapper);

            if (purchasedItemsList == null || purchasedItemsList.isEmpty()) {
                log.info("未找到物料 {} 的采购记录", itemName);
                return Result.OK(purchaseDetails);
            }

            // 遍历采购物品记录，获取对应的采购订单信息
            for (PurchasedItems item : purchasedItemsList) {
                String purchaseOrderId = item.getPurchaseOrderId();
                if (purchaseOrderId != null) {
                    // 查询采购订单信息
                    PurchaseOrderManagement purchaseOrder = purchaseOrderManagementService.getById(purchaseOrderId);
                    if (purchaseOrder != null) {
                        Map<String, Object> detail = new HashMap<>();
                        detail.put("purchaseOrderNo", purchaseOrder.getPurchaseOrderNumber() != null ?
                                purchaseOrder.getPurchaseOrderNumber() : "PO-" + purchaseOrderId.substring(0, Math.min(8, purchaseOrderId.length())));
                        detail.put("purchaser", purchaseOrder.getPurchaser() != null ? purchaseOrder.getPurchaser() : "未知采购员");
                        detail.put("purchaseCost", item.getPurchaseOrderPrice());
                        detail.put("quantity", item.getPurchaseItemsCount());
                        detail.put("remainingQuantity", item.getPurchaseItemsCount()); // 这里可以根据实际业务逻辑计算剩余数量
                        detail.put("purchaseDate", purchaseOrder.getPurchaseOrderDate() != null ?
                                purchaseOrder.getPurchaseOrderDate().toString() : "未知日期");
                        detail.put("unit", item.getPurchaseItemsUnit());
                        detail.put("totalAmount", item.getPurchaseOrderPrice() != null && item.getPurchaseItemsCount() != null ?
                                item.getPurchaseOrderPrice().multiply(new BigDecimal(item.getPurchaseItemsCount())).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);

                        purchaseDetails.add(detail);
                    }
                }
            }

            return Result.OK(purchaseDetails);
        } catch (Exception e) {
            log.error("获取采购详情失败", e);
            return Result.error("获取采购详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品生产详情
     *
     * @param inventoryId 库存ID
     * @return 生产详情列表
     */
    @ApiOperation(value="库存管理表-获取产品生产详情", notes="库存管理表-获取产品生产详情")
    @GetMapping(value = "/getProductionDetails")
    public Result<List<Map<String, Object>>> getProductionDetails(@RequestParam(name="inventoryId",required=true) String inventoryId) {
        try {
            // 根据库存ID获取生产详情
            InventoryManagement inventory = inventoryManagementService.getById(inventoryId);
            if (inventory == null) {
                return Result.error("库存记录不存在");
            }

            String productName = inventory.getImProductName();
            String companyId = inventory.getCompanyId();

            if (productName == null || productName.trim().isEmpty()) {
                return Result.error("产品名称为空");
            }

            List<Map<String, Object>> productionDetails = new ArrayList<>();

            // 根据产品名称查询所有相关的生产订单记录
            QueryWrapper<ProductionOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("product_name", productName.trim());
            queryWrapper.eq("company_id", companyId);
            queryWrapper.eq("production_order_status", "1"); // 只查询审批完成的生产订单
            queryWrapper.orderByDesc("create_time");

            List<ProductionOrder> productionOrderList = productionOrderService.list(queryWrapper);

            if (productionOrderList == null || productionOrderList.isEmpty()) {
                log.info("未找到产品 {} 的生产订单记录", productName);
                return Result.OK(productionDetails);
            }

            // 遍历生产订单记录
            for (ProductionOrder order : productionOrderList) {
                Map<String, Object> detail = new HashMap<>();
                detail.put("id", order.getId());
                detail.put("productionBatch", "BATCH-" + order.getId().substring(0, Math.min(8, order.getId().length())));
//                detail.put("productionOrderNo", order.getProductionOrderName() != null ?
//                        order.getProductionOrderName() : "PRO-" + order.getId().substring(0, Math.min(8, order.getId().length())));

				detail.put("productionOrderNo", order.getId());
                detail.put("quantity", order.getProductCount());
                detail.put("batchCost", order.getProductUnitPrice());
                detail.put("productionDate", order.getCreateTime() != null ? order.getCreateTime() : "未知日期");
                detail.put("prioritySale", false); // 默认不优先出售，可以根据实际业务逻辑调整
                detail.put("unit", order.getProductUnit());
                detail.put("totalAmount", order.getProductUnitPrice() != null && order.getProductCount() != null ?
                        order.getProductUnitPrice().multiply(new BigDecimal(order.getProductCount())).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);

                productionDetails.add(detail);
            }

            return Result.OK(productionDetails);
        } catch (Exception e) {
            log.error("获取生产详情失败", e);
            return Result.error("获取生产详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取已售出库存信息
     *
     * @param inventoryId 库存ID
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 已售出库存信息
     */
    @ApiOperation(value="库存管理表-获取已售出库存信息", notes="库存管理表-获取已售出库存信息")
    @GetMapping(value = "/getSoldInventory")
    public Result<IPage<Map<String, Object>>> getSoldInventory(
            @RequestParam(name="inventoryId",required=true) String inventoryId,
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
        try {
            // 根据库存ID获取已售出库存信息
            InventoryManagement inventory = inventoryManagementService.getById(inventoryId);
            if (inventory == null) {
                return Result.error("库存记录不存在");
            }

            String productName = inventory.getImProductName();
            String companyId = inventory.getCompanyId();

            if (productName == null || productName.trim().isEmpty()) {
                return Result.error("产品名称为空");
            }

            Page<OrderManagementSystem> page = new Page<>(pageNo, pageSize);

            // 根据产品名称和公司ID查询已完成的销售订单
            QueryWrapper<OrderManagementSystem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("product_name", productName.trim());
            queryWrapper.eq("company_id", companyId);
            queryWrapper.eq("audit_start", 0); // 只查询审批完成的订单
            queryWrapper.gt("amount_paid", 0); // 已付款的订单
            queryWrapper.orderByDesc("create_time");

            IPage<OrderManagementSystem> orderPage = orderManagementSystemService.page(page, queryWrapper);

            List<Map<String, Object>> soldOrders = new ArrayList<>();

            for (OrderManagementSystem order : orderPage.getRecords()) {
                Map<String, Object> soldOrder = new HashMap<>();
                soldOrder.put("contractOrderNo", order.getId()); // 使用订单ID作为合同订单号
                soldOrder.put("customerName", order.getCustomerName());
                soldOrder.put("soldQuantity", order.getNumber() != null ? order.getNumber().toString() : "0");

                // 计算单价
                BigDecimal unitPrice = BigDecimal.ZERO;
                if (order.getTotalAmount() != null && order.getNumber() != null && order.getNumber() > 0) {
                    unitPrice = order.getTotalAmount().divide(new BigDecimal(order.getNumber()), 2, RoundingMode.HALF_UP);
                }
                soldOrder.put("unitPrice", unitPrice);
                soldOrder.put("soldAmount", order.getTotalAmount());
                // 格式化销售日期为 YYYY-MM-dd HH:mm:ss 格式
                String soldDate = "未知日期";
                if (order.getCreateTime() != null) {
                    try {
                        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        soldDate = sdf.format(order.getCreateTime());
                    } catch (Exception e) {
                        log.warn("格式化销售日期失败", e);
                        soldDate = order.getCreateTime().toString();
                    }
                }
                soldOrder.put("soldDate", soldDate);

                // 根据审批状态和付款状态确定订单状态
                String orderStatus = "processing";
                if (order.getAuditStart() != null) {
                    switch (order.getAuditStart()) {
                        case 0:
                            orderStatus = "completed";
                            break;
                        case 1:
                            orderStatus = "cancelled";
                            break;
                        case 2:
                            orderStatus = "processing";
                            break;
                        case 3:
                            orderStatus = "pending";
                            break;
                        default:
                            orderStatus = "processing";
                    }
                }
                soldOrder.put("orderStatus", orderStatus);

                soldOrders.add(soldOrder);
            }

            // 构建返回的分页对象
            Page<Map<String, Object>> resultPage = new Page<>(pageNo, pageSize);
            resultPage.setRecords(soldOrders);
            resultPage.setTotal(orderPage.getTotal());
            resultPage.setPages(orderPage.getPages());
            resultPage.setCurrent(orderPage.getCurrent());
            resultPage.setSize(orderPage.getSize());

            return Result.OK(resultPage);
        } catch (Exception e) {
            log.error("获取已售出库存信息失败", e);
            return Result.error("获取已售出库存信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新批次优先出售设置
     *
     * @param batchId 批次ID
     * @param prioritySale 是否优先出售
     * @return 操作结果
     */
    @ApiOperation(value="库存管理表-更新批次优先出售设置", notes="库存管理表-更新批次优先出售设置")
    @PostMapping(value = "/updatePrioritySale")
    public Result<String> updatePrioritySale(
            @RequestParam(name="batchId",required=true) String batchId,
            @RequestParam(name="prioritySale",required=true) Boolean prioritySale) {
        try {
            // 实际应该更新生产批次表中的优先出售字段
            // 这里只是模拟操作
            log.info("更新批次 {} 的优先出售设置为: {}", batchId, prioritySale);
            return Result.OK("设置成功");
        } catch (Exception e) {
            log.error("更新批次优先出售设置失败", e);
            return Result.error("设置失败");
        }
    }

    /**
     * 计算物料平均单价
     * 根据采购订单计算平均单价（例如采购单一：书本*2 单价10元，采购单二：书本*3 单价8元，平均单价就为（20+24）/5 = 8.8）
     *
     * @param record 库存记录
     */
    private void calculateMaterialAveragePrice(InventoryManagement record) {
        try {
            String itemName = record.getImItemName();
            String companyId = record.getCompanyId();

            if (itemName == null || itemName.trim().isEmpty()) {
                log.warn("物料名称为空，无法计算平均单价");
                return;
            }

            // 根据物料名称和公司ID查询所有相关的采购物品记录
            QueryWrapper<PurchasedItems> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("item", itemName.trim());
            queryWrapper.eq("company_id", companyId);
            queryWrapper.eq("yon_return", "0"); // 只查询未退货的记录

            List<PurchasedItems> purchasedItemsList = purchasedItemsService.list(queryWrapper);

            if (purchasedItemsList == null || purchasedItemsList.isEmpty()) {
                log.info("未找到物料 {} 的采购记录，使用原有单价", itemName);
                // 使用原有逻辑计算总成本
                calculateTotalCost(record, record.getImItemPrice());
                return;
            }

            // 计算加权平均价格
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal totalQuantity = BigDecimal.ZERO;

            for (PurchasedItems item : purchasedItemsList) {
                if (item.getPurchaseOrderPrice() != null && item.getPurchaseItemsCount() != null) {
                    try {
                        BigDecimal price = item.getPurchaseOrderPrice();
                        BigDecimal quantity = new BigDecimal(item.getPurchaseItemsCount());

                        totalAmount = totalAmount.add(price.multiply(quantity));
                        totalQuantity = totalQuantity.add(quantity);
                    } catch (NumberFormatException e) {
                        log.warn("采购物品数量格式错误: {}", item.getPurchaseItemsCount());
                    }
                }
            }

            if (totalQuantity.compareTo(BigDecimal.ZERO) > 0) {
                // 计算平均单价
                BigDecimal averagePrice = totalAmount.divide(totalQuantity, 2, RoundingMode.HALF_UP);
                record.setImItemPrice(averagePrice);
                log.info("物料 {} 计算平均单价成功: {} (基于 {} 条采购记录)", itemName, averagePrice, purchasedItemsList.size());

                // 计算总成本
                calculateTotalCost(record, averagePrice);
            } else {
                log.warn("物料 {} 的采购记录中没有有效的数量数据", itemName);
                calculateTotalCost(record, record.getImItemPrice());
            }

        } catch (Exception e) {
            log.error("计算物料平均单价失败", e);
            // 如果计算失败，使用原有逻辑
            calculateTotalCost(record, record.getImItemPrice());
        }
    }

    /**
     * 计算产品平均成本
     * 根据生产订单计算平均成本（例如生产单一：书本*2 成本10元，生产单二：书本*3 成本8元，平均成本就为（20+24）/5 = 8.8）
     *
     * @param record 库存记录
     */
    private void calculateProductAverageCost(InventoryManagement record) {
        try {
            String productName = record.getImProductName();
            String companyId = record.getCompanyId();

            if (productName == null || productName.trim().isEmpty()) {
                log.warn("产品名称为空，无法计算平均成本");
                return;
            }

            // 根据产品名称和公司ID查询所有相关的生产订单记录
            QueryWrapper<ProductionOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("product_name", productName.trim());
            queryWrapper.eq("company_id", companyId);
            queryWrapper.eq("production_order_status", "1"); // 只查询审批完成的生产订单

            List<ProductionOrder> productionOrderList = productionOrderService.list(queryWrapper);

            if (productionOrderList == null || productionOrderList.isEmpty()) {
                log.info("未找到产品 {} 的生产订单记录，使用原有成本", productName);
                // 使用原有逻辑计算总成本
                calculateTotalCost(record, record.getImCost());
                return;
            }

            // 计算加权平均成本
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal totalQuantity = BigDecimal.ZERO;

            for (ProductionOrder order : productionOrderList) {
                if (order.getProductUnitPrice() != null && order.getProductCount() != null) {
                    try {
                        BigDecimal unitPrice = order.getProductUnitPrice();
                        BigDecimal quantity = new BigDecimal(order.getProductCount());

                        totalAmount = totalAmount.add(unitPrice.multiply(quantity));
                        totalQuantity = totalQuantity.add(quantity);
                    } catch (NumberFormatException e) {
                        log.warn("生产订单产品数量格式错误: {}", order.getProductCount());
                    }
                }
            }

            if (totalQuantity.compareTo(BigDecimal.ZERO) > 0) {
                // 计算平均成本
                BigDecimal averageCost = totalAmount.divide(totalQuantity, 2, RoundingMode.HALF_UP);
                record.setImCost(averageCost);
                log.info("产品 {} 计算平均成本成功: {} (基于 {} 条生产订单记录)", productName, averageCost, productionOrderList.size());

                // 计算总成本
                calculateTotalCost(record, averageCost);
            } else {
                log.warn("产品 {} 的生产订单记录中没有有效的数量数据", productName);
                calculateTotalCost(record, record.getImCost());
            }

        } catch (Exception e) {
            log.error("计算产品平均成本失败", e);
            // 如果计算失败，使用原有逻辑
            calculateTotalCost(record, record.getImCost());
        }
    }

    /**
     * 计算总成本的通用方法
     *
     * @param record 库存记录
     * @param unitPrice 单价
     */
    private void calculateTotalCost(InventoryManagement record, BigDecimal unitPrice) {
        try {
            String imInventoryQuantity = record.getImInventoryQuantity();
            BigDecimal quantity = imInventoryQuantity != null && !imInventoryQuantity.trim().isEmpty()
                    ? new BigDecimal(imInventoryQuantity.trim())
                    : BigDecimal.ZERO;

            BigDecimal price = unitPrice != null ? unitPrice : BigDecimal.ZERO;
            BigDecimal totalCost = price.multiply(quantity).setScale(2, RoundingMode.HALF_UP);
            record.setImTotalCost(totalCost);
        } catch (Exception e) {
            log.error("计算总成本失败", e);
            record.setImTotalCost(BigDecimal.ZERO);
        }
    }

	/**
	 * 获取物料采购批次信息
	 *
	 * @param materialName 物料名称
	 * @return 采购批次信息列表
	 */
	@ApiOperation(value="库存管理表-获取物料采购批次信息", notes="库存管理表-获取物料采购批次信息")
	@GetMapping(value = "/getMaterialPurchaseBatch")
	public Result<List<Map<String, Object>>> getMaterialPurchaseBatch(@RequestParam(name="materialName",required=true) String materialName) {
		try {
			// 获取当前企业ID
			String currentEnterpriseId = sysDepartService.getCurrentEnterpriseId();

			List<Map<String, Object>> batchList = new ArrayList<>();

			// 根据物料名称查询所有采购记录
			QueryWrapper<PurchasedItems> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("item", materialName.trim());
			queryWrapper.eq("company_id", currentEnterpriseId);
			queryWrapper.eq("yon_return", "0"); // 未退货
			queryWrapper.orderByDesc("create_time");

			List<PurchasedItems> purchasedItemsList = purchasedItemsService.list(queryWrapper);

			if (purchasedItemsList == null || purchasedItemsList.isEmpty()) {
				log.info("未找到物料 {} 的采购记录", materialName);
				return Result.OK(batchList);
			}

			// 遍历采购记录，构建批次信息
			for (PurchasedItems item : purchasedItemsList) {
				Map<String, Object> batch = new HashMap<>();

				// 生成采购批次号
				String purchaseBatch = "BATCH-" + (item.getPurchaseOrderId() != null ?
					item.getPurchaseOrderId().substring(0, Math.min(8, item.getPurchaseOrderId().length())) :
					"DEFAULT");
				batch.put("purchaseBatch", purchaseBatch);

				// 采购单号
				batch.put("purchaseOrderNo", item.getPurchaseOrderId() != null ?
					"PO-" + item.getPurchaseOrderId().substring(0, Math.min(8, item.getPurchaseOrderId().length())) :
					"PO-DEFAULT");

				// 格式化采购时间
				String purchaseTime = "未知时间";
				if (item.getCreateTime() != null) {
					try {
						java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						purchaseTime = sdf.format(item.getCreateTime());
					} catch (Exception e) {
						purchaseTime = item.getCreateTime().toString();
					}
				}
				batch.put("purchaseTime", purchaseTime);

				// 采购数量
				batch.put("quantity", item.getPurchaseItemsCount() != null ? item.getPurchaseItemsCount() : "0");

				// 采购单价
				batch.put("price", item.getPurchaseOrderPrice() != null ? item.getPurchaseOrderPrice() : BigDecimal.ZERO);

				// 采购总额
				BigDecimal totalAmount = BigDecimal.ZERO;
				if (item.getPurchaseOrderPrice() != null && item.getPurchaseItemsCount() != null) {
					try {
						BigDecimal quantity = new BigDecimal(item.getPurchaseItemsCount());
						totalAmount = item.getPurchaseOrderPrice().multiply(quantity);
					} catch (NumberFormatException e) {
						log.warn("采购数量格式错误: {}", item.getPurchaseItemsCount());
					}
				}
				batch.put("totalAmount", totalAmount);

				// 采购人（从采购订单中获取）
				batch.put("purchaser", item.getCreateBy() != null ? item.getCreateBy() : "未知");

				batchList.add(batch);
			}

			return Result.OK(batchList);
		} catch (Exception e) {
			log.error("获取物料采购批次信息失败", e);
			return Result.error("获取物料采购批次信息失败: " + e.getMessage());
		}
	}

	/**
	 * 获取产品成本（根据物料清单计算）
	 *
	 * @param productName 产品名称
	 * @return 产品成本信息
	 */
	@ApiOperation(value="库存管理表-获取产品成本", notes="库存管理表-获取产品成本")
	@GetMapping(value = "/getProductCost")
	public Result<Map<String, Object>> getProductCost(@RequestParam(name="productName",required=true) String productName) {
		try {
			// 获取当前企业ID
			String currentEnterpriseId = sysDepartService.getCurrentEnterpriseId();

			Map<String, Object> costInfo = new HashMap<>();
			costInfo.put("productName", productName);
			costInfo.put("totalCost", BigDecimal.ZERO);
			costInfo.put("materialDetails", new ArrayList<>());

			// 根据产品名称查询物料清单
			QueryWrapper<MaterialBill> materialBillQueryWrapper = new QueryWrapper<>();
			materialBillQueryWrapper.eq("product_name", productName.trim());
			materialBillQueryWrapper.eq("company_id", currentEnterpriseId);
			materialBillQueryWrapper.eq("material_approval_status", "0"); // 审批通过
			materialBillQueryWrapper.eq("material_type", "0"); // 启用状态

			List<MaterialBill> materialBills = materialBillService.list(materialBillQueryWrapper);

			if (materialBills == null || materialBills.isEmpty()) {
				log.info("未找到产品 {} 的物料清单", productName);
				return Result.OK(costInfo);
			}

			// 取第一个启用的物料清单
			MaterialBill materialBill = materialBills.get(0);

			// 查询物料清单关联的物料列表
			QueryWrapper<Material> materialQueryWrapper = new QueryWrapper<>();
			materialQueryWrapper.eq("material_bill_id", materialBill.getId());
			List<Material> materialList = materialMapper.selectList(materialQueryWrapper);

			if (materialList == null || materialList.isEmpty()) {
				log.info("物料清单 {} 没有关联的物料", materialBill.getId());
				return Result.OK(costInfo);
			}

			BigDecimal totalCost = BigDecimal.ZERO;
			List<Map<String, Object>> materialDetails = new ArrayList<>();

			// 遍历物料列表，计算成本
			for (Material material : materialList) {
				Map<String, Object> materialDetail = new HashMap<>();
				materialDetail.put("materialName", material.getMaterialName());
				materialDetail.put("materialCount", material.getMaterialCount());
				materialDetail.put("materialUnit", material.getMaterialUnit());
				materialDetail.put("unitPrice", material.getImItemPrice());
				materialDetail.put("materialCost", material.getMaterialCost());

				if (material.getMaterialCost() != null) {
					totalCost = totalCost.add(material.getMaterialCost());
				}

				materialDetails.add(materialDetail);
			}

			costInfo.put("totalCost", totalCost.setScale(2, RoundingMode.HALF_UP));
			costInfo.put("materialDetails", materialDetails);
			costInfo.put("materialBillId", materialBill.getId());
			costInfo.put("materialBillName", materialBill.getMaterialBillName());

			return Result.OK(costInfo);
		} catch (Exception e) {
			log.error("获取产品成本失败", e);
			return Result.error("获取产品成本失败: " + e.getMessage());
		}
	}

}
