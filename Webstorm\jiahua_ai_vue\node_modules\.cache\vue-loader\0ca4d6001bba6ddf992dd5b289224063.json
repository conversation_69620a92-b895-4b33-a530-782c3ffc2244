{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\modules\\InventoryDetailModal.vue", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\inventoryManagement\\modules\\InventoryDetailModal.vue", "mtime": 1753844660496}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\eslint-loader\\index.js", "mtime": 1753423166782}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./InventoryDetailModal.vue?vue&type=template&id=38b42cac&scoped=true&\"\nimport script from \"./InventoryDetailModal.vue?vue&type=script&lang=js&\"\nexport * from \"./InventoryDetailModal.vue?vue&type=script&lang=js&\"\nimport style0 from \"./InventoryDetailModal.vue?vue&type=style&index=0&id=38b42cac&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"38b42cac\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\developing\\\\java\\\\Code\\\\Webstorm\\\\jiahua_ai_vue\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('38b42cac')) {\n      api.createRecord('38b42cac', component.options)\n    } else {\n      api.reload('38b42cac', component.options)\n    }\n    module.hot.accept(\"./InventoryDetailModal.vue?vue&type=template&id=38b42cac&scoped=true&\", function () {\n      api.rerender('38b42cac', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/admin/purchaseSalesInventory/inventoryManagement/modules/InventoryDetailModal.vue\"\nexport default component.exports"]}