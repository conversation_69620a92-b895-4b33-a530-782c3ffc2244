<template>
  <div class="inventory-management-container">
    <!-- 顶部统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <div class="stat-card total-items">
            <div class="stat-icon">
              <a-icon type="database" />
            </div>
            <div class="stat-info">
              <div class="stat-title">总物品数</div>
              <div class="stat-value">{{ totalItems }}</div>
              <div class="stat-trend">
                <a-icon type="arrow-up" />
                <span>{{ trendData.items }}%</span>
              </div>
            </div>
          </div>
        </a-col>
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <div class="stat-card total-inventory">
            <div class="stat-icon">
              <a-icon type="inbox" />
            </div>
            <div class="stat-info">
              <div class="stat-title">库存总量</div>
              <div class="stat-value">{{ totalInventory }}</div>
              <div class="stat-trend">
                <a-icon type="arrow-up" />
                <span>{{ trendData.inventory }}%</span>
              </div>
            </div>
          </div>
        </a-col>
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <div class="stat-card available-inventory">
            <div class="stat-icon">
              <a-icon type="check-circle" />
            </div>
            <div class="stat-info">
              <div class="stat-title">可用库存</div>
              <div class="stat-value">{{ availableInventory }}</div>
              <div class="stat-trend">
                <a-icon type="arrow-down" />
                <span>{{ trendData.available }}%</span>
              </div>
            </div>
          </div>
        </a-col>
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <div class="stat-card total-cost">
            <div class="stat-icon">
              <a-icon type="dollar" />
            </div>
            <div class="stat-info">
              <div class="stat-title">总成本</div>
              <div class="stat-value">¥{{ formatNumber(totalCost) }}</div>
              <div class="stat-trend">
                <a-icon type="arrow-up" />
                <span>{{ trendData.cost }}%</span>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索与过滤栏 -->
    <div class="search-filter-bar">
      <div class="search-area">
        <a-input-search
          placeholder="搜索产品名称或物品名称"
          @search="onSearch"
          allow-clear
          class="search-input"
        >
          <a-icon slot="prefix" type="search" />
        </a-input-search>
        <a-button-group class="type-filter">
<!--          <a-tooltip title="全部">-->
<!--            <a-button-->
<!--              :type="inventoryTypeFilter === 'all' ? 'primary' : 'default'"-->
<!--              @click="() => { inventoryTypeFilter = 'all'; loadData(1); }"-->
<!--            >-->
<!--              全部-->
<!--            </a-button>-->
<!--          </a-tooltip>-->
          <a-tooltip title="物品">
            <a-button
              :type="inventoryTypeFilter === 'material' ? 'primary' : 'default'"
              @click="() => { inventoryTypeFilter = 'material'; loadData(1); }"
            >
              物料
            </a-button>
          </a-tooltip>
          <a-tooltip title="产品">
            <a-button
              :type="inventoryTypeFilter === 'product' ? 'primary' : 'default'"
              @click="() => { inventoryTypeFilter = 'product'; loadData(1); }"
            >
              产品
            </a-button>
          </a-tooltip>
        </a-button-group>
      </div>

      <div class="filter-actions">
        <a-button-group class="view-toggle">
          <a-tooltip title="数据表格视图">
            <a-button :type="viewMode === 'table' ? 'primary' : 'default'" @click="viewMode = 'table'">
              <a-icon type="table" />
            </a-button>
          </a-tooltip>
          <a-tooltip title="卡片视图">
            <a-button :type="viewMode === 'card' ? 'primary' : 'default'" @click="viewMode = 'card'">
              <a-icon type="appstore" />
            </a-button>
          </a-tooltip>
        </a-button-group>
      </div>
    </div>

    <!-- 选择信息提示 -->
    <div v-if="selectedRowKeys.length > 0" class="selection-info">
      <a-alert
        :message="`已选择 ${selectedRowKeys.length} 项`"
        type="info"
        show-icon
        closable
        @close="selectedRowKeys = []"
      >
        <template slot="action">
          <a-button size="small" type="primary" @click="handleBatchAdjust" style="margin-right: 8px;">
            <a-icon type="tool" /> 批量调整
          </a-button>
          <a-button size="small" type="danger" @click="batchDelete">
            批量删除
          </a-button>
        </template>
      </a-alert>
    </div>

    <!-- 卡片视图 -->
    <div v-if="viewMode === 'card'" class="inventory-card-view">
      <a-spin :spinning="loading">
        <a-empty v-if="dataSource.length === 0" description="暂无库存数据" />
        <a-row :gutter="[16, 16]" v-else>
          <a-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" v-for="item in dataSource" :key="item.id">
            <div
              class="inventory-card"
              :class="{ 'selected-card': selectedRowKeys.includes(item.id) }"
              @click="toggleSelection(item.id)"
            >
              <div class="card-header">
                <div class="item-name" :title="item.imItemName">{{ item.imItemName }}</div>
                <a-checkbox
                  :checked="selectedRowKeys.includes(item.id)"
                  @change="(e) => onItemSelect(item.id, e.target.checked)"
                  @click.stop
                />
              </div>

              <div class="card-product">
                <span>产品: {{ item.imProductName }}</span>
                <span class="item-id">ID: {{ item.imItemId }}</span>
              </div>

              <div class="inventory-metrics">
                <div class="metric-item">
                  <div class="metric-value">{{ item.imInventoryQuantity }}</div>
                  <div class="metric-label">库存数量</div>
                  <a-progress
                    :percent="100"
                    strokeColor="#52c41a"
                    size="small"
                    :showInfo="false"
                  />
                </div>

                <div class="metric-item">
                  <div class="metric-value">{{ item.imAvailableStock }}</div>
                  <div class="metric-label">可用库存</div>
                  <a-progress
                    :percent="calculateAvailablePercent(item.imInventoryQuantity, item.imAvailableStock)"
                    :strokeColor="getStockColor(item.imInventoryQuantity, item.imAvailableStock)"
                    size="small"
                    :showInfo="false"
                  />
                </div>
              </div>

              <div class="price-info">
                <div class="price-item">
                  <span class="price-label">单价:</span>
                  <span class="price-value">¥{{ formatNumber(item.imItemPrice) }}</span>
                  <span class="unit-label">/ {{ item.imItemUnit }}</span>
                </div>
                <div class="price-item">
                  <span class="price-label">总成本:</span>
                  <span class="price-value">¥{{ formatNumber(item.imTotalCost) }}</span>
                </div>
              </div>

              <div class="card-actions">
                <a-button type="link" @click.stop="handleDetail(item)">
                  <a-icon type="eye" /> 详情
                </a-button>
                <!-- <a-button type="link" class="edit-btn" @click.stop="handleEdit(item)">
                  <a-icon type="edit" /> 编辑
                </a-button>
                <a-button type="link" class="delete-btn" @click.stop="handleDelete(item.id)">
                  <a-icon type="delete" /> 删除
                </a-button> -->
              </div>
            </div>
          </a-col>
        </a-row>
        <!-- 卡片分页 -->
        <div class="card-pagination">
          <a-pagination
            :current="ipagination.current"
            :pageSize="ipagination.pageSize"
            :total="ipagination.total"
            :pageSizeOptions="ipagination.pageSizeOptions"
            :showTotal="(total, range) => `${range[0]}-${range[1]} 共 ${total} 条`"
            showSizeChanger
            showQuickJumper
            @change="handleCardPageChange"
            @showSizeChange="handleCardShowSizeChange"
          />
        </div>
      </a-spin>
    </div>

    <!-- 表格视图 -->
    <div v-else-if="viewMode === 'table'" class="inventory-table-view">
      <a-spin :spinning="loading">
        <a-table
          ref="table"
          size="middle"
          :scroll="{x:true}"
          :rowKey="record => record.id"
          :columns="getColumns()"
          :dataSource="dataSource"
          :pagination="ipagination"
          :rowSelection="rowSelection"
          class="custom-inventory-table"
          @change="handleTableChange"
        >
          <template slot="imInventoryQuantitySlot" slot-scope="text, record">
            <div class="inventory-cell clickable-cell" @click="handleInventoryClick(record)">
              <span class="inventory-number">{{ text }}</span>
              <a-progress
                :percent="calculateStockPercent(record.imInventoryQuantity, record.imAvailableStock)"
                size="small"
                strokeColor="#52c41a"
                :showInfo="false"
              />
            </div>
          </template>

          <template slot="imAvailableStockSlot" slot-scope="text, record">
            <div class="inventory-cell clickable-cell" @click="handleAvailableStockClick(record)">
              <span class="inventory-number">{{ text }}</span>
              <a-progress
                :percent="calculateAvailablePercent(record.imInventoryQuantity, record.imAvailableStock)"
                :strokeColor="getStockColor(record.imInventoryQuantity, record.imAvailableStock)"
                size="small"
                :showInfo="false"
              />
            </div>
          </template>

          <template slot="priceSlot" slot-scope="text">
            <span class="price-text">¥{{ formatNumber(text) }}</span>
          </template>

          <template slot="action" slot-scope="text, record">
            <div class="table-actions">
              <a-button type="link" @click="handleDetail(record)">
                <a-icon type="eye" /> 详情
              </a-button>
              <a-button v-if="record.imInventoryType === '2'" type="link" @click="handleSoldInventory(record)">
                <a-icon type="shopping-cart" /> 已售出库存
              </a-button>
              <!-- <a-button type="link" class="edit-btn" @click="handleEdit(record)">
                <a-icon type="edit" /> 编辑
              </a-button>
              <a-button type="link" class="delete-btn" @click="handleDelete(record.id)">
                <a-icon type="delete" /> 删除
              </a-button> -->
            </div>
          </template>
        </a-table>
      </a-spin>
    </div>

    <inventory-management-modal ref="modalForm" @ok="modalFormOk"></inventory-management-modal>
    <inventory-management-detail ref="detailDrawer" @close="closeDetail" @edit="handleEdit"></inventory-management-detail>
    <batch-stock-adjust-modal ref="batchAdjustModal" :selected-items="getSelectedItems()" @success="handleBatchAdjustSuccess"></batch-stock-adjust-modal>
    <inventory-detail-modal ref="inventoryDetailModal"></inventory-detail-modal>
    <sold-inventory-modal ref="soldInventoryModal"></sold-inventory-modal>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import InventoryManagementModal from './modules/InventoryManagementModal'
import InventoryManagementDetail from './modules/InventoryManagementDetail'
import BatchStockAdjustModal from './modules/BatchStockAdjustModal'
import InventoryDetailModal from './modules/InventoryDetailModal'
import SoldInventoryModal from './modules/SoldInventoryModal'

export default {
  name: 'InventoryManagementList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    InventoryManagementModal,
    InventoryManagementDetail,
    BatchStockAdjustModal,
    InventoryDetailModal,
    SoldInventoryModal
  },
  data() {
    return {
      allDataForStats: [],
      inventoryTypeFilter: 'material',
      trendData: {
        items: 5.2,
        inventory: 3.7,
        available: -1.2,
        cost: 2.4
      },
      description: '库存管理表管理页面',
      viewMode: 'table',
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: "center",
          customRender: function(t, r, index) {
            return parseInt(index) + 1;
          }
        },
        {
          title: '产品id',
          align: "center",
          dataIndex: 'imProductId'
        },
        {
          title: '产品名',
          align: "center",
          dataIndex: 'imProductName'
        },
        {
          title: '物品id',
          align: "center",
          dataIndex: 'imItemId'
        },
        {
          title: '物品名',
          align: "center",
          dataIndex: 'imItemName'
        },
        {
          title: '物品单位',
          align: "center",
          dataIndex: 'imItemUnit'
        },
        {
          title: '物品单价',
          align: "center",
          dataIndex: 'imItemPrice'
        },
        {
          title: '成本',
          align: "center",
          dataIndex: 'imCost'
        },
        {
          title: '库存数量',
          align: "center",
          dataIndex: 'imInventoryQuantity'
        },
        {
          title: '可用库存',
          align: "center",
          dataIndex: 'imAvailableStock'
        },
        {
          title: '总成本',
          align: "center",
          dataIndex: 'imTotalCost'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          fixed: "right",
          width: 147,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: "/admin/inventoryManagement/list",
        delete: "/admin/inventoryManagement/delete",
        deleteBatch: "/admin/inventoryManagement/deleteBatch",
        exportXlsUrl: "/admin/inventoryManagement/exportXls",
        importExcelUrl: "admin/inventoryManagement/importExcel",
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList();
    this.enhanceColumns();
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    },
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
        onSelect: this.onSelect,
        onSelectAll: this.onSelectAll,
      }
    },

    totalItems() {
      return this.dataSource.length || 0;
    },
    totalInventory() {
      return this.dataSource.reduce((sum, item) => {
        return sum + (parseInt(item.imInventoryQuantity) || 0);
      }, 0);
    },
    availableInventory() {
      return this.dataSource.reduce((sum, item) => {
        return sum + (parseInt(item.imAvailableStock) || 0);
      }, 0);
    },
    totalCost() {
      return this.dataSource.reduce((sum, item) => {
        return sum + (parseFloat(item.imTotalCost) || 0);
      }, 0);
    }
  },
  methods: {

    getColumns() {
      if(this.inventoryTypeFilter === 'material'){
        return this.enhancedColumns();
      }else{
        return this.productColumns();
      }
    },
    enhancedColumns() {
      return [
        {
          title: '物品信息',
          align: "center",
          children: [
            // {
            //   title: '产品名称',
            //   align: "left",
            //   dataIndex: 'imProductName',
            //   width: 150,
            // },
            {
              title: '物品名称',
              align: "left",
              dataIndex: 'imItemName',
              width: 150,
            },
            // {
            //   title: '物品ID',
            //   align: "center",
            //   dataIndex: 'imItemId',
            //   width: 100,
            // }
          ]
        },
        {
          title: '库存信息',
          align: "center",
          children: [
            {
              title: '库存数量',
              align: "center",
              dataIndex: 'imInventoryQuantity',
              width: 150,
              scopedSlots: { customRender: 'imInventoryQuantitySlot' }
            },
            {
              title: '可用库存',
              align: "center",
              dataIndex: 'imAvailableStock',
              width: 150,
              scopedSlots: { customRender: 'imAvailableStockSlot' }
            },
            {
              title: '单位',
              align: "center",
              dataIndex: 'imItemUnit',
              width: 80,
            }
          ]
        },
        {
          title: '价格/成本',
          align: "center",
          children: [
            {
              title: '平均单价',
              align: "right",
              dataIndex: 'imItemPrice',
              width: 120,
              scopedSlots: { customRender: 'priceSlot' }
            },
            {
              title: '总成本',
              align: "right",
              dataIndex: 'imTotalCost',
              width: 120,
              scopedSlots: { customRender: 'priceSlot' }
            }
          ]
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          fixed: "right",
          width: 180,
          scopedSlots: { customRender: 'action' }
        }
      ];
    },
    productColumns() {
      return [
        {
          title: '产品信息',
          align: "center",
          children: [
            {
              title: '产品名称',
              align: "left",
              dataIndex: 'imProductName',
              width: 150,
            }
          ]
        },
        {
          title: '库存信息',
          align: "center",
          children: [
            {
              title: '库存数量',
              align: "center",
              dataIndex: 'imInventoryQuantity',
              width: 150,
              scopedSlots: { customRender: 'imInventoryQuantitySlot' }
            },
            {
              title: '可用库存',
              align: "center",
              dataIndex: 'imAvailableStock',
              width: 150,
              scopedSlots: { customRender: 'imAvailableStockSlot' }
            },
            {
              title: '单位',
              align: "center",
              dataIndex: 'imItemUnit',
              width: 80,
            }
          ]
        },
        {
          title: '价格/成本',
          align: "center",
          children: [
            {
              title: '平均单价成本',
              align: "right",
              dataIndex: 'imCost',
              width: 120,
              scopedSlots: { customRender: 'priceSlot' }
            },
            {
              title: '总成本',
              align: "right",
              dataIndex: 'imTotalCost',
              width: 120,
              scopedSlots: { customRender: 'priceSlot' }
            }
          ]
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          fixed: "right",
          width: 250,
          scopedSlots: { customRender: 'action' }
        }
      ];
    },
    handleCardPageChange(page, pageSize) {
      this.ipagination.current = page;
      this.loadData();
    },
    handleCardShowSizeChange(current, size) {
      this.ipagination.pageSize = size;
      this.ipagination.current = 1;
      this.loadData();
    },
    loadData(arg) {
      if (arg === 1) {
        this.ipagination.current = 1;
      }

      const params = this.getQueryParams();
      if (this.inventoryTypeFilter !== 'all') {
        params.imInventoryType = this.inventoryTypeFilter === 'material' ? '1' : '2';
      }
      this.loading = true;
      this.$http.get(this.url.list, { params }).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records || [];
          this.ipagination.total = res.result.total || 0;
        }
        if (res.code === 510) {
          this.$message.warning(res.message);
        }
        this.loading = false;
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = [];
      fieldList.push({
        type: 'string',
        value: 'imProductId',
        text: '产品id',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'imProductName',
        text: '产品名',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'imItemId',
        text: '物品id',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'imItemName',
        text: '物品名',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'imItemUnit',
        text: '物品单位',
        dictCode: ''
      })
      fieldList.push({
        type: 'BigDecimal',
        value: 'imItemPrice',
        text: '物品单价',
        dictCode: ''
      })
      fieldList.push({
        type: 'BigDecimal',
        value: 'imCost',
        text: '成本',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'imInventoryQuantity',
        text: '库存数量',
        dictCode: ''
      })
      fieldList.push({
        type: 'string',
        value: 'imAvailableStock',
        text: '可用库存',
        dictCode: ''
      })
      fieldList.push({
        type: 'BigDecimal',
        value: 'imTotalCost',
        text: '总成本',
        dictCode: ''
      })
      this.superFieldList = fieldList
    },
    enhanceColumns() {},
    toggleSelection(id) {
      if (this.selectedRowKeys.includes(id)) {
        this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== id);
      } else {
        this.selectedRowKeys.push(id);
      }
    },
    onItemSelect(id, checked) {
      if (checked && !this.selectedRowKeys.includes(id)) {
        this.selectedRowKeys.push(id);
      } else if (!checked && this.selectedRowKeys.includes(id)) {
        this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== id);
      }
    },
    calculateStockPercent(total, available) {
      return 100;
    },
    calculateAvailablePercent(total, available) {
      if (!total || total <= 0 || !available) return 0;
      return Math.min(100, Math.round((available / total) * 100));
    },
    getStockColor(total, available) {
      if (!total || !available) return '#f5222d';
      const percent = (available / total) * 100;
      if (percent < 20) return '#f5222d';
      if (percent < 50) return '#faad14';
      return '#52c41a';
    },
    formatNumber(num) {
      if (!num || isNaN(num)) return '0.00';
      return parseFloat(num).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    onSearch(value) {
      this.queryParam = {
        ...this.queryParam,
        imProductName: value,
        imItemName: value
      };
      this.loadData(1);
    },
    handleEdit(record) {
      this.$refs.modalForm.edit(record);
      this.$refs.modalForm.title = "编辑";
      this.$refs.modalForm.disableSubmit = false;
    },
    handleDetail(record) {
      this.$refs.detailDrawer.show(record);
    },
    
    closeDetail() {
      // 可以在这里处理详情关闭后的逻辑
    },
    batchDelete() {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！');
        return false;
      } else {
        let ids = "";
        for (let a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ",";
        }
        this.$confirm({
          title: "确认删除",
          content: "是否删除选中数据?",
          onOk: () => {
            this.loading = true;
            this.$http.delete(this.url.deleteBatch, { params: { ids: ids } }).then((res) => {
              if (res.success) {
                this.$message.success(res.message);
                this.loadData();
                this.onClearSelected();
              } else {
                this.$message.warning(res.message);
              }
              this.loading = false;
            });
          }
        });
      }
    },
    handleBatchAdjust() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请至少选择一项进行操作');
        return;
      }
      this.$refs.batchAdjustModal.show();
    },
    
    handleBatchAdjustSuccess() {
      this.loadData();
      this.selectedRowKeys = [];
    },
    
    getSelectedItems() {
      return this.dataSource.filter(item => this.selectedRowKeys.includes(item.id));
    },

    // 处理库存数量点击事件
    handleInventoryClick(record) {
      if (record.imInventoryType === '2') {
        // 产品类型：显示产品成本、生产批次等信息
        this.$refs.productInventoryDetailModal.show(record, 'inventory');
      } else {
        // 物料类型：显示采购详情
        this.$refs.inventoryDetailModal.show(record, 'inventory');
      }
    },

    // 处理可用库存点击事件
    handleAvailableStockClick(record) {
      if (record.imInventoryType === '2') {
        // 产品类型：显示产品成本、生产批次等信息
        this.$refs.productInventoryDetailModal.show(record, 'available');
      } else {
        // 物料类型：显示采购详情
        this.$refs.inventoryDetailModal.show(record, 'available');
      }
    },

    // 处理已售出库存点击事件
    handleSoldInventory(record) {
      this.$refs.soldInventoryModal.show(record);
    }
  }
}
</script>

<style scoped>
/* 容器样式 */
.inventory-management-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 统计卡片样式 */
.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  height: 100%;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
}

.stat-card.total-items::before {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

.stat-card.total-inventory::before {
  background: linear-gradient(135deg, #faad14 0%, #fa8c16 100%);
}

.stat-card.available-inventory::before {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}

.stat-card.total-cost::before {
  background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
}

.stat-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  margin-right: 20px;
  font-size: 28px;
  color: #ffffff;
}

.stat-card.total-items .stat-icon {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

.stat-card.total-inventory .stat-icon {
  background: linear-gradient(135deg, #faad14 0%, #fa8c16 100%);
}

.stat-card.available-inventory .stat-icon {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}

.stat-card.total-cost .stat-icon {
  background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
}

.stat-info {
  flex: 1;
}

.stat-title {
  color: #595959;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 400;
}

.stat-value {
  color: #262626;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1.2;
}

.stat-trend {
  font-size: 12px;
  color: #8c8c8c;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-trend .anticon-arrow-up {
  color: #52c41a;
}

.stat-trend .anticon-arrow-down {
  color: #ff4d4f;
}

/* 搜索过滤栏样式 */
.search-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
  background: #ffffff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.search-area {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-input {
  width: 320px;
  border-radius: 8px;
}

.type-filter {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.type-filter .ant-btn {
  border-radius: 0;
  border-right: 1px solid #d9d9d9;
}

.type-filter .ant-btn:first-child {
  border-radius: 8px 0 0 8px;
}

.type-filter .ant-btn:last-child {
  border-radius: 0 8px 8px 0;
  border-right: none;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.view-toggle {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.view-toggle .ant-btn {
  border-radius: 0;
  border-right: 1px solid #d9d9d9;
}

.view-toggle .ant-btn:first-child {
  border-radius: 8px 0 0 8px;
}

.view-toggle .ant-btn:last-child {
  border-radius: 0 8px 8px 0;
  border-right: none;
}

/* 选择信息样式 */
.selection-info {
  margin-bottom: 16px;
}

/* 卡片视图样式 */
.inventory-card-view {
  margin-bottom: 24px;
}

.inventory-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.inventory-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #e6f7ff;
}

.inventory-card.selected-card {
  border-color: #1890ff;
  background: #e6f7ff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.item-name {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  max-width: calc(100% - 40px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-product {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  color: #595959;
  font-size: 14px;
}

.item-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #8c8c8c;
  background: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.inventory-metrics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 16px;
}

.metric-item {
  flex: 1;
  text-align: center;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: #262626;
  margin-bottom: 4px;
  line-height: 1.2;
}

.metric-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
  font-weight: 500;
}

.price-info {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.price-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.price-item:last-child {
  margin-bottom: 0;
}

.price-label {
  color: #8c8c8c;
  margin-right: 8px;
  width: 60px;
  font-size: 14px;
}

.price-value {
  color: #262626;
  font-weight: 600;
  font-size: 16px;
}

.unit-label {
  color: #8c8c8c;
  margin-left: 4px;
  font-size: 12px;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.card-actions .ant-btn {
  padding: 4px 12px;
  height: auto;
  font-size: 12px;
}

.edit-btn {
  color: #1890ff;
}

.delete-btn {
  color: #ff4d4f;
}

/* 卡片分页样式 */
.card-pagination {
  margin-top: 32px;
  text-align: center;
  background: #ffffff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 表格视图样式 */
.inventory-table-view {
  margin-bottom: 24px;
}

.custom-inventory-table {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.inventory-cell {
  text-align: center;

  &.clickable-cell {
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 4px;

    &:hover {
      background-color: #e6f7ff;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }
  }
}

.inventory-number {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: #262626;
  font-size: 16px;
}

.price-text {
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #262626;
  font-size: 14px;
}

.table-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.table-actions .ant-btn {
  padding: 4px 8px;
  height: auto;
  font-size: 12px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .inventory-management-container {
    padding: 16px;
  }

  .search-filter-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-area {
    flex-direction: column;
    gap: 12px;
  }

  .search-input {
    width: 100%;
  }

  .filter-actions {
    justify-content: center;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    margin-right: 16px;
    font-size: 24px;
  }

  .stat-value {
    font-size: 24px;
  }

  .inventory-card {
    padding: 16px;
  }

  .inventory-card:hover {
    transform: none;
  }

  .inventory-metrics {
    flex-direction: column;
    gap: 16px;
  }

  .metric-item {
    text-align: left;
  }

  .card-actions {
    flex-wrap: wrap;
    gap: 4px;
  }

  .card-actions .ant-btn {
    flex: 1;
    min-width: 0;
  }
}

@media (max-width: 480px) {
  .inventory-management-container {
    padding: 8px;
  }

  .stat-card {
    padding: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 20px;
  }

  .inventory-card {
    padding: 12px;
  }

  .item-name {
    font-size: 16px;
  }

  .metric-value {
    font-size: 24px;
  }
}
</style>