<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="796d1390-7e22-4965-aed7-f6aad613e088" name="更改" comment="项目管理修改">
      <change beforePath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/controller/InventoryManagementController.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/controller/InventoryManagementController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/controller/MaterialBillController.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/controller/MaterialBillController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/controller/OrderManagementSystemController.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/controller/OrderManagementSystemController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/entity/InventoryManagement.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/entity/InventoryManagement.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/entity/Material.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/entity/Material.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/entity/MaterialBill.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/entity/MaterialBill.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/entity/OrderManagementSystem.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/entity/OrderManagementSystem.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/service/impl/ProductionOrderServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/service/impl/ProductionOrderServiceImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="1250a4a608c7030e214ca51a548b2d81d6a466b8" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\developing\java\maven\apache-maven-3.9.8-bin\apache-maven-3.9.8" />
        <option name="localRepository" value="D:\developing\java\maven\mavenRepository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\developing\java\maven\apache-maven-3.9.8-bin\apache-maven-3.9.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="explicitlyEnabledProfiles" value="dev" />
    <option name="explicitlyDisabledProfiles" value="prod" />
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2wqBllQfFWVbFNKk2rueeBtm0dv" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <OptionsSetting value="false" id="Update" />
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.jeecg-boot-parent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-parent [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-parent [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-parent [validate].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-system-start [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-system-start [install].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.JeecgSystemApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/BaiduNetdiskDownload/在线商城/在线商城/shoppingOnline&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.3816092&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;editor.preferences.fonts.default&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.ALiPayUtils.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.DateTime.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.JwtUtil.executor&quot;: &quot;Debug&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql_aurora&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="org.jeecg.modules.admin.controller" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\developing\java\Code\IDEA\jiahua_ai_java\jeecg-module-system\jeecg-system-biz\src\main\resources" />
      <recent name="D:\developing\java\Code\IDEA\jiahua_ai_java\jeecg-module-system\jeecg-system-biz\src\main\java\org\jeecg\modules\enterpriseWechat\util" />
      <recent name="D:\developing\java\Code\IDEA\jiahua_ai_java\jeecg-module-system\jeecg-system-biz\src\main\java\org\jeecg\modules\admin\util" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\developing\java\Code\IDEA\jiahua_ai_java\jeecg-module-system\jeecg-system-start\src\main\resources" />
    </key>
    <key name="ExtractSuperBase.RECENT_KEYS">
      <recent name="org.jeecg.modules.message.websocket" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="org.jeecg.modules.enterpriseWechat.util" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="Redis" />
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.JeecgSystemApplication">
    <configuration name="ALiPayUtils" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.jeecg.modules.admin.util.ALiPayUtils" />
      <module name="jeecg-system-biz" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.jeecg.modules.admin.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DateTime" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.jeecg.modules.admin.util.DateTime" />
      <module name="jeecg-system-biz" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.jeecg.modules.admin.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JwtUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.jeecg.common.system.util.JwtUtil" />
      <module name="jeecg-boot-base-core" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.jeecg.common.system.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="jiahua_ai_java" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="jiahua_ai_java" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="JeecgSystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="jeecg-system-start" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.jeecg.JeecgSystemApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.JwtUtil" />
        <item itemvalue="应用程序.DateTime" />
        <item itemvalue="应用程序.ALiPayUtils" />
        <item itemvalue="应用程序.JwtUtil" />
        <item itemvalue="应用程序.DateTime" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="实体" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="796d1390-7e22-4965-aed7-f6aad613e088" name="更改" comment="" />
      <created>1746759376762</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746759376762</updated>
      <workItem from="1746759377833" duration="6402000" />
      <workItem from="1746773728077" duration="11843000" />
      <workItem from="1747009448146" duration="27001000" />
      <workItem from="1747095608743" duration="37404000" />
      <workItem from="1747183321563" duration="15175000" />
      <workItem from="1747204880797" duration="18522000" />
      <workItem from="1747268044731" duration="31127000" />
      <workItem from="1747356826742" duration="29244000" />
      <workItem from="1747614083757" duration="7147000" />
      <workItem from="1747641886183" duration="8890000" />
      <workItem from="1747700838621" duration="20567000" />
      <workItem from="1747787765527" duration="33962000" />
      <workItem from="1747875656420" duration="26397000" />
      <workItem from="1747910999918" duration="1389000" />
      <workItem from="1747960530892" duration="27928000" />
      <workItem from="1748218802170" duration="30460000" />
      <workItem from="1748307109761" duration="27635000" />
      <workItem from="1748392845059" duration="19580000" />
      <workItem from="1748479384652" duration="28471000" />
      <workItem from="1748565947473" duration="26835000" />
      <workItem from="1748910335872" duration="29588000" />
      <workItem from="1748998027749" duration="32051000" />
      <workItem from="1749083615462" duration="24238000" />
      <workItem from="1749169571326" duration="16472000" />
      <workItem from="1749429292650" duration="29023000" />
      <workItem from="1749516567501" duration="19993000" />
      <workItem from="1749602307666" duration="27009000" />
      <workItem from="1749688609592" duration="26338000" />
      <workItem from="1749774451867" duration="23972000" />
      <workItem from="1750033914589" duration="11923000" />
      <workItem from="1750119985872" duration="23977000" />
      <workItem from="1750206689015" duration="28834000" />
      <workItem from="1750294370973" duration="25278000" />
      <workItem from="1750329571624" duration="1185000" />
      <workItem from="1750380270196" duration="12747000" />
      <workItem from="1750638456138" duration="24833000" />
      <workItem from="1750724781319" duration="31046000" />
      <workItem from="1750812130694" duration="22932000" />
      <workItem from="1750897919498" duration="17487000" />
      <workItem from="1750984774994" duration="25705000" />
      <workItem from="1751248045986" duration="25054000" />
      <workItem from="1751331336948" duration="24421000" />
      <workItem from="1751418208695" duration="26205000" />
      <workItem from="1751504399266" duration="29935000" />
      <workItem from="1751589386956" duration="22441000" />
      <workItem from="1751850570788" duration="1004000" />
      <workItem from="1751853379124" duration="19633000" />
      <workItem from="1751937096167" duration="26994000" />
      <workItem from="1752022146528" duration="23745000" />
      <workItem from="1752109543080" duration="24313000" />
      <workItem from="1752197149768" duration="4217000" />
      <workItem from="1752202088427" duration="19575000" />
      <workItem from="1752453578392" duration="25179000" />
      <workItem from="1752539504677" duration="20685000" />
      <workItem from="1752628106168" duration="17648000" />
      <workItem from="1752714891641" duration="11865000" />
      <workItem from="1752800089119" duration="27573000" />
      <workItem from="1753058507319" duration="23931000" />
      <workItem from="1753094160079" duration="162000" />
      <workItem from="1753146238498" duration="27623000" />
      <workItem from="1753232005696" duration="24826000" />
      <workItem from="1753319340016" duration="13895000" />
      <workItem from="1753409731891" duration="21451000" />
      <workItem from="1753514669983" duration="6471000" />
      <workItem from="1753664792409" duration="21468000" />
      <workItem from="1753751209765" duration="15356000" />
      <workItem from="1753837421819" duration="1133000" />
      <workItem from="1753838678437" duration="5420000" />
    </task>
    <task id="LOCAL-00191" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752571103879</created>
      <option name="number" value="00191" />
      <option name="presentableId" value="LOCAL-00191" />
      <option name="project" value="LOCAL" />
      <updated>1752571103879</updated>
    </task>
    <task id="LOCAL-00192" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752571977738</created>
      <option name="number" value="00192" />
      <option name="presentableId" value="LOCAL-00192" />
      <option name="project" value="LOCAL" />
      <updated>1752571977738</updated>
    </task>
    <task id="LOCAL-00193" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752572392628</created>
      <option name="number" value="00193" />
      <option name="presentableId" value="LOCAL-00193" />
      <option name="project" value="LOCAL" />
      <updated>1752572392628</updated>
    </task>
    <task id="LOCAL-00194" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752631768402</created>
      <option name="number" value="00194" />
      <option name="presentableId" value="LOCAL-00194" />
      <option name="project" value="LOCAL" />
      <updated>1752631768403</updated>
    </task>
    <task id="LOCAL-00195" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752632633782</created>
      <option name="number" value="00195" />
      <option name="presentableId" value="LOCAL-00195" />
      <option name="project" value="LOCAL" />
      <updated>1752632633782</updated>
    </task>
    <task id="LOCAL-00196" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752633184788</created>
      <option name="number" value="00196" />
      <option name="presentableId" value="LOCAL-00196" />
      <option name="project" value="LOCAL" />
      <updated>1752633184788</updated>
    </task>
    <task id="LOCAL-00197" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752633451929</created>
      <option name="number" value="00197" />
      <option name="presentableId" value="LOCAL-00197" />
      <option name="project" value="LOCAL" />
      <updated>1752633451929</updated>
    </task>
    <task id="LOCAL-00198" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752633745789</created>
      <option name="number" value="00198" />
      <option name="presentableId" value="LOCAL-00198" />
      <option name="project" value="LOCAL" />
      <updated>1752633745789</updated>
    </task>
    <task id="LOCAL-00199" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752634514554</created>
      <option name="number" value="00199" />
      <option name="presentableId" value="LOCAL-00199" />
      <option name="project" value="LOCAL" />
      <updated>1752634514554</updated>
    </task>
    <task id="LOCAL-00200" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752638475907</created>
      <option name="number" value="00200" />
      <option name="presentableId" value="LOCAL-00200" />
      <option name="project" value="LOCAL" />
      <updated>1752638475908</updated>
    </task>
    <task id="LOCAL-00201" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752638793919</created>
      <option name="number" value="00201" />
      <option name="presentableId" value="LOCAL-00201" />
      <option name="project" value="LOCAL" />
      <updated>1752638793919</updated>
    </task>
    <task id="LOCAL-00202" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752639381133</created>
      <option name="number" value="00202" />
      <option name="presentableId" value="LOCAL-00202" />
      <option name="project" value="LOCAL" />
      <updated>1752639381133</updated>
    </task>
    <task id="LOCAL-00203" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752651370925</created>
      <option name="number" value="00203" />
      <option name="presentableId" value="LOCAL-00203" />
      <option name="project" value="LOCAL" />
      <updated>1752651370926</updated>
    </task>
    <task id="LOCAL-00204" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752661165860</created>
      <option name="number" value="00204" />
      <option name="presentableId" value="LOCAL-00204" />
      <option name="project" value="LOCAL" />
      <updated>1752661165860</updated>
    </task>
    <task id="LOCAL-00205" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752664078565</created>
      <option name="number" value="00205" />
      <option name="presentableId" value="LOCAL-00205" />
      <option name="project" value="LOCAL" />
      <updated>1752664078565</updated>
    </task>
    <task id="LOCAL-00206" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752723190730</created>
      <option name="number" value="00206" />
      <option name="presentableId" value="LOCAL-00206" />
      <option name="project" value="LOCAL" />
      <updated>1752723190731</updated>
    </task>
    <task id="LOCAL-00207" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752724387888</created>
      <option name="number" value="00207" />
      <option name="presentableId" value="LOCAL-00207" />
      <option name="project" value="LOCAL" />
      <updated>1752724387888</updated>
    </task>
    <task id="LOCAL-00208" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752807370653</created>
      <option name="number" value="00208" />
      <option name="presentableId" value="LOCAL-00208" />
      <option name="project" value="LOCAL" />
      <updated>1752807370654</updated>
    </task>
    <task id="LOCAL-00209" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752807794535</created>
      <option name="number" value="00209" />
      <option name="presentableId" value="LOCAL-00209" />
      <option name="project" value="LOCAL" />
      <updated>1752807794535</updated>
    </task>
    <task id="LOCAL-00210" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752809183312</created>
      <option name="number" value="00210" />
      <option name="presentableId" value="LOCAL-00210" />
      <option name="project" value="LOCAL" />
      <updated>1752809183312</updated>
    </task>
    <task id="LOCAL-00211" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752811291018</created>
      <option name="number" value="00211" />
      <option name="presentableId" value="LOCAL-00211" />
      <option name="project" value="LOCAL" />
      <updated>1752811291018</updated>
    </task>
    <task id="LOCAL-00212" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752818340975</created>
      <option name="number" value="00212" />
      <option name="presentableId" value="LOCAL-00212" />
      <option name="project" value="LOCAL" />
      <updated>1752818340975</updated>
    </task>
    <task id="LOCAL-00213" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752818954966</created>
      <option name="number" value="00213" />
      <option name="presentableId" value="LOCAL-00213" />
      <option name="project" value="LOCAL" />
      <updated>1752818954967</updated>
    </task>
    <task id="LOCAL-00214" summary="会话存档-测试">
      <option name="closed" value="true" />
      <created>1752830486917</created>
      <option name="number" value="00214" />
      <option name="presentableId" value="LOCAL-00214" />
      <option name="project" value="LOCAL" />
      <updated>1752830486917</updated>
    </task>
    <task id="LOCAL-00215" summary="企客宝扩展">
      <option name="closed" value="true" />
      <created>1753063283760</created>
      <option name="number" value="00215" />
      <option name="presentableId" value="LOCAL-00215" />
      <option name="project" value="LOCAL" />
      <updated>1753063283761</updated>
    </task>
    <task id="LOCAL-00216" summary="合同订单-测试">
      <option name="closed" value="true" />
      <created>1753088179920</created>
      <option name="number" value="00216" />
      <option name="presentableId" value="LOCAL-00216" />
      <option name="project" value="LOCAL" />
      <updated>1753088179920</updated>
    </task>
    <task id="LOCAL-00217" summary="合同订单-测试">
      <option name="closed" value="true" />
      <created>1753091692025</created>
      <option name="number" value="00217" />
      <option name="presentableId" value="LOCAL-00217" />
      <option name="project" value="LOCAL" />
      <updated>1753091692025</updated>
    </task>
    <task id="LOCAL-00218" summary="合同订单-测试">
      <option name="closed" value="true" />
      <created>1753092703115</created>
      <option name="number" value="00218" />
      <option name="presentableId" value="LOCAL-00218" />
      <option name="project" value="LOCAL" />
      <updated>1753092703115</updated>
    </task>
    <task id="LOCAL-00219" summary="人事BUG修改">
      <option name="closed" value="true" />
      <created>1753155694211</created>
      <option name="number" value="00219" />
      <option name="presentableId" value="LOCAL-00219" />
      <option name="project" value="LOCAL" />
      <updated>1753155694211</updated>
    </task>
    <task id="LOCAL-00220" summary="部门角色授权BUG、部门授权BUG、新增部门角色/部门权限更改记录保存、数字人-测试">
      <option name="closed" value="true" />
      <created>1753178989082</created>
      <option name="number" value="00220" />
      <option name="presentableId" value="LOCAL-00220" />
      <option name="project" value="LOCAL" />
      <updated>1753178989082</updated>
    </task>
    <task id="LOCAL-00221" summary="数字人-测试">
      <option name="closed" value="true" />
      <created>1753179986666</created>
      <option name="number" value="00221" />
      <option name="presentableId" value="LOCAL-00221" />
      <option name="project" value="LOCAL" />
      <updated>1753179986666</updated>
    </task>
    <task id="LOCAL-00222" summary="数字人">
      <option name="closed" value="true" />
      <created>1753180067583</created>
      <option name="number" value="00222" />
      <option name="presentableId" value="LOCAL-00222" />
      <option name="project" value="LOCAL" />
      <updated>1753180067583</updated>
    </task>
    <task id="LOCAL-00223" summary="数字人">
      <option name="closed" value="true" />
      <created>1753181563372</created>
      <option name="number" value="00223" />
      <option name="presentableId" value="LOCAL-00223" />
      <option name="project" value="LOCAL" />
      <updated>1753181563372</updated>
    </task>
    <task id="LOCAL-00224" summary="数字人">
      <option name="closed" value="true" />
      <created>1753182049884</created>
      <option name="number" value="00224" />
      <option name="presentableId" value="LOCAL-00224" />
      <option name="project" value="LOCAL" />
      <updated>1753182049884</updated>
    </task>
    <task id="LOCAL-00225" summary="数字人">
      <option name="closed" value="true" />
      <created>1753182340601</created>
      <option name="number" value="00225" />
      <option name="presentableId" value="LOCAL-00225" />
      <option name="project" value="LOCAL" />
      <updated>1753182340601</updated>
    </task>
    <task id="LOCAL-00226" summary="数字人">
      <option name="closed" value="true" />
      <created>1753182539796</created>
      <option name="number" value="00226" />
      <option name="presentableId" value="LOCAL-00226" />
      <option name="project" value="LOCAL" />
      <updated>1753182539796</updated>
    </task>
    <task id="LOCAL-00227" summary="数字人">
      <option name="closed" value="true" />
      <created>1753182645752</created>
      <option name="number" value="00227" />
      <option name="presentableId" value="LOCAL-00227" />
      <option name="project" value="LOCAL" />
      <updated>1753182645752</updated>
    </task>
    <task id="LOCAL-00228" summary="部门权限、部门角色权限修改">
      <option name="closed" value="true" />
      <created>1753236158087</created>
      <option name="number" value="00228" />
      <option name="presentableId" value="LOCAL-00228" />
      <option name="project" value="LOCAL" />
      <updated>1753236158088</updated>
    </task>
    <task id="LOCAL-00229" summary="数字人-测试">
      <option name="closed" value="true" />
      <created>1753239441301</created>
      <option name="number" value="00229" />
      <option name="presentableId" value="LOCAL-00229" />
      <option name="project" value="LOCAL" />
      <updated>1753239441301</updated>
    </task>
    <task id="LOCAL-00230" summary="数字人-测试">
      <option name="closed" value="true" />
      <created>1753240397525</created>
      <option name="number" value="00230" />
      <option name="presentableId" value="LOCAL-00230" />
      <option name="project" value="LOCAL" />
      <updated>1753240397525</updated>
    </task>
    <task id="LOCAL-00231" summary="数字人-测试">
      <option name="closed" value="true" />
      <created>1753240510426</created>
      <option name="number" value="00231" />
      <option name="presentableId" value="LOCAL-00231" />
      <option name="project" value="LOCAL" />
      <updated>1753240510426</updated>
    </task>
    <task id="LOCAL-00232" summary="数字人">
      <option name="closed" value="true" />
      <created>1753329606546</created>
      <option name="number" value="00232" />
      <option name="presentableId" value="LOCAL-00232" />
      <option name="project" value="LOCAL" />
      <updated>1753329606547</updated>
    </task>
    <task id="LOCAL-00233" summary="数字人">
      <option name="closed" value="true" />
      <created>1753338335088</created>
      <option name="number" value="00233" />
      <option name="presentableId" value="LOCAL-00233" />
      <option name="project" value="LOCAL" />
      <updated>1753338335088</updated>
    </task>
    <task id="LOCAL-00234" summary="首页AI对话">
      <option name="closed" value="true" />
      <created>1753431500984</created>
      <option name="number" value="00234" />
      <option name="presentableId" value="LOCAL-00234" />
      <option name="project" value="LOCAL" />
      <updated>1753431500984</updated>
    </task>
    <task id="LOCAL-00235" summary="AI自然语言查询">
      <option name="closed" value="true" />
      <created>1753441859228</created>
      <option name="number" value="00235" />
      <option name="presentableId" value="LOCAL-00235" />
      <option name="project" value="LOCAL" />
      <updated>1753441859228</updated>
    </task>
    <task id="LOCAL-00236" summary="系统配置">
      <option name="closed" value="true" />
      <created>1753519523381</created>
      <option name="number" value="00236" />
      <option name="presentableId" value="LOCAL-00236" />
      <option name="project" value="LOCAL" />
      <updated>1753519523381</updated>
    </task>
    <task id="LOCAL-00237" summary="客户管理修改">
      <option name="closed" value="true" />
      <created>1753693438546</created>
      <option name="number" value="00237" />
      <option name="presentableId" value="LOCAL-00237" />
      <option name="project" value="LOCAL" />
      <updated>1753693438547</updated>
    </task>
    <task id="LOCAL-00238" summary="库存管理-测试修改">
      <option name="closed" value="true" />
      <created>1753699137653</created>
      <option name="number" value="00238" />
      <option name="presentableId" value="LOCAL-00238" />
      <option name="project" value="LOCAL" />
      <updated>1753699137653</updated>
    </task>
    <task id="LOCAL-00239" summary="项目管理修改">
      <option name="closed" value="true" />
      <created>1753775025131</created>
      <option name="number" value="00239" />
      <option name="presentableId" value="LOCAL-00239" />
      <option name="project" value="LOCAL" />
      <updated>1753775025134</updated>
    </task>
    <option name="localTasksCounter" value="240" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:javax.persistence:javax.persistence-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.cloud:spring-cloud-context" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:javax.validation:validation-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
    <option featureType="dependencySupport" implementationName="java:io.reactivex.rxjava2:rxjava" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="java:jakarta.ws.rs:jakarta.ws.rs-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="4132ea7b-9724-4395-9291-71fd641d0567" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="4132ea7b-9724-4395-9291-71fd641d0567">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="dev" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="AI查询" />
    <MESSAGE value="1" />
    <MESSAGE value="合同订单AI查询、人事员工助手" />
    <MESSAGE value="合同订单AI查询" />
    <MESSAGE value="工单修改" />
    <MESSAGE value="审批、会话存档-测试" />
    <MESSAGE value="扫码上传文件、工资对接打卡" />
    <MESSAGE value="扫码上传文件" />
    <MESSAGE value="生成工资-适配所有工资情况" />
    <MESSAGE value="会话存档-测试、审批Bug修改" />
    <MESSAGE value="合同订单BUG修改" />
    <MESSAGE value="会话存档-测试" />
    <MESSAGE value="企客宝扩展" />
    <MESSAGE value="合同订单-测试" />
    <MESSAGE value="人事BUG修改" />
    <MESSAGE value="部门角色授权BUG、部门授权BUG、新增部门角色/部门权限更改记录保存、数字人-测试" />
    <MESSAGE value="部门权限、部门角色权限修改" />
    <MESSAGE value="数字人-测试" />
    <MESSAGE value="数字人" />
    <MESSAGE value="首页AI对话" />
    <MESSAGE value="AI自然语言查询" />
    <MESSAGE value="系统配置" />
    <MESSAGE value="客户管理修改" />
    <MESSAGE value="库存管理-测试修改" />
    <MESSAGE value="项目管理修改" />
    <option name="LAST_COMMIT_MESSAGE" value="项目管理修改" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/service/impl/ZpTalentServiceImpl.java</url>
          <line>357</line>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/controller/ZpTalentController.java</url>
          <line>429</line>
          <option name="timeStamp" value="29" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/GPT/util/AiChatUtil.java</url>
          <line>406</line>
          <option name="timeStamp" value="61" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/system/controller/FileUploadController.java</url>
          <line>109</line>
          <option name="timeStamp" value="90" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/service/impl/OrderManagementSystemServiceImpl.java</url>
          <line>140</line>
          <option name="timeStamp" value="103" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/admin/controller/OrderManagementSystemController.java</url>
          <line>632</line>
          <option name="timeStamp" value="110" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="java.util.HashMap$Node" memberName="value" />
        <PinnedItemInfo parentTag="org.jeecg.modules.admin.approval.entity.AiApproval" memberName="id" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="transferCustomerForWechat" />
        <watch expression="getUserIdByServiceThirdCode" />
        <watch expression="&#9;&#9;&#9;&#9;log.info(&quot;不在监听人的监听范围，不发送消息：fieldId: {}, senderId: {}, scope: {}&quot;, fieldListener.getFieldId(), fieldListener.getSenderId(), fieldListener.getListenerScope());&#10;" />
        <watch expression="            if(b) {&#10;" />
        <watch expression="            LoginUser user =(LoginUser) SecurityUtils.getSubject().getPrincipal();&#10;" />
        <watch expression="            int audioDuration = getAudioDurationFromUrl(audioUrl);&#10;" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>