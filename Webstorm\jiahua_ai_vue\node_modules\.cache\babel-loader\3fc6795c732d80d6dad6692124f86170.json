{"remainingRequest": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\babel-loader\\lib\\index.js!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\material\\modules\\MaterialBillForm.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\src\\views\\admin\\purchaseSalesInventory\\material\\modules\\MaterialBillForm.vue", "mtime": 1753844909036}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753423167852}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753423168548}, {"path": "D:\\developing\\java\\Code\\Webstorm\\jiahua_ai_vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753423169810}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/developing/java/Code/Webstorm/jiahua_ai_vue/node_modules/@babel/runtime/regenerator\";\nvar _name$components$prop;\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { httpAction, getAction } from '@/api/manage';\nimport { validateDuplicateValue } from '@/utils/util';\nimport moment from 'moment';\nexport default (_name$components$prop = {\n  name: 'MaterialBillForm',\n  components: {},\n  props: {\n    //表单禁用\n    disabled: {\n      type: Boolean,\n      default: false,\n      required: false\n    }\n  },\n  data: function data() {\n    return {\n      products: [],\n      filteredProducts: [],\n      // 添加过滤后的产品列表\n      loadingProducts: false,\n      visibleCheck: true,\n      materialList: [],\n      searchMaterialName: '',\n      inventoryModalVisible: false,\n      inventorySearchName: '',\n      inventoryList: [],\n      loadingInventory: false,\n      tempIdCounter: 0,\n      model: {\n        productId: '',\n        productName: '',\n        materialBillName: '',\n        materialType: 0,\n        materialJson: '',\n        materialApprovalStatus: '',\n        stockEmptyStrategy: '',\n        // 存储逗号分隔的策略值\n        imItemPrice: 0\n      },\n      stockEmptyStrategyArray: [],\n      // 用于多选框的数组\n      materialColumns: [{\n        title: '物料名称',\n        dataIndex: 'materialName',\n        key: 'materialName',\n        scopedSlots: {\n          customRender: 'materialName'\n        },\n        width: 220,\n        ellipsis: true\n      }, {\n        title: '单位',\n        dataIndex: 'materialUnit',\n        key: 'materialUnit',\n        scopedSlots: {\n          customRender: 'materialUnit'\n        },\n        width: 120\n      }, {\n        title: '数量',\n        key: 'materialCount',\n        width: 150,\n        scopedSlots: {\n          customRender: 'materialCount'\n        }\n      }, {\n        title: '操作',\n        key: 'action',\n        width: 80,\n        fixed: 'right',\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }],\n      inventoryColumns: [{\n        title: '物料名称',\n        dataIndex: 'imItemName',\n        key: 'imItemName',\n        ellipsis: true,\n        scopedSlots: {\n          customRender: 'imItemName'\n        }\n      }, {\n        title: '单位',\n        dataIndex: 'imUnit',\n        key: 'imUnit',\n        scopedSlots: {\n          customRender: 'imUnit'\n        }\n      }, {\n        title: '库存数量',\n        dataIndex: 'imInventoryQuantity',\n        key: 'imInventoryQuantity',\n        scopedSlots: {\n          customRender: 'imQuantity'\n        }\n      }, {\n        title: '采购批次',\n        dataIndex: 'purchaseBatch',\n        key: 'purchaseBatch',\n        scopedSlots: {\n          customRender: 'purchaseBatch'\n        }\n      }, {\n        title: '采购时间',\n        dataIndex: 'purchaseTime',\n        key: 'purchaseTime',\n        scopedSlots: {\n          customRender: 'purchaseTime'\n        }\n      }, {\n        title: '采购单位成本',\n        dataIndex: 'purchaseUnitCost',\n        key: 'purchaseUnitCost',\n        scopedSlots: {\n          customRender: 'purchaseUnitCost'\n        }\n      }, {\n        title: '操作',\n        key: 'action',\n        width: 100,\n        fixed: 'right',\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }],\n      labelCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 5\n        }\n      },\n      wrapperCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 16\n        }\n      },\n      confirmLoading: false,\n      validatorRules: {\n        productId: [{\n          required: true,\n          message: '请选择产品'\n        }],\n        materialBillName: [{\n          required: true,\n          message: '请输入物料清单名称'\n        }, {\n          max: 50,\n          message: '物料清单名称不能超过50个字符'\n        }]\n      },\n      url: {\n        add: \"/admin/materialBill/add\",\n        edit: \"/admin/materialBill/edit\",\n        queryById: \"/admin/materialBill/queryById\",\n        inventoryList: \"/admin/inventoryManagement/list\",\n        searchMaterials: \"/admin/inventoryManagement/searchMaterials\",\n        queryProductById: \"/sys/sysDepart/queryProductById\"\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.loadProducts();\n    // 处理路由参数\n    var routeProductId = this.$route.query.productId;\n    if (routeProductId) {\n      this.model.productId = routeProductId;\n      this.handleProductChange(routeProductId);\n    }\n  },\n  watch: {\n    materialList: {\n      handler: function handler(newVal) {\n        // 当物料列表变化时，更新materialJson\n        this.updateMaterialJson();\n      },\n      deep: true\n    }\n  },\n  computed: {\n    formDisabled: function formDisabled() {\n      return this.disabled;\n    }\n  }\n}, _defineProperty(_name$components$prop, \"watch\", {\n  // 监听多选框数组变化，同步到字符串字段\n  stockEmptyStrategyArray: {\n    handler: function handler(newVal) {\n      this.model.stockEmptyStrategy = newVal.join(',');\n    },\n    deep: true\n  },\n  // 监听字符串字段变化，同步到多选框数组\n  'model.stockEmptyStrategy': {\n    handler: function handler(newVal) {\n      if (newVal && typeof newVal === 'string') {\n        this.stockEmptyStrategyArray = newVal.split(',').filter(function (item) {\n          return item.trim();\n        });\n      } else {\n        this.stockEmptyStrategyArray = [];\n      }\n    },\n    immediate: true\n  }\n}), _defineProperty(_name$components$prop, \"created\", function created() {\n  //备份model原始值\n  this.modelDefault = JSON.parse(JSON.stringify(this.model));\n  // 设置默认值为启用\n  if (this.model.materialType === undefined) {\n    this.model.materialType = 0;\n  }\n}), _defineProperty(_name$components$prop, \"methods\", {\n  // 重置表单\n  resetForm: function resetForm() {\n    this.model = JSON.parse(JSON.stringify(this.modelDefault));\n    this.materialList = [];\n    this.visibleCheck = true;\n    if (this.$refs.form) {\n      this.$refs.form.resetFields();\n    }\n  },\n  // 更新物料列表\n  updateMaterialList: function updateMaterialList() {\n    var _this = this;\n    try {\n      this.model.materialList = this.materialList.map(function (item, index) {\n        return {\n          id: item.id || item.tempId,\n          materialName: item.materialName || '',\n          materialUnit: item.materialUnit || '',\n          materialCount: String(item.materialCount || 1),\n          // 改为 materialCount\n          imItemPrice: item.imItemPrice || 0,\n          companyId: item.companyId || '',\n          createBy: item.createBy || '',\n          createTime: item.createTime || '',\n          materialBillId: _this.model.id || '',\n          productionOrderId: item.productionOrderId || '',\n          sysOrgCode: item.sysOrgCode || '',\n          updateBy: item.updateBy || '',\n          updateTime: item.updateTime || ''\n        };\n      });\n      console.log('更新后的 materialList:', this.model.materialList);\n    } catch (error) {\n      console.error('更新 materialList 失败:', error);\n    }\n  },\n  onChose: function onChose(checked) {\n    if (checked) {\n      this.model.materialType = 0; // 0表示启用\n      this.visibleCheck = true;\n    } else {\n      this.model.materialType = 1; // 1表示禁用\n      this.visibleCheck = false;\n    }\n  },\n  loadProducts: function () {\n    var _loadProducts = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      var res;\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              this.loadingProducts = true;\n              _context.next = 4;\n              return getAction('/sys/sysDepart/queryProductsByCurrentEnterprise', {});\n            case 4:\n              res = _context.sent;\n              if (!res.success) {\n                _context.next = 11;\n                break;\n              }\n              this.products = (res.result || []).filter(function (p) {\n                return p.id && p.productName;\n              }).map(function (p) {\n                return {\n                  id: p.id,\n                  name: p.productName\n                };\n              });\n\n              // 初始设置过滤后的产品列表与产品列表相同\n              this.filteredProducts = _toConsumableArray(this.products);\n              return _context.abrupt(\"return\", this.products);\n            case 11:\n              this.$message.error('产品加载失败：' + (res.message || '请求异常'));\n              this.products = [];\n              this.filteredProducts = [];\n              return _context.abrupt(\"return\", []);\n            case 15:\n              _context.next = 24;\n              break;\n            case 17:\n              _context.prev = 17;\n              _context.t0 = _context[\"catch\"](0);\n              console.error('产品加载失败:', _context.t0);\n              this.$message.error('产品加载失败：' + (_context.t0.message || '未知错误'));\n              this.products = [];\n              this.filteredProducts = [];\n              return _context.abrupt(\"return\", []);\n            case 24:\n              _context.prev = 24;\n              this.loadingProducts = false;\n              return _context.finish(24);\n            case 27:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this, [[0, 17, 24, 27]]);\n    }));\n    function loadProducts() {\n      return _loadProducts.apply(this, arguments);\n    }\n    return loadProducts;\n  }(),\n  add: function add() {\n    this.edit(this.modelDefault);\n  },\n  // 产品选择变更\n  handleProductChange: function handleProductChange(productId) {\n    var _this2 = this;\n    if (!productId) {\n      this.model.productId = '';\n      this.model.productName = '';\n      return;\n    }\n    this.model.productId = productId;\n\n    // 查找选中的产品名称并设置\n    var selectedProduct = this.products.find(function (p) {\n      return p.id === productId;\n    });\n    if (selectedProduct) {\n      this.model.productName = selectedProduct.name;\n      console.log('产品已选择:', selectedProduct.name);\n    } else {\n      // 如果在本地列表中找不到，尝试从服务器获取\n      getAction(this.url.queryProductById, {\n        id: productId\n      }).then(function (res) {\n        if (res.success && res.result) {\n          _this2.model.productName = res.result.productName || res.result.name;\n          console.log('从服务器获取产品名称:', _this2.model.productName);\n        }\n      }).catch(function (err) {\n        console.error('获取产品详情失败:', err);\n      });\n    }\n  },\n  // 处理产品搜索\n  handleProductSearch: function handleProductSearch(searchText) {\n    this.filteredProducts = this.products.filter(function (product) {\n      return product.name.toLowerCase().includes(searchText.toLowerCase());\n    });\n  },\n  // 处理产品选择\n  handleProductSelect: function handleProductSelect(value) {\n    this.model.productId = value;\n    this.model.productName = this.products.find(function (p) {\n      return p.id === value;\n    }).name;\n    this.filteredProducts = []; // 清空过滤列表\n  },\n  // 打开物料搜索弹窗\n  searchMaterial: function searchMaterial() {\n    this.inventoryModalVisible = true;\n    this.inventorySearchName = this.searchMaterialName;\n    this.searchInventory();\n  },\n  // 关闭物料搜索弹窗\n  closeInventoryModal: function closeInventoryModal() {\n    this.inventoryModalVisible = false;\n    this.inventoryList = [];\n  },\n  // 搜索库存物料\n  searchInventory: function () {\n    var _searchInventory = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n      var res;\n      return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n        while (1) {\n          switch (_context2.prev = _context2.next) {\n            case 0:\n              if (this.inventorySearchName.trim()) {\n                _context2.next = 3;\n                break;\n              }\n              this.$message.warning('请输入搜索关键词');\n              return _context2.abrupt(\"return\");\n            case 3:\n              _context2.prev = 3;\n              this.loadingInventory = true;\n              // 使用新的模糊搜索接口\n              _context2.next = 7;\n              return getAction(this.url.searchMaterials, {\n                keyword: this.inventorySearchName\n              });\n            case 7:\n              res = _context2.sent;\n              if (res.success) {\n                this.inventoryList = res.result || [];\n                if (this.inventoryList.length === 0) {\n                  this.$message.info('未找到匹配的物料，请尝试其他关键词');\n                }\n              } else {\n                this.$message.error('库存查询失败：' + (res.message || '请求异常'));\n                this.inventoryList = [];\n              }\n              _context2.next = 16;\n              break;\n            case 11:\n              _context2.prev = 11;\n              _context2.t0 = _context2[\"catch\"](3);\n              console.error('库存查询失败:', _context2.t0);\n              this.$message.error('库存查询失败：' + (_context2.t0.message || '未知错误'));\n              this.inventoryList = [];\n            case 16:\n              _context2.prev = 16;\n              this.loadingInventory = false;\n              return _context2.finish(16);\n            case 19:\n            case \"end\":\n              return _context2.stop();\n          }\n        }\n      }, _callee2, this, [[3, 11, 16, 19]]);\n    }));\n    function searchInventory() {\n      return _searchInventory.apply(this, arguments);\n    }\n    return searchInventory;\n  }(),\n  // 选择库存物料\n  // 选择库存物料\n  selectInventoryItem: function selectInventoryItem(item) {\n    var existingIndex = this.materialList.findIndex(function (m) {\n      return m.id === item.id;\n    });\n    if (existingIndex >= 0) {\n      this.$message.warning('该物料已添加到清单中');\n      return;\n    }\n    var newMaterial = {\n      id: item.id,\n      materialName: item.imItemName,\n      materialSpecifications: item.imItemUnit || '个',\n      // 将单位存储到规格字段\n      materialCount: 1,\n      imItemPrice: item.imItemPrice || 0,\n      // 新增：传递物料价格\n      companyId: '',\n      // 默认公司 ID\n      createBy: this.model.createBy || '',\n      // 创建人\n      createTime: moment().format('YYYY-MM-DD HH:mm:ss'),\n      // 创建时间\n      materialBillId: this.model.id || '',\n      // 物料清单 ID\n      productionOrderId: '',\n      // 生产订单 ID\n      sysOrgCode: this.model.sysOrgCode || '',\n      // 所属部门\n      updateBy: '',\n      // 更新人\n      updateTime: '' // 更新时间\n    };\n    this.materialList.push(newMaterial);\n    this.updateMaterialList();\n    this.$message.success(\"\\u5DF2\\u6DFB\\u52A0\\u7269\\u6599: \".concat(item.imItemName));\n    this.closeInventoryModal();\n  },\n  // 手动添加物料\n  // 手动添加物料\n  addMaterial: function addMaterial() {\n    var tempId = 'temp_' + ++this.tempIdCounter;\n    this.materialList.push({\n      tempId: tempId,\n      materialName: '',\n      // 空值\n      materialUnit: '',\n      // 空值\n      materialCount: 1,\n      // 默认数量\n      imItemPrice: 0,\n      // 新增：默认物料价格为 0\n      companyId: '',\n      // 默认公司 ID\n      createBy: '',\n      // 默认创建人\n      createTime: moment().format('YYYY-MM-DD HH:mm:ss'),\n      // 默认创建时间\n      materialBillId: this.model.id || '',\n      // 物料清单 ID\n      productionOrderId: '',\n      // 默认生产订单 ID\n      sysOrgCode: '',\n      // 默认所属部门\n      updateBy: '',\n      // 默认更新人\n      updateTime: '' // 默认更新时间\n    });\n    this.updateMaterialList();\n  },\n  // 删除物料\n  removeMaterial: function removeMaterial(index) {\n    this.materialList.splice(index, 1); // 从数组中移除\n    this.updateMaterialList(); // 更新 materialList\n  },\n  // 更新数量\n  handleCountChange: function handleCountChange(value, index) {\n    if (!this.materialList[index]) return;\n    this.materialList[index].materialCount = value; // 更新数量\n    this.updateMaterialList(); // 更新 materialList\n  },\n  // 更新materialJson\n  updateMaterialJson: function updateMaterialJson() {\n    try {\n      var jsonData = this.materialList.map(function (item) {\n        return _objectSpread({\n          materialName: item.materialName,\n          materialUnit: item.materialUnit,\n          materialCount: String(item.materialCount),\n          imItemPrice: item.imItemPrice || 0,\n          // 新增：传递物料价格\n          id: item.id || item.tempId\n        }, item.originalData ? {\n          originalData: item.originalData\n        } : {});\n      });\n      this.model.materialJson = JSON.stringify(jsonData);\n    } catch (error) {\n      console.error('生成物料JSON失败:', error);\n    }\n  },\n  // 从JSON恢复物料列表\n  restoreMaterialListFromJson: function restoreMaterialListFromJson() {\n    var _this3 = this;\n    try {\n      if (!Array.isArray(this.model.materialList)) {\n        this.materialList = [];\n        return;\n      }\n      this.materialList = this.model.materialList.map(function (item) {\n        return {\n          id: item.id || 'temp_' + ++_this3.tempIdCounter,\n          materialName: item.materialName || '',\n          materialSpecifications: item.materialSpecifications || '',\n          materialCount: parseInt(item.materialCount) || 1,\n          // 改为从 materialCount 获取\n          imItemPrice: item.imItemPrice || 0,\n          companyId: item.companyId || '',\n          createBy: item.createBy || '',\n          createTime: item.createTime || '',\n          materialBillId: item.materialBillId || '',\n          productionOrderId: item.productionOrderId || '',\n          sysOrgCode: item.sysOrgCode || '',\n          updateBy: item.updateBy || '',\n          updateTime: item.updateTime || ''\n        };\n      });\n    } catch (error) {\n      console.error('恢复物料列表失败:', error);\n      this.materialList = [];\n    }\n  },\n  edit: function edit(record) {\n    var _this4 = this;\n    this.model = Object.assign({}, record);\n    this.visibleCheck = this.model.materialType === 0 || this.model.materialType === \"0\";\n    this.visible = true; // 设置visible属性为true\n\n    // 打印当前编辑记录，方便排查问题\n    console.log('编辑记录:', record);\n\n    // 确保productId正确设置，防止undefined或null值\n    if (!this.model.productId) {\n      this.model.productId = '';\n      this.model.productName = '';\n    }\n\n    // 当存在productId但没有productName时，从加载的产品列表中查找产品名称\n    if (this.model.productId && !this.model.productName) {\n      // 如果产品列表已加载\n      if (this.products && this.products.length > 0) {\n        var selectedProduct = this.products.find(function (p) {\n          return p.id === _this4.model.productId;\n        });\n        if (selectedProduct) {\n          this.model.productName = selectedProduct.name;\n          console.log('从产品列表中获取到产品名称:', this.model.productName);\n        } else {\n          // 如果在本地找不到，从服务器查询\n          this.queryProductNameById(this.model.productId);\n        }\n      } else {\n        // 如果产品列表尚未加载，先加载产品列表\n        this.loadProducts().then(function () {\n          var selectedProduct = _this4.products.find(function (p) {\n            return p.id === _this4.model.productId;\n          });\n          if (selectedProduct) {\n            _this4.model.productName = selectedProduct.name;\n            console.log('异步加载产品名称成功:', _this4.model.productName);\n          } else {\n            // 如果仍然找不到，尝试单独查询这个产品\n            _this4.queryProductNameById(_this4.model.productId);\n          }\n        });\n      }\n    }\n\n    // 恢复物料列表\n    if (Array.isArray(this.model.materialList) && this.model.materialList.length > 0) {\n      // 直接使用后端返回的物料列表\n      this.materialList = this.model.materialList.map(function (item) {\n        return {\n          id: item.id,\n          materialName: item.materialName || '',\n          materialSpecifications: item.materialSpecifications || '',\n          materialCount: parseInt(item.materialCount) || 1,\n          imItemPrice: item.imItemPrice || 0,\n          companyId: item.companyId || '',\n          createBy: item.createBy || '',\n          createTime: item.createTime || '',\n          materialBillId: item.materialBillId || '',\n          productionOrderId: item.productionOrderId || '',\n          sysOrgCode: item.sysOrgCode || '',\n          updateBy: item.updateBy || '',\n          updateTime: item.updateTime || ''\n        };\n      });\n    } else {\n      // 如果没有物料列表数据，尝试从materialJson解析\n      this.restoreMaterialListFromJson();\n    }\n    console.log('加载的物料列表:', this.materialList);\n  },\n  // 通过产品ID查询产品名称\n  queryProductNameById: function queryProductNameById(productId) {\n    var _this5 = this;\n    if (!productId) return;\n    getAction(this.url.queryProductById, {\n      id: productId\n    }).then(function (res) {\n      if (res.success && res.result) {\n        _this5.model.productName = res.result.productName || res.result.name;\n        console.log('通过API查询获取产品名称:', _this5.model.productName);\n      } else {\n        console.warn('查询产品详情返回空数据');\n      }\n    }).catch(function (err) {\n      console.error('查询产品详情失败:', err);\n    });\n  },\n  submitForm: function submitForm() {\n    var _this6 = this;\n    var invalidItem = this.materialList.find(function (item) {\n      return !moment(item.createTime, 'YYYY-MM-DD HH:mm:ss', true).isValid();\n    });\n    if (invalidItem) {\n      this.$message.error(\"\\u7269\\u6599 \".concat(invalidItem.materialName, \" \\u65F6\\u95F4\\u683C\\u5F0F\\u9519\\u8BEF\"));\n      return;\n    }\n\n    // 检查是否有物料\n    if (this.materialList.length === 0) {\n      this.$message.warning('请至少添加一种物料');\n      return;\n    }\n\n    // 更新最终的 materialList\n    this.updateMaterialList();\n\n    // 提取 materialList 中的 imItemPrice\n    var imItemPrice = this.materialList.reduce(function (total, item) {\n      return total + (item.imItemPrice || 0) * (item.materialCount || 1); // 改为 materialCount\n    }, 0);\n\n    // 构造最终的请求参数\n    var requestData = _objectSpread(_objectSpread({}, this.model), {}, {\n      imItemPrice: imItemPrice,\n      materialList: this.materialList\n    });\n    console.log('最终请求参数:', requestData);\n\n    // 验证表单\n    this.$refs.form.validate(function (valid) {\n      if (valid) {\n        _this6.confirmLoading = true;\n        var httpurl = _this6.model.id ? _this6.url.edit : _this6.url.add;\n        var method = _this6.model.id ? 'put' : 'post';\n        httpAction(httpurl, requestData, method).then(function (res) {\n          if (res.success) {\n            _this6.$message.success(res.message);\n            _this6.$emit('ok');\n          } else {\n            _this6.$message.warning(res.message);\n          }\n        }).finally(function () {\n          _this6.confirmLoading = false;\n        });\n      }\n    });\n  }\n}), _name$components$prop);", {"version": 3, "sources": ["MaterialBillForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiUA,SAAA,UAAA,EAAA,SAAA,QAAA,cAAA;AACA,SAAA,sBAAA,QAAA,cAAA;AACA,OAAA,MAAA,MAAA,QAAA;AACA,gBAAA,qBAAA;EACA,IAAA,EAAA,kBAAA;EACA,UAAA,EAAA,CACA,CAAA;EACA,KAAA,EAAA;IACA;IACA,QAAA,EAAA;MACA,IAAA,EAAA,OAAA;MACA,OAAA,EAAA,KAAA;MACA,QAAA,EAAA;IACA;EACA,CAAA;EACA,IAAA,WAAA,KAAA,EAAA;IACA,OAAA;MACA,QAAA,EAAA,EAAA;MACA,gBAAA,EAAA,EAAA;MAAA;MACA,eAAA,EAAA,KAAA;MACA,YAAA,EAAA,IAAA;MACA,YAAA,EAAA,EAAA;MACA,kBAAA,EAAA,EAAA;MACA,qBAAA,EAAA,KAAA;MACA,mBAAA,EAAA,EAAA;MACA,aAAA,EAAA,EAAA;MACA,gBAAA,EAAA,KAAA;MACA,aAAA,EAAA,CAAA;MACA,KAAA,EAAA;QACA,SAAA,EAAA,EAAA;QACA,WAAA,EAAA,EAAA;QACA,gBAAA,EAAA,EAAA;QACA,YAAA,EAAA,CAAA;QACA,YAAA,EAAA,EAAA;QACA,sBAAA,EAAA,EAAA;QACA,kBAAA,EAAA,EAAA;QAAA;QACA,WAAA,EAAA;MACA,CAAA;MACA,uBAAA,EAAA,EAAA;MAAA;MACA,eAAA,EAAA,CACA;QACA,KAAA,EAAA,MAAA;QACA,SAAA,EAAA,cAAA;QACA,GAAA,EAAA,cAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA,CAAA;QACA,KAAA,EAAA,GAAA;QACA,QAAA,EAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,IAAA;QACA,SAAA,EAAA,cAAA;QACA,GAAA,EAAA,cAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA,CAAA;QACA,KAAA,EAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,IAAA;QACA,GAAA,EAAA,eAAA;QACA,KAAA,EAAA,GAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,IAAA;QACA,GAAA,EAAA,QAAA;QACA,KAAA,EAAA,EAAA;QACA,KAAA,EAAA,OAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA,CACA;MACA,gBAAA,EAAA,CACA;QACA,KAAA,EAAA,MAAA;QACA,SAAA,EAAA,YAAA;QACA,GAAA,EAAA,YAAA;QACA,QAAA,EAAA,IAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,IAAA;QACA,SAAA,EAAA,QAAA;QACA,GAAA,EAAA,QAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,MAAA;QACA,SAAA,EAAA,qBAAA;QACA,GAAA,EAAA,qBAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,MAAA;QACA,SAAA,EAAA,eAAA;QACA,GAAA,EAAA,eAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,MAAA;QACA,SAAA,EAAA,cAAA;QACA,GAAA,EAAA,cAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,QAAA;QACA,SAAA,EAAA,kBAAA;QACA,GAAA,EAAA,kBAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA,EACA;QACA,KAAA,EAAA,IAAA;QACA,GAAA,EAAA,QAAA;QACA,KAAA,EAAA,GAAA;QACA,KAAA,EAAA,OAAA;QACA,WAAA,EAAA;UAAA,YAAA,EAAA;QAAA;MACA,CAAA,CACA;MACA,QAAA,EAAA;QACA,EAAA,EAAA;UAAA,IAAA,EAAA;QAAA,CAAA;QACA,EAAA,EAAA;UAAA,IAAA,EAAA;QAAA;MACA,CAAA;MACA,UAAA,EAAA;QACA,EAAA,EAAA;UAAA,IAAA,EAAA;QAAA,CAAA;QACA,EAAA,EAAA;UAAA,IAAA,EAAA;QAAA;MACA,CAAA;MACA,cAAA,EAAA,KAAA;MACA,cAAA,EAAA;QACA,SAAA,EAAA,CACA;UAAA,QAAA,EAAA,IAAA;UAAA,OAAA,EAAA;QAAA,CAAA,CACA;QACA,gBAAA,EAAA,CACA;UAAA,QAAA,EAAA,IAAA;UAAA,OAAA,EAAA;QAAA,CAAA,EACA;UAAA,GAAA,EAAA,EAAA;UAAA,OAAA,EAAA;QAAA,CAAA;MAEA,CAAA;MACA,GAAA,EAAA;QACA,GAAA,EAAA,yBAAA;QACA,IAAA,EAAA,0BAAA;QACA,SAAA,EAAA,+BAAA;QACA,aAAA,EAAA,iCAAA;QACA,eAAA,EAAA,4CAAA;QACA,gBAAA,EAAA;MACA;IACA,CAAA;EACA,CAAA;EACA,OAAA,WAAA,QAAA,EAAA;IACA,IAAA,CAAA,YAAA,CAAA,CAAA;IACA;IACA,IAAA,cAAA,GAAA,IAAA,CAAA,MAAA,CAAA,KAAA,CAAA,SAAA;IACA,IAAA,cAAA,EAAA;MACA,IAAA,CAAA,KAAA,CAAA,SAAA,GAAA,cAAA;MACA,IAAA,CAAA,mBAAA,CAAA,cAAA,CAAA;IACA;EACA,CAAA;EACA,KAAA,EAAA;IACA,YAAA,EAAA;MACA,OAAA,WAAA,QAAA,MAAA,EAAA;QACA;QACA,IAAA,CAAA,kBAAA,CAAA,CAAA;MACA,CAAA;MACA,IAAA,EAAA;IACA;EACA,CAAA;EACA,QAAA,EAAA;IACA,YAAA,WAAA,aAAA,EAAA;MACA,OAAA,IAAA,CAAA,QAAA;IACA;EACA;AAAA,GAAA,eAAA,CAAA,qBAAA,WACA;EACA;EACA,uBAAA,EAAA;IACA,OAAA,WAAA,QAAA,MAAA,EAAA;MACA,IAAA,CAAA,KAAA,CAAA,kBAAA,GAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA;IACA,CAAA;IACA,IAAA,EAAA;EACA,CAAA;EACA;EACA,0BAAA,EAAA;IACA,OAAA,WAAA,QAAA,MAAA,EAAA;MACA,IAAA,MAAA,IAAA,OAAA,MAAA,KAAA,QAAA,EAAA;QACA,IAAA,CAAA,uBAAA,GAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,MAAA,CAAA,UAAA,IAAA;UAAA,OAAA,IAAA,CAAA,IAAA,CAAA,CAAA;QAAA,EAAA;MACA,CAAA,MAAA;QACA,IAAA,CAAA,uBAAA,GAAA,EAAA;MACA;IACA,CAAA;IACA,SAAA,EAAA;EACA;AACA,CAAA,GAAA,eAAA,CAAA,qBAAA,sBAAA,QAAA,EACA;EACA;EACA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;EACA;EACA,IAAA,IAAA,CAAA,KAAA,CAAA,YAAA,KAAA,SAAA,EAAA;IACA,IAAA,CAAA,KAAA,CAAA,YAAA,GAAA,CAAA;EACA;AACA,CAAA,GAAA,eAAA,CAAA,qBAAA,aACA;EACA;EACA,SAAA,WAAA,UAAA,EAAA;IACA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA;IACA,IAAA,CAAA,YAAA,GAAA,EAAA;IACA,IAAA,CAAA,YAAA,GAAA,IAAA;IACA,IAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA;MACA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA;IACA;EACA,CAAA;EAEA;EACA,kBAAA,WAAA,mBAAA,EAAA;IAAA,IAAA,KAAA;IACA,IAAA;MACA,IAAA,CAAA,KAAA,CAAA,YAAA,GAAA,IAAA,CAAA,YAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA;QAAA,OAAA;UACA,EAAA,EAAA,IAAA,CAAA,EAAA,IAAA,IAAA,CAAA,MAAA;UACA,YAAA,EAAA,IAAA,CAAA,YAAA,IAAA,EAAA;UACA,YAAA,EAAA,IAAA,CAAA,YAAA,IAAA,EAAA;UACA,aAAA,EAAA,MAAA,CAAA,IAAA,CAAA,aAAA,IAAA,CAAA,CAAA;UAAA;UACA,WAAA,EAAA,IAAA,CAAA,WAAA,IAAA,CAAA;UACA,SAAA,EAAA,IAAA,CAAA,SAAA,IAAA,EAAA;UACA,QAAA,EAAA,IAAA,CAAA,QAAA,IAAA,EAAA;UACA,UAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;UACA,cAAA,EAAA,KAAA,CAAA,KAAA,CAAA,EAAA,IAAA,EAAA;UACA,iBAAA,EAAA,IAAA,CAAA,iBAAA,IAAA,EAAA;UACA,UAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;UACA,QAAA,EAAA,IAAA,CAAA,QAAA,IAAA,EAAA;UACA,UAAA,EAAA,IAAA,CAAA,UAAA,IAAA;QACA,CAAA;MAAA,CAAA,CAAA;MAEA,OAAA,CAAA,GAAA,CAAA,oBAAA,EAAA,IAAA,CAAA,KAAA,CAAA,YAAA,CAAA;IACA,CAAA,CAAA,OAAA,KAAA,EAAA;MACA,OAAA,CAAA,KAAA,CAAA,qBAAA,EAAA,KAAA,CAAA;IACA;EACA,CAAA;EAEA,OAAA,WAAA,QAAA,OAAA,EAAA;IACA,IAAA,OAAA,EAAA;MACA,IAAA,CAAA,KAAA,CAAA,YAAA,GAAA,CAAA,CAAA,CAAA;MACA,IAAA,CAAA,YAAA,GAAA,IAAA;IACA,CAAA,MAAA;MACA,IAAA,CAAA,KAAA,CAAA,YAAA,GAAA,CAAA,CAAA,CAAA;MACA,IAAA,CAAA,YAAA,GAAA,KAAA;IACA;EACA,CAAA;EAEA,YAAA;IAAA,IAAA,aAAA,GAAA,iBAAA,cAAA,mBAAA,CAAA,IAAA,UAAA,QAAA;MAAA,IAAA,GAAA;MAAA,OAAA,mBAAA,CAAA,IAAA,UAAA,SAAA,QAAA;QAAA;UAAA,QAAA,QAAA,CAAA,IAAA,GAAA,QAAA,CAAA,IAAA;YAAA;cAAA,QAAA,CAAA,IAAA;cAEA,IAAA,CAAA,eAAA,GAAA,IAAA;cAAA,QAAA,CAAA,IAAA;cAAA,OACA,SAAA,CAAA,iDAAA,EAAA,CAAA,CAAA,CAAA;YAAA;cAAA,GAAA,GAAA,QAAA,CAAA,IAAA;cAAA,KACA,GAAA,CAAA,OAAA;gBAAA,QAAA,CAAA,IAAA;gBAAA;cAAA;cACA,IAAA,CAAA,QAAA,GAAA,CAAA,GAAA,CAAA,MAAA,IAAA,EAAA,EACA,MAAA,CAAA,UAAA,CAAA;gBAAA,OAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,WAAA;cAAA,EAAA,CACA,GAAA,CAAA,UAAA,CAAA;gBAAA,OAAA;kBACA,EAAA,EAAA,CAAA,CAAA,EAAA;kBACA,IAAA,EAAA,CAAA,CAAA;gBACA,CAAA;cAAA,CAAA,CAAA;;cAEA;cACA,IAAA,CAAA,gBAAA,GAAA,kBAAA,CAAA,IAAA,CAAA,QAAA,CAAA;cAAA,OAAA,QAAA,CAAA,MAAA,WAEA,IAAA,CAAA,QAAA;YAAA;cAEA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,SAAA,IAAA,GAAA,CAAA,OAAA,IAAA,MAAA,CAAA,CAAA;cACA,IAAA,CAAA,QAAA,GAAA,EAAA;cACA,IAAA,CAAA,gBAAA,GAAA,EAAA;cAAA,OAAA,QAAA,CAAA,MAAA,WACA,EAAA;YAAA;cAAA,QAAA,CAAA,IAAA;cAAA;YAAA;cAAA,QAAA,CAAA,IAAA;cAAA,QAAA,CAAA,EAAA,GAAA,QAAA;cAGA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,QAAA,CAAA,EAAA,CAAA;cACA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,SAAA,IAAA,QAAA,CAAA,EAAA,CAAA,OAAA,IAAA,MAAA,CAAA,CAAA;cACA,IAAA,CAAA,QAAA,GAAA,EAAA;cACA,IAAA,CAAA,gBAAA,GAAA,EAAA;cAAA,OAAA,QAAA,CAAA,MAAA,WACA,EAAA;YAAA;cAAA,QAAA,CAAA,IAAA;cAEA,IAAA,CAAA,eAAA,GAAA,KAAA;cAAA,OAAA,QAAA,CAAA,MAAA;YAAA;YAAA;cAAA,OAAA,QAAA,CAAA,IAAA;UAAA;QAAA;MAAA,GAAA,OAAA;IAAA;IAAA,SAAA,aAAA;MAAA,OAAA,aAAA,CAAA,KAAA,OAAA,SAAA;IAAA;IAAA,OAAA,YAAA;EAAA;EAIA,GAAA,WAAA,IAAA,EAAA;IACA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,YAAA,CAAA;EACA,CAAA;EAEA;EACA,mBAAA,WAAA,oBAAA,SAAA,EAAA;IAAA,IAAA,MAAA;IACA,IAAA,CAAA,SAAA,EAAA;MACA,IAAA,CAAA,KAAA,CAAA,SAAA,GAAA,EAAA;MACA,IAAA,CAAA,KAAA,CAAA,WAAA,GAAA,EAAA;MACA;IACA;IAEA,IAAA,CAAA,KAAA,CAAA,SAAA,GAAA,SAAA;;IAEA;IACA,IAAA,eAAA,GAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CAAA;MAAA,OAAA,CAAA,CAAA,EAAA,KAAA,SAAA;IAAA,EAAA;IACA,IAAA,eAAA,EAAA;MACA,IAAA,CAAA,KAAA,CAAA,WAAA,GAAA,eAAA,CAAA,IAAA;MACA,OAAA,CAAA,GAAA,CAAA,QAAA,EAAA,eAAA,CAAA,IAAA,CAAA;IACA,CAAA,MAAA;MACA;MACA,SAAA,CAAA,IAAA,CAAA,GAAA,CAAA,gBAAA,EAAA;QAAA,EAAA,EAAA;MAAA,CAAA,CAAA,CACA,IAAA,CAAA,UAAA,GAAA,EAAA;QACA,IAAA,GAAA,CAAA,OAAA,IAAA,GAAA,CAAA,MAAA,EAAA;UACA,MAAA,CAAA,KAAA,CAAA,WAAA,GAAA,GAAA,CAAA,MAAA,CAAA,WAAA,IAAA,GAAA,CAAA,MAAA,CAAA,IAAA;UACA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,MAAA,CAAA,KAAA,CAAA,WAAA,CAAA;QACA;MACA,CAAA,CAAA,CACA,KAAA,CAAA,UAAA,GAAA,EAAA;QACA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA,CAAA;MACA,CAAA,CAAA;IACA;EACA,CAAA;EAEA;EACA,mBAAA,WAAA,oBAAA,UAAA,EAAA;IACA,IAAA,CAAA,gBAAA,GAAA,IAAA,CAAA,QAAA,CAAA,MAAA,CAAA,UAAA,OAAA;MAAA,OACA,OAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,CAAA,QAAA,CAAA,UAAA,CAAA,WAAA,CAAA,CAAA,CAAA;IAAA,CACA,CAAA;EACA,CAAA;EAEA;EACA,mBAAA,WAAA,oBAAA,KAAA,EAAA;IACA,IAAA,CAAA,KAAA,CAAA,SAAA,GAAA,KAAA;IACA,IAAA,CAAA,KAAA,CAAA,WAAA,GAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CAAA;MAAA,OAAA,CAAA,CAAA,EAAA,KAAA,KAAA;IAAA,EAAA,CAAA,IAAA;IACA,IAAA,CAAA,gBAAA,GAAA,EAAA,CAAA,CAAA;EACA,CAAA;EAEA;EACA,cAAA,WAAA,eAAA,EAAA;IACA,IAAA,CAAA,qBAAA,GAAA,IAAA;IACA,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAA,kBAAA;IACA,IAAA,CAAA,eAAA,CAAA,CAAA;EACA,CAAA;EAEA;EACA,mBAAA,WAAA,oBAAA,EAAA;IACA,IAAA,CAAA,qBAAA,GAAA,KAAA;IACA,IAAA,CAAA,aAAA,GAAA,EAAA;EACA,CAAA;EAEA;EACA,eAAA;IAAA,IAAA,gBAAA,GAAA,iBAAA,cAAA,mBAAA,CAAA,IAAA,UAAA,SAAA;MAAA,IAAA,GAAA;MAAA,OAAA,mBAAA,CAAA,IAAA,UAAA,UAAA,SAAA;QAAA;UAAA,QAAA,SAAA,CAAA,IAAA,GAAA,SAAA,CAAA,IAAA;YAAA;cAAA,IACA,IAAA,CAAA,mBAAA,CAAA,IAAA,CAAA,CAAA;gBAAA,SAAA,CAAA,IAAA;gBAAA;cAAA;cACA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,CAAA;cAAA,OAAA,SAAA,CAAA,MAAA;YAAA;cAAA,SAAA,CAAA,IAAA;cAKA,IAAA,CAAA,gBAAA,GAAA,IAAA;cACA;cAAA,SAAA,CAAA,IAAA;cAAA,OACA,SAAA,CAAA,IAAA,CAAA,GAAA,CAAA,eAAA,EAAA;gBACA,OAAA,EAAA,IAAA,CAAA;cACA,CAAA,CAAA;YAAA;cAFA,GAAA,GAAA,SAAA,CAAA,IAAA;cAIA,IAAA,GAAA,CAAA,OAAA,EAAA;gBACA,IAAA,CAAA,aAAA,GAAA,GAAA,CAAA,MAAA,IAAA,EAAA;gBACA,IAAA,IAAA,CAAA,aAAA,CAAA,MAAA,KAAA,CAAA,EAAA;kBACA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,mBAAA,CAAA;gBACA;cACA,CAAA,MAAA;gBACA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,SAAA,IAAA,GAAA,CAAA,OAAA,IAAA,MAAA,CAAA,CAAA;gBACA,IAAA,CAAA,aAAA,GAAA,EAAA;cACA;cAAA,SAAA,CAAA,IAAA;cAAA;YAAA;cAAA,SAAA,CAAA,IAAA;cAAA,SAAA,CAAA,EAAA,GAAA,SAAA;cAEA,OAAA,CAAA,KAAA,CAAA,SAAA,EAAA,SAAA,CAAA,EAAA,CAAA;cACA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,SAAA,IAAA,SAAA,CAAA,EAAA,CAAA,OAAA,IAAA,MAAA,CAAA,CAAA;cACA,IAAA,CAAA,aAAA,GAAA,EAAA;YAAA;cAAA,SAAA,CAAA,IAAA;cAEA,IAAA,CAAA,gBAAA,GAAA,KAAA;cAAA,OAAA,SAAA,CAAA,MAAA;YAAA;YAAA;cAAA,OAAA,SAAA,CAAA,IAAA;UAAA;QAAA;MAAA,GAAA,QAAA;IAAA;IAAA,SAAA,gBAAA;MAAA,OAAA,gBAAA,CAAA,KAAA,OAAA,SAAA;IAAA;IAAA,OAAA,eAAA;EAAA;EAIA;EACA;EACA,mBAAA,WAAA,oBAAA,IAAA,EAAA;IACA,IAAA,aAAA,GAAA,IAAA,CAAA,YAAA,CAAA,SAAA,CAAA,UAAA,CAAA;MAAA,OAAA,CAAA,CAAA,EAAA,KAAA,IAAA,CAAA,EAAA;IAAA,EAAA;IAEA,IAAA,aAAA,IAAA,CAAA,EAAA;MACA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,YAAA,CAAA;MACA;IACA;IAEA,IAAA,WAAA,GAAA;MACA,EAAA,EAAA,IAAA,CAAA,EAAA;MACA,YAAA,EAAA,IAAA,CAAA,UAAA;MACA,sBAAA,EAAA,IAAA,CAAA,UAAA,IAAA,GAAA;MAAA;MACA,aAAA,EAAA,CAAA;MACA,WAAA,EAAA,IAAA,CAAA,WAAA,IAAA,CAAA;MAAA;MACA,SAAA,EAAA,EAAA;MAAA;MACA,QAAA,EAAA,IAAA,CAAA,KAAA,CAAA,QAAA,IAAA,EAAA;MAAA;MACA,UAAA,EAAA,MAAA,CAAA,CAAA,CAAA,MAAA,CAAA,qBAAA,CAAA;MAAA;MACA,cAAA,EAAA,IAAA,CAAA,KAAA,CAAA,EAAA,IAAA,EAAA;MAAA;MACA,iBAAA,EAAA,EAAA;MAAA;MACA,UAAA,EAAA,IAAA,CAAA,KAAA,CAAA,UAAA,IAAA,EAAA;MAAA;MACA,QAAA,EAAA,EAAA;MAAA;MACA,UAAA,EAAA,EAAA,CAAA;IACA,CAAA;IAEA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,WAAA,CAAA;IACA,IAAA,CAAA,kBAAA,CAAA,CAAA;IACA,IAAA,CAAA,QAAA,CAAA,OAAA,oCAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA;IACA,IAAA,CAAA,mBAAA,CAAA,CAAA;EACA,CAAA;EAEA;EACA;EACA,WAAA,WAAA,YAAA,EAAA;IACA,IAAA,MAAA,GAAA,OAAA,GAAA,EAAA,IAAA,CAAA,aAAA;IACA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA;MACA,MAAA,EAAA,MAAA;MACA,YAAA,EAAA,EAAA;MAAA;MACA,YAAA,EAAA,EAAA;MAAA;MACA,aAAA,EAAA,CAAA;MAAA;MACA,WAAA,EAAA,CAAA;MAAA;MACA,SAAA,EAAA,EAAA;MAAA;MACA,QAAA,EAAA,EAAA;MAAA;MACA,UAAA,EAAA,MAAA,CAAA,CAAA,CAAA,MAAA,CAAA,qBAAA,CAAA;MAAA;MACA,cAAA,EAAA,IAAA,CAAA,KAAA,CAAA,EAAA,IAAA,EAAA;MAAA;MACA,iBAAA,EAAA,EAAA;MAAA;MACA,UAAA,EAAA,EAAA;MAAA;MACA,QAAA,EAAA,EAAA;MAAA;MACA,UAAA,EAAA,EAAA,CAAA;IACA,CAAA,CAAA;IAEA,IAAA,CAAA,kBAAA,CAAA,CAAA;EACA,CAAA;EAEA;EACA,cAAA,WAAA,eAAA,KAAA,EAAA;IACA,IAAA,CAAA,YAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,CAAA;IACA,IAAA,CAAA,kBAAA,CAAA,CAAA,CAAA,CAAA;EACA,CAAA;EAEA;EACA,iBAAA,WAAA,kBAAA,KAAA,EAAA,KAAA,EAAA;IACA,IAAA,CAAA,IAAA,CAAA,YAAA,CAAA,KAAA,CAAA,EAAA;IACA,IAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,aAAA,GAAA,KAAA,CAAA,CAAA;IACA,IAAA,CAAA,kBAAA,CAAA,CAAA,CAAA,CAAA;EACA,CAAA;EAEA;EACA,kBAAA,WAAA,mBAAA,EAAA;IACA,IAAA;MACA,IAAA,QAAA,GAAA,IAAA,CAAA,YAAA,CAAA,GAAA,CAAA,UAAA,IAAA;QAAA,OAAA,aAAA;UACA,YAAA,EAAA,IAAA,CAAA,YAAA;UACA,YAAA,EAAA,IAAA,CAAA,YAAA;UACA,aAAA,EAAA,MAAA,CAAA,IAAA,CAAA,aAAA,CAAA;UACA,WAAA,EAAA,IAAA,CAAA,WAAA,IAAA,CAAA;UAAA;UACA,EAAA,EAAA,IAAA,CAAA,EAAA,IAAA,IAAA,CAAA;QAAA,GAEA,IAAA,CAAA,YAAA,GAAA;UAAA,YAAA,EAAA,IAAA,CAAA;QAAA,CAAA,GAAA,CAAA,CAAA;MAAA,CACA,CAAA;MAEA,IAAA,CAAA,KAAA,CAAA,YAAA,GAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA;IACA,CAAA,CAAA,OAAA,KAAA,EAAA;MACA,OAAA,CAAA,KAAA,CAAA,aAAA,EAAA,KAAA,CAAA;IACA;EACA,CAAA;EACA;EACA,2BAAA,WAAA,4BAAA,EAAA;IAAA,IAAA,MAAA;IACA,IAAA;MACA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,YAAA,CAAA,EAAA;QACA,IAAA,CAAA,YAAA,GAAA,EAAA;QACA;MACA;MAEA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,KAAA,CAAA,YAAA,CAAA,GAAA,CAAA,UAAA,IAAA;QAAA,OAAA;UACA,EAAA,EAAA,IAAA,CAAA,EAAA,IAAA,OAAA,GAAA,EAAA,MAAA,CAAA,aAAA;UACA,YAAA,EAAA,IAAA,CAAA,YAAA,IAAA,EAAA;UACA,sBAAA,EAAA,IAAA,CAAA,sBAAA,IAAA,EAAA;UACA,aAAA,EAAA,QAAA,CAAA,IAAA,CAAA,aAAA,CAAA,IAAA,CAAA;UAAA;UACA,WAAA,EAAA,IAAA,CAAA,WAAA,IAAA,CAAA;UACA,SAAA,EAAA,IAAA,CAAA,SAAA,IAAA,EAAA;UACA,QAAA,EAAA,IAAA,CAAA,QAAA,IAAA,EAAA;UACA,UAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;UACA,cAAA,EAAA,IAAA,CAAA,cAAA,IAAA,EAAA;UACA,iBAAA,EAAA,IAAA,CAAA,iBAAA,IAAA,EAAA;UACA,UAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;UACA,QAAA,EAAA,IAAA,CAAA,QAAA,IAAA,EAAA;UACA,UAAA,EAAA,IAAA,CAAA,UAAA,IAAA;QACA,CAAA;MAAA,CAAA,CAAA;IACA,CAAA,CAAA,OAAA,KAAA,EAAA;MACA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,KAAA,CAAA;MACA,IAAA,CAAA,YAAA,GAAA,EAAA;IACA;EACA,CAAA;EAEA,IAAA,WAAA,KAAA,MAAA,EAAA;IAAA,IAAA,MAAA;IACA,IAAA,CAAA,KAAA,GAAA,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,MAAA,CAAA;IACA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,KAAA,CAAA,YAAA,KAAA,CAAA,IAAA,IAAA,CAAA,KAAA,CAAA,YAAA,KAAA,GAAA;IACA,IAAA,CAAA,OAAA,GAAA,IAAA,CAAA,CAAA;;IAEA;IACA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,MAAA,CAAA;;IAEA;IACA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,SAAA,EAAA;MACA,IAAA,CAAA,KAAA,CAAA,SAAA,GAAA,EAAA;MACA,IAAA,CAAA,KAAA,CAAA,WAAA,GAAA,EAAA;IACA;;IAEA;IACA,IAAA,IAAA,CAAA,KAAA,CAAA,SAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,WAAA,EAAA;MACA;MACA,IAAA,IAAA,CAAA,QAAA,IAAA,IAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;QACA,IAAA,eAAA,GAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CAAA;UAAA,OAAA,CAAA,CAAA,EAAA,KAAA,MAAA,CAAA,KAAA,CAAA,SAAA;QAAA,EAAA;QACA,IAAA,eAAA,EAAA;UACA,IAAA,CAAA,KAAA,CAAA,WAAA,GAAA,eAAA,CAAA,IAAA;UACA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,IAAA,CAAA,KAAA,CAAA,WAAA,CAAA;QACA,CAAA,MAAA;UACA;UACA,IAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA;QACA;MACA,CAAA,MAAA;QACA;QACA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA;UACA,IAAA,eAAA,GAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CAAA;YAAA,OAAA,CAAA,CAAA,EAAA,KAAA,MAAA,CAAA,KAAA,CAAA,SAAA;UAAA,EAAA;UACA,IAAA,eAAA,EAAA;YACA,MAAA,CAAA,KAAA,CAAA,WAAA,GAAA,eAAA,CAAA,IAAA;YACA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,MAAA,CAAA,KAAA,CAAA,WAAA,CAAA;UACA,CAAA,MAAA;YACA;YACA,MAAA,CAAA,oBAAA,CAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA;UACA;QACA,CAAA,CAAA;MACA;IACA;;IAEA;IACA,IAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,YAAA,CAAA,IAAA,IAAA,CAAA,KAAA,CAAA,YAAA,CAAA,MAAA,GAAA,CAAA,EAAA;MACA;MACA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,KAAA,CAAA,YAAA,CAAA,GAAA,CAAA,UAAA,IAAA;QAAA,OAAA;UACA,EAAA,EAAA,IAAA,CAAA,EAAA;UACA,YAAA,EAAA,IAAA,CAAA,YAAA,IAAA,EAAA;UACA,sBAAA,EAAA,IAAA,CAAA,sBAAA,IAAA,EAAA;UACA,aAAA,EAAA,QAAA,CAAA,IAAA,CAAA,aAAA,CAAA,IAAA,CAAA;UACA,WAAA,EAAA,IAAA,CAAA,WAAA,IAAA,CAAA;UACA,SAAA,EAAA,IAAA,CAAA,SAAA,IAAA,EAAA;UACA,QAAA,EAAA,IAAA,CAAA,QAAA,IAAA,EAAA;UACA,UAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;UACA,cAAA,EAAA,IAAA,CAAA,cAAA,IAAA,EAAA;UACA,iBAAA,EAAA,IAAA,CAAA,iBAAA,IAAA,EAAA;UACA,UAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;UACA,QAAA,EAAA,IAAA,CAAA,QAAA,IAAA,EAAA;UACA,UAAA,EAAA,IAAA,CAAA,UAAA,IAAA;QACA,CAAA;MAAA,CAAA,CAAA;IACA,CAAA,MAAA;MACA;MACA,IAAA,CAAA,2BAAA,CAAA,CAAA;IACA;IAEA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,IAAA,CAAA,YAAA,CAAA;EACA,CAAA;EAEA;EACA,oBAAA,WAAA,qBAAA,SAAA,EAAA;IAAA,IAAA,MAAA;IACA,IAAA,CAAA,SAAA,EAAA;IAEA,SAAA,CAAA,IAAA,CAAA,GAAA,CAAA,gBAAA,EAAA;MAAA,EAAA,EAAA;IAAA,CAAA,CAAA,CACA,IAAA,CAAA,UAAA,GAAA,EAAA;MACA,IAAA,GAAA,CAAA,OAAA,IAAA,GAAA,CAAA,MAAA,EAAA;QACA,MAAA,CAAA,KAAA,CAAA,WAAA,GAAA,GAAA,CAAA,MAAA,CAAA,WAAA,IAAA,GAAA,CAAA,MAAA,CAAA,IAAA;QACA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,MAAA,CAAA,KAAA,CAAA,WAAA,CAAA;MACA,CAAA,MAAA;QACA,OAAA,CAAA,IAAA,CAAA,aAAA,CAAA;MACA;IACA,CAAA,CAAA,CACA,KAAA,CAAA,UAAA,GAAA,EAAA;MACA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA,CAAA;IACA,CAAA,CAAA;EACA,CAAA;EAEA,UAAA,WAAA,WAAA,EAAA;IAAA,IAAA,MAAA;IACA,IAAA,WAAA,GAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,UAAA,IAAA;MAAA,OACA,CAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,qBAAA,EAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA;IAAA,CACA,CAAA;IAEA,IAAA,WAAA,EAAA;MACA,IAAA,CAAA,QAAA,CAAA,KAAA,iBAAA,MAAA,CAAA,WAAA,CAAA,YAAA,0CAAA,CAAA;MACA;IACA;;IAEA;IACA,IAAA,IAAA,CAAA,YAAA,CAAA,MAAA,KAAA,CAAA,EAAA;MACA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,WAAA,CAAA;MACA;IACA;;IAEA;IACA,IAAA,CAAA,kBAAA,CAAA,CAAA;;IAEA;IACA,IAAA,WAAA,GAAA,IAAA,CAAA,YAAA,CAAA,MAAA,CAAA,UAAA,KAAA,EAAA,IAAA,EAAA;MACA,OAAA,KAAA,GAAA,CAAA,IAAA,CAAA,WAAA,IAAA,CAAA,KAAA,IAAA,CAAA,aAAA,IAAA,CAAA,CAAA,CAAA,CAAA;IACA,CAAA,EAAA,CAAA,CAAA;;IAEA;IACA,IAAA,WAAA,GAAA,aAAA,CAAA,aAAA,KACA,IAAA,CAAA,KAAA;MACA,WAAA,EAAA,WAAA;MACA,YAAA,EAAA,IAAA,CAAA;IAAA,EACA;IAEA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,WAAA,CAAA;;IAEA;IACA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,QAAA,CAAA,UAAA,KAAA,EAAA;MACA,IAAA,KAAA,EAAA;QACA,MAAA,CAAA,cAAA,GAAA,IAAA;QACA,IAAA,OAAA,GAAA,MAAA,CAAA,KAAA,CAAA,EAAA,GAAA,MAAA,CAAA,GAAA,CAAA,IAAA,GAAA,MAAA,CAAA,GAAA,CAAA,GAAA;QACA,IAAA,MAAA,GAAA,MAAA,CAAA,KAAA,CAAA,EAAA,GAAA,KAAA,GAAA,MAAA;QAEA,UAAA,CAAA,OAAA,EAAA,WAAA,EAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;UACA,IAAA,GAAA,CAAA,OAAA,EAAA;YACA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,OAAA,CAAA;YACA,MAAA,CAAA,KAAA,CAAA,IAAA,CAAA;UACA,CAAA,MAAA;YACA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,OAAA,CAAA;UACA;QACA,CAAA,CAAA,CAAA,OAAA,CAAA,YAAA;UACA,MAAA,CAAA,cAAA,GAAA,KAAA;QACA,CAAA,CAAA;MACA;IACA,CAAA,CAAA;EACA;AACA,CAAA,GAAA,qBAAA", "sourcesContent": ["<template>\r\n  <a-spin :spinning=\"confirmLoading\">\r\n    <j-form-container :disabled=\"formDisabled\">\r\n      <a-form-model ref=\"form\" :model=\"model\" :rules=\"validatorRules\" slot=\"detail\">\r\n        <!-- 产品选择卡片 -->\r\n        <a-card class=\"custom-card product-card\" :bordered=\"false\">\r\n          <div class=\"card-title\">\r\n            <a-icon type=\"shop\" />\r\n            <span>选择产品</span>\r\n          </div>\r\n          <a-form-model-item prop=\"productId\">\r\n            <!-- 产品搜索选择器 -->\r\n            <div class=\"product-search-wrapper\">\r\n              <a-auto-complete\r\n                v-model=\"model.productName\"\r\n                placeholder=\"请输入产品名称搜索\"\r\n                :dropdownMatchSelectWidth=\"false\"\r\n                @search=\"handleProductSearch\"\r\n                @select=\"handleProductSelect\"\r\n                class=\"custom-select\"\r\n                size=\"large\"\r\n                :filterOption=\"false\"\r\n                :defaultActiveFirstOption=\"false\"\r\n              >\r\n                <template slot=\"dataSource\">\r\n                  <a-select-option v-if=\"loadingProducts\" disabled key=\"loading\">\r\n                    <a-icon type=\"loading\" spin />\r\n                    加载中...\r\n                  </a-select-option>\r\n                  <a-select-option\r\n                    v-for=\"product in filteredProducts\"\r\n                    :key=\"product.id\"\r\n                    :value=\"product.id\"\r\n                  >\r\n                    {{ product.name }}\r\n                  </a-select-option>\r\n                </template>\r\n                <a-input>\r\n                  <a-icon slot=\"suffix\" type=\"search\" @click=\"loadProducts\" />\r\n                </a-input>\r\n              </a-auto-complete>\r\n            </div>\r\n            \r\n            <!-- 隐藏的产品ID字段，用于表单提交 -->\r\n            <a-input type=\"hidden\" v-model=\"model.productId\" />\r\n          </a-form-model-item>\r\n          \r\n          <div v-if=\"model.productId && model.productName\" class=\"selected-product-info\">\r\n            <a-alert type=\"success\" show-icon>\r\n              <template slot=\"message\">\r\n                已选择产品: <strong>{{ model.productName }}</strong>\r\n              </template>\r\n            </a-alert>\r\n          </div>\r\n        </a-card>\r\n\r\n        <!-- 物料清单名称卡片 -->\r\n        <a-card class=\"custom-card name-card\" :bordered=\"false\">\r\n          <div class=\"card-title\">\r\n            <a-icon type=\"edit\" />\r\n            <span>物料清单名称</span>\r\n          </div>\r\n          <a-form-model-item prop=\"materialBillName\">\r\n            <a-input\r\n              v-model=\"model.materialBillName\"\r\n              placeholder=\"请输入物料清单名称\"\r\n              size=\"large\"\r\n              :maxLength=\"50\"\r\n              show-count\r\n            >\r\n              <a-icon slot=\"prefix\" type=\"file-text\" />\r\n            </a-input>\r\n          </a-form-model-item>\r\n        </a-card>\r\n\r\n        <!-- 物料清单卡片 -->\r\n        <a-card class=\"custom-card material-card\" :bordered=\"false\">\r\n          <div class=\"card-header\">\r\n            <div class=\"card-title\">\r\n              <a-icon type=\"database\" />\r\n              <span>物料清单</span>\r\n            </div>\r\n            <a-badge :count=\"materialList.length\" :overflowCount=\"99\" class=\"material-count-badge\" />\r\n          </div>\r\n\r\n          <div class=\"material-search-container\">\r\n            <a-input-search\r\n              placeholder=\"输入物料名称或单位进行模糊搜索\"\r\n              v-model=\"searchMaterialName\"\r\n              @search=\"searchMaterial\"\r\n              enter-button\r\n              class=\"search-input\"\r\n              size=\"large\"\r\n            >\r\n              <a-icon slot=\"prefix\" type=\"search\" />\r\n            </a-input-search>\r\n            \r\n            <a-tooltip title=\"从物料库中添加物料\">\r\n              <a-button type=\"primary\" icon=\"plus\" @click=\"searchMaterial\" size=\"large\">\r\n                添加物料\r\n              </a-button>\r\n            </a-tooltip>\r\n          </div>\r\n\r\n          <div class=\"material-list-container\">\r\n            <a-empty v-if=\"materialList.length === 0\" description=\"暂无物料，请搜索并添加\">\r\n              <a-button type=\"primary\" @click=\"searchMaterial\">添加物料</a-button>\r\n            </a-empty>\r\n            <a-table\r\n              v-else\r\n              :columns=\"materialColumns\"\r\n              :dataSource=\"materialList\"\r\n              :pagination=\"false\"\r\n              :rowKey=\"record => record.id || record.tempId\"\r\n              size=\"middle\"\r\n              :scroll=\"{ x: 800 }\"\r\n              :bordered=\"true\"\r\n              :rowClassName=\"'material-table-row'\"\r\n            >\r\n              <!-- 物料名称列 -->\r\n              <template slot=\"materialName\" slot-scope=\"text, record, index\">\r\n                <div class=\"material-name-cell\">\r\n                  <template v-if=\"record.tempId\">\r\n                    <a-input\r\n                      v-model=\"materialList[index].materialName\"\r\n                      placeholder=\"请输入物料名称\"\r\n                      @change=\"updateMaterialJson\"\r\n                    />\r\n                  </template>\r\n                  <template v-else>\r\n                    <span class=\"material-name\">{{ text }}</span>\r\n                    <a-tag v-if=\"record.imSpecs\" color=\"blue\" class=\"material-spec-tag\">{{ record.imSpecs }}</a-tag>\r\n                  </template>\r\n                </div>\r\n              </template>\r\n\r\n              <!-- 单位列 -->\r\n              <template slot=\"materialUnit\" slot-scope=\"text, record, index\">\r\n                <div class=\"unit-cell\">\r\n                  <template v-if=\"record.tempId\">\r\n                    <a-input\r\n                      v-model=\"materialList[index].materialUnit\"\r\n                      placeholder=\"请输入单位\"\r\n                      @change=\"updateMaterialJson\"\r\n                    />\r\n                  </template>\r\n                  <template v-else>\r\n                    <a-tag color=\"cyan\">{{ text || '个' }}</a-tag>\r\n                  </template>\r\n                </div>\r\n              </template>\r\n\r\n              <!-- 数量列 -->\r\n              <template slot=\"materialCount\" slot-scope=\"text, record, index\">\r\n                <div class=\"count-cell\">\r\n                  <a-input-number\r\n                    v-model=\"materialList[index].materialCount\"\r\n                    :min=\"1\"\r\n                    @change=\"value => handleCountChange(value, index)\"\r\n                    class=\"count-input\"\r\n                    size=\"default\"\r\n                  />\r\n                  <span class=\"unit-text\">{{ record.materialSpecifications || '个' }}</span>\r\n                </div>\r\n              </template>\r\n\r\n              <!-- 操作列 -->\r\n              <template slot=\"action\" slot-scope=\"text, record, index\">\r\n                <div class=\"action-cell\">\r\n                  <a-button type=\"danger\" size=\"small\" @click=\"removeMaterial(index)\" class=\"delete-btn\">\r\n                    <a-icon type=\"delete\" />\r\n                  </a-button>\r\n                </div>\r\n              </template>\r\n            </a-table>\r\n            \r\n            <div v-if=\"materialList.length > 0\" class=\"material-summary\">\r\n              <div class=\"summary-item\">\r\n                <span class=\"summary-label\">总物料数:</span>\r\n                <span class=\"summary-value\">{{ materialList.length }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </a-card>\r\n\r\n        <!-- 物料清单设置卡片 -->\r\n        <a-card class=\"custom-card status-card\" :bordered=\"false\">\r\n          <div class=\"card-title\">\r\n            <a-icon type=\"setting\" />\r\n            <span>物料清单设置</span>\r\n          </div>\r\n          <a-form-model-item label=\"物料清单状态\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"materialType\">\r\n            <div class=\"status-switch-container\">\r\n              <a-switch\r\n                checkedChildren=\"启用\"\r\n                unCheckedChildren=\"禁用\"\r\n                @change=\"onChose\"\r\n                v-model=\"visibleCheck\"\r\n                class=\"custom-switch\"\r\n                size=\"large\"\r\n              />\r\n              <span :class=\"['status-text', visibleCheck ? 'status-enabled' : 'status-disabled']\">\r\n                {{ visibleCheck ? '清单已启用' : '清单已禁用' }}\r\n              </span>\r\n            </div>\r\n          </a-form-model-item>\r\n\r\n          <a-form-model-item label=\"库存用完处理策略\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"stockEmptyStrategy\">\r\n            <a-checkbox-group v-model=\"stockEmptyStrategyArray\" class=\"strategy-checkbox-group\">\r\n              <a-checkbox value=\"1\" class=\"strategy-checkbox\" style=\"margin-left: 7px;\">\r\n                <div class=\"checkbox-content\">\r\n                  <div class=\"checkbox-title\">自动禁用此物料清单</div>\r\n                  <div class=\"checkbox-desc\">当前采购批次库存用完时，自动禁用此物料清单</div>\r\n                </div>\r\n              </a-checkbox>\r\n              <a-checkbox value=\"2\" class=\"strategy-checkbox\">\r\n                <div class=\"checkbox-content\">\r\n                  <div class=\"checkbox-title\">自动切换为同成本物料</div>\r\n                  <div class=\"checkbox-desc\">当前采购批次库存用完时，自动切换为同成本的其他物料</div>\r\n                </div>\r\n              </a-checkbox>\r\n            </a-checkbox-group>\r\n          </a-form-model-item>\r\n        </a-card>\r\n        \r\n        <!-- 提交按钮区域 -->\r\n        <div class=\"form-actions\" v-if=\"!formDisabled\">\r\n          <a-button @click=\"resetForm\" :disabled=\"confirmLoading\">\r\n            <a-icon type=\"undo\" />重置\r\n          </a-button>\r\n          <a-button type=\"primary\" @click=\"submitForm\" :loading=\"confirmLoading\" :disabled=\"materialList.length === 0\">\r\n            <a-icon type=\"check\" />保存\r\n          </a-button>\r\n        </div>\r\n      </a-form-model>\r\n    </j-form-container>\r\n\r\n    <!-- 物料搜索弹窗 -->\r\n    <a-modal\r\n      title=\"搜索物料库存\"\r\n      :visible=\"inventoryModalVisible\"\r\n      :width=\"800\"\r\n      @cancel=\"closeInventoryModal\"\r\n      :footer=\"null\"\r\n      :destroyOnClose=\"true\"\r\n      :maskClosable=\"false\"\r\n      class=\"inventory-modal\"\r\n    >\r\n      <div class=\"modal-header\">\r\n        <a-icon type=\"database\" theme=\"filled\" />\r\n        <span>添加物料到清单</span>\r\n      </div>\r\n      \r\n      <div class=\"inventory-search-container\">\r\n        <a-input-search\r\n          placeholder=\"输入物料名称或单位进行模糊搜索\"\r\n          v-model=\"inventorySearchName\"\r\n          @search=\"searchInventory\"\r\n          enter-button\r\n          class=\"search-input\"\r\n          size=\"large\"\r\n          @pressEnter=\"searchInventory\"\r\n        >\r\n          <a-icon slot=\"prefix\" type=\"search\" />\r\n        </a-input-search>\r\n      </div>\r\n\r\n      <a-spin :spinning=\"loadingInventory\">\r\n        <div class=\"inventory-list-container\">\r\n          <a-empty v-if=\"inventoryList.length === 0 && !loadingInventory\" description=\"未找到物料，请尝试其他关键词\" />\r\n          <a-table\r\n            v-else\r\n            :columns=\"inventoryColumns\"\r\n            :dataSource=\"inventoryList\"\r\n            :pagination=\"{ pageSize: 5, showQuickJumper: true, showSizeChanger: true }\"\r\n            :rowKey=\"record => record.id\"\r\n            size=\"middle\"\r\n            :bordered=\"true\"\r\n            :rowClassName=\"'inventory-table-row'\"\r\n          >\r\n            <template slot=\"imItemName\" slot-scope=\"text\">\r\n              <span class=\"inventory-name\">{{ text }}</span>\r\n            </template>\r\n\r\n            <template slot=\"imUnit\" slot-scope=\"text\">\r\n              <a-tag color=\"blue\">{{ text || '个' }}</a-tag>\r\n            </template>\r\n\r\n            <template slot=\"imQuantity\" slot-scope=\"text\">\r\n              <span class=\"quantity-value\">{{ text }}</span>\r\n            </template>\r\n\r\n            <template slot=\"purchaseBatch\" slot-scope=\"text\">\r\n              <a-tag color=\"green\">{{ text || '批次-001' }}</a-tag>\r\n            </template>\r\n\r\n            <template slot=\"purchaseTime\" slot-scope=\"text\">\r\n              <span class=\"purchase-time\">{{ text || '2025-01-01' }}</span>\r\n            </template>\r\n\r\n            <template slot=\"purchaseUnitCost\" slot-scope=\"text\">\r\n              <span class=\"unit-cost\">¥{{ text || '0.00' }}</span>\r\n            </template>\r\n\r\n            <template slot=\"action\" slot-scope=\"text, record\">\r\n              <a-button type=\"primary\" size=\"small\" @click=\"selectInventoryItem(record)\" class=\"select-btn\">\r\n                <a-icon type=\"plus\" />添加\r\n              </a-button>\r\n            </template>\r\n          </a-table>\r\n        </div>\r\n      </a-spin>\r\n      \r\n      <div class=\"modal-footer\">\r\n        <a-button @click=\"closeInventoryModal\">关闭</a-button>\r\n      </div>\r\n    </a-modal>\r\n  </a-spin>\r\n</template>\r\n\r\n<script>\r\nimport { httpAction, getAction } from '@/api/manage'\r\nimport { validateDuplicateValue } from '@/utils/util'\r\nimport moment from 'moment'\r\nexport default {\r\n  name: 'MaterialBillForm',\r\n  components: {\r\n  },\r\n  props: {\r\n    //表单禁用\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false,\r\n      required: false\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      products: [],\r\n      filteredProducts: [], // 添加过滤后的产品列表\r\n      loadingProducts: false,\r\n      visibleCheck: true,\r\n      materialList: [],\r\n      searchMaterialName: '',\r\n      inventoryModalVisible: false,\r\n      inventorySearchName: '',\r\n      inventoryList: [],\r\n      loadingInventory: false,\r\n      tempIdCounter: 0,\r\n      model: {\r\n        productId: '',\r\n        productName: '',\r\n        materialBillName: '',\r\n        materialType: 0,\r\n        materialJson: '',\r\n        materialApprovalStatus: '',\r\n        stockEmptyStrategy: '', // 存储逗号分隔的策略值\r\n        imItemPrice : 0\r\n      },\r\n      stockEmptyStrategyArray: [], // 用于多选框的数组\r\n      materialColumns: [\r\n        {\r\n          title: '物料名称',\r\n          dataIndex: 'materialName',\r\n          key: 'materialName',\r\n          scopedSlots: { customRender: 'materialName' },\r\n          width: 220,\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '单位',\r\n          dataIndex: 'materialUnit',\r\n          key: 'materialUnit',\r\n          scopedSlots: { customRender: 'materialUnit' },\r\n          width: 120\r\n        },\r\n        {\r\n          title: '数量',\r\n          key: 'materialCount',\r\n          width: 150,\r\n          scopedSlots: { customRender: 'materialCount' }\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: 80,\r\n          fixed: 'right',\r\n          scopedSlots: { customRender: 'action' }\r\n        }\r\n      ],\r\n      inventoryColumns: [\r\n        {\r\n          title: '物料名称',\r\n          dataIndex: 'imItemName',\r\n          key: 'imItemName',\r\n          ellipsis: true,\r\n          scopedSlots: { customRender: 'imItemName' }\r\n        },\r\n        {\r\n          title: '单位',\r\n          dataIndex: 'imUnit',\r\n          key: 'imUnit',\r\n          scopedSlots: { customRender: 'imUnit' }\r\n        },\r\n        {\r\n          title: '库存数量',\r\n          dataIndex: 'imInventoryQuantity',\r\n          key: 'imInventoryQuantity',\r\n          scopedSlots: { customRender: 'imQuantity' }\r\n        },\r\n        {\r\n          title: '采购批次',\r\n          dataIndex: 'purchaseBatch',\r\n          key: 'purchaseBatch',\r\n          scopedSlots: { customRender: 'purchaseBatch' }\r\n        },\r\n        {\r\n          title: '采购时间',\r\n          dataIndex: 'purchaseTime',\r\n          key: 'purchaseTime',\r\n          scopedSlots: { customRender: 'purchaseTime' }\r\n        },\r\n        {\r\n          title: '采购单位成本',\r\n          dataIndex: 'purchaseUnitCost',\r\n          key: 'purchaseUnitCost',\r\n          scopedSlots: { customRender: 'purchaseUnitCost' }\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: 100,\r\n          fixed: 'right',\r\n          scopedSlots: { customRender: 'action' }\r\n        }\r\n      ],\r\n      labelCol: {\r\n        xs: { span: 24 },\r\n        sm: { span: 5 },\r\n      },\r\n      wrapperCol: {\r\n        xs: { span: 24 },\r\n        sm: { span: 16 },\r\n      },\r\n      confirmLoading: false,\r\n      validatorRules: {\r\n        productId: [\r\n          { required: true, message: '请选择产品' }\r\n        ],\r\n        materialBillName: [\r\n          { required: true, message: '请输入物料清单名称' },\r\n          { max: 50, message: '物料清单名称不能超过50个字符' }\r\n        ]\r\n      },\r\n      url: {\r\n        add: \"/admin/materialBill/add\",\r\n        edit: \"/admin/materialBill/edit\",\r\n        queryById: \"/admin/materialBill/queryById\",\r\n        inventoryList: \"/admin/inventoryManagement/list\",\r\n        searchMaterials: \"/admin/inventoryManagement/searchMaterials\",\r\n        queryProductById: \"/sys/sysDepart/queryProductById\"\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadProducts()\r\n    // 处理路由参数\r\n    const routeProductId = this.$route.query.productId\r\n    if (routeProductId) {\r\n      this.model.productId = routeProductId\r\n      this.handleProductChange(routeProductId)\r\n    }\r\n  },\r\n  watch: {\r\n    materialList: {\r\n      handler(newVal) {\r\n        // 当物料列表变化时，更新materialJson\r\n        this.updateMaterialJson()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  computed: {\r\n    formDisabled(){\r\n      return this.disabled\r\n    },\r\n  },\r\n  watch: {\r\n    // 监听多选框数组变化，同步到字符串字段\r\n    stockEmptyStrategyArray: {\r\n      handler(newVal) {\r\n        this.model.stockEmptyStrategy = newVal.join(',');\r\n      },\r\n      deep: true\r\n    },\r\n    // 监听字符串字段变化，同步到多选框数组\r\n    'model.stockEmptyStrategy': {\r\n      handler(newVal) {\r\n        if (newVal && typeof newVal === 'string') {\r\n          this.stockEmptyStrategyArray = newVal.split(',').filter(item => item.trim());\r\n        } else {\r\n          this.stockEmptyStrategyArray = [];\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  created () {\r\n    //备份model原始值\r\n    this.modelDefault = JSON.parse(JSON.stringify(this.model));\r\n    // 设置默认值为启用\r\n    if (this.model.materialType === undefined) {\r\n      this.model.materialType = 0;\r\n    }\r\n  },\r\n  methods: {\r\n    // 重置表单\r\n    resetForm() {\r\n      this.model = JSON.parse(JSON.stringify(this.modelDefault));\r\n      this.materialList = [];\r\n      this.visibleCheck = true;\r\n      if (this.$refs.form) {\r\n        this.$refs.form.resetFields();\r\n      }\r\n    },\r\n    \r\n    // 更新物料列表\r\n    updateMaterialList() {\r\n      try {\r\n        this.model.materialList = this.materialList.map((item, index) => ({\r\n          id: item.id || item.tempId,\r\n          materialName: item.materialName || '',\r\n          materialUnit: item.materialUnit || '',\r\n          materialCount: String(item.materialCount || 1), // 改为 materialCount\r\n          imItemPrice: item.imItemPrice || 0,\r\n          companyId: item.companyId || '',\r\n          createBy: item.createBy || '',\r\n          createTime: item.createTime || '',\r\n          materialBillId: this.model.id || '',\r\n          productionOrderId: item.productionOrderId || '',\r\n          sysOrgCode: item.sysOrgCode || '',\r\n          updateBy: item.updateBy || '',\r\n          updateTime: item.updateTime || '',\r\n        }));\r\n\r\n        console.log('更新后的 materialList:', this.model.materialList);\r\n      } catch (error) {\r\n        console.error('更新 materialList 失败:', error);\r\n      }\r\n    },\r\n    \r\n    onChose(checked) {\r\n      if (checked) {\r\n        this.model.materialType = 0;  // 0表示启用\r\n        this.visibleCheck = true;\r\n      } else {\r\n        this.model.materialType = 1;  // 1表示禁用\r\n        this.visibleCheck = false;\r\n      }\r\n    },\r\n\r\n    async loadProducts() {\r\n      try {\r\n        this.loadingProducts = true\r\n        const res = await getAction('/sys/sysDepart/queryProductsByCurrentEnterprise', {})\r\n        if (res.success) {\r\n          this.products = (res.result || [])\r\n            .filter(p => p.id && p.productName)\r\n            .map(p => ({\r\n              id: p.id,\r\n              name: p.productName\r\n            }))\r\n          \r\n          // 初始设置过滤后的产品列表与产品列表相同\r\n          this.filteredProducts = [...this.products]\r\n          \r\n          return this.products\r\n        } else {\r\n          this.$message.error('产品加载失败：' + (res.message || '请求异常'))\r\n          this.products = []\r\n          this.filteredProducts = []\r\n          return []\r\n        }\r\n      } catch (error) {\r\n        console.error('产品加载失败:', error)\r\n        this.$message.error('产品加载失败：' + (error.message || '未知错误'))\r\n        this.products = []\r\n        this.filteredProducts = []\r\n        return []\r\n      } finally {\r\n        this.loadingProducts = false\r\n      }\r\n    },\r\n\r\n    add () {\r\n      this.edit(this.modelDefault);\r\n    },\r\n\r\n    // 产品选择变更\r\n    handleProductChange(productId) {\r\n      if (!productId) {\r\n        this.model.productId = '';\r\n        this.model.productName = '';\r\n        return;\r\n      }\r\n      \r\n      this.model.productId = productId;\r\n      \r\n      // 查找选中的产品名称并设置\r\n      const selectedProduct = this.products.find(p => p.id === productId);\r\n      if (selectedProduct) {\r\n        this.model.productName = selectedProduct.name;\r\n        console.log('产品已选择:', selectedProduct.name);\r\n      } else {\r\n        // 如果在本地列表中找不到，尝试从服务器获取\r\n        getAction(this.url.queryProductById, { id: productId })\r\n          .then(res => {\r\n            if (res.success && res.result) {\r\n              this.model.productName = res.result.productName || res.result.name;\r\n              console.log('从服务器获取产品名称:', this.model.productName);\r\n            }\r\n          })\r\n          .catch(err => {\r\n            console.error('获取产品详情失败:', err);\r\n          });\r\n      }\r\n    },\r\n\r\n    // 处理产品搜索\r\n    handleProductSearch(searchText) {\r\n      this.filteredProducts = this.products.filter(product =>\r\n        product.name.toLowerCase().includes(searchText.toLowerCase())\r\n      );\r\n    },\r\n\r\n    // 处理产品选择\r\n    handleProductSelect(value) {\r\n      this.model.productId = value;\r\n      this.model.productName = this.products.find(p => p.id === value).name;\r\n      this.filteredProducts = []; // 清空过滤列表\r\n    },\r\n\r\n    // 打开物料搜索弹窗\r\n    searchMaterial() {\r\n      this.inventoryModalVisible = true\r\n      this.inventorySearchName = this.searchMaterialName\r\n      this.searchInventory()\r\n    },\r\n\r\n    // 关闭物料搜索弹窗\r\n    closeInventoryModal() {\r\n      this.inventoryModalVisible = false\r\n      this.inventoryList = []\r\n    },\r\n\r\n    // 搜索库存物料\r\n    async searchInventory() {\r\n      if (!this.inventorySearchName.trim()) {\r\n        this.$message.warning('请输入搜索关键词')\r\n        return\r\n      }\r\n\r\n      try {\r\n        this.loadingInventory = true\r\n        // 使用新的模糊搜索接口\r\n        const res = await getAction(this.url.searchMaterials, {\r\n          keyword: this.inventorySearchName\r\n        })\r\n\r\n        if (res.success) {\r\n          this.inventoryList = res.result || []\r\n          if (this.inventoryList.length === 0) {\r\n            this.$message.info('未找到匹配的物料，请尝试其他关键词')\r\n          }\r\n        } else {\r\n          this.$message.error('库存查询失败：' + (res.message || '请求异常'))\r\n          this.inventoryList = []\r\n        }\r\n      } catch (error) {\r\n        console.error('库存查询失败:', error)\r\n        this.$message.error('库存查询失败：' + (error.message || '未知错误'))\r\n        this.inventoryList = []\r\n      } finally {\r\n        this.loadingInventory = false\r\n      }\r\n    },\r\n\r\n    // 选择库存物料\r\n    // 选择库存物料\r\n    selectInventoryItem(item) {\r\n      const existingIndex = this.materialList.findIndex(m => m.id === item.id);\r\n\r\n      if (existingIndex >= 0) {\r\n        this.$message.warning('该物料已添加到清单中');\r\n        return;\r\n      }\r\n\r\n      const newMaterial = {\r\n        id: item.id,\r\n        materialName: item.imItemName,\r\n        materialSpecifications: item.imItemUnit || '个', // 将单位存储到规格字段\r\n        materialCount: 1,\r\n        imItemPrice: item.imItemPrice || 0, // 新增：传递物料价格\r\n        companyId: '', // 默认公司 ID\r\n        createBy: this.model.createBy || '', // 创建人\r\n        createTime: moment().format('YYYY-MM-DD HH:mm:ss'), // 创建时间\r\n        materialBillId: this.model.id || '', // 物料清单 ID\r\n        productionOrderId: '', // 生产订单 ID\r\n        sysOrgCode: this.model.sysOrgCode || '', // 所属部门\r\n        updateBy: '', // 更新人\r\n        updateTime: '', // 更新时间\r\n      };\r\n\r\n      this.materialList.push(newMaterial);\r\n      this.updateMaterialList();\r\n      this.$message.success(`已添加物料: ${item.imItemName}`);\r\n      this.closeInventoryModal();\r\n    },\r\n\r\n    // 手动添加物料\r\n    // 手动添加物料\r\n    addMaterial() {\r\n      const tempId = 'temp_' + (++this.tempIdCounter);\r\n      this.materialList.push({\r\n        tempId,\r\n        materialName: '', // 空值\r\n        materialUnit: '', // 空值\r\n        materialCount: 1, // 默认数量\r\n        imItemPrice: 0, // 新增：默认物料价格为 0\r\n        companyId: '', // 默认公司 ID\r\n        createBy: '', // 默认创建人\r\n        createTime: moment().format('YYYY-MM-DD HH:mm:ss'), // 默认创建时间\r\n        materialBillId: this.model.id || '', // 物料清单 ID\r\n        productionOrderId: '', // 默认生产订单 ID\r\n        sysOrgCode: '', // 默认所属部门\r\n        updateBy: '', // 默认更新人\r\n        updateTime: '', // 默认更新时间\r\n      });\r\n\r\n      this.updateMaterialList();\r\n    },\r\n\r\n    // 删除物料\r\n    removeMaterial(index) {\r\n      this.materialList.splice(index, 1); // 从数组中移除\r\n      this.updateMaterialList(); // 更新 materialList\r\n    },\r\n\r\n    // 更新数量\r\n    handleCountChange(value, index) {\r\n      if (!this.materialList[index]) return;\r\n      this.materialList[index].materialCount = value; // 更新数量\r\n      this.updateMaterialList(); // 更新 materialList\r\n    },\r\n\r\n    // 更新materialJson\r\n    updateMaterialJson() {\r\n      try {\r\n        const jsonData = this.materialList.map(item => ({\r\n          materialName: item.materialName,\r\n          materialUnit: item.materialUnit,\r\n          materialCount: String(item.materialCount),\r\n          imItemPrice: item.imItemPrice || 0, // 新增：传递物料价格\r\n          id: item.id || item.tempId,\r\n          // 保留其他可能需要的字段\r\n          ...(item.originalData ? { originalData: item.originalData } : {})\r\n        }))\r\n\r\n        this.model.materialJson = JSON.stringify(jsonData)\r\n      } catch (error) {\r\n        console.error('生成物料JSON失败:', error)\r\n      }\r\n    },\r\n    // 从JSON恢复物料列表\r\n    restoreMaterialListFromJson() {\r\n      try {\r\n        if (!Array.isArray(this.model.materialList)) {\r\n          this.materialList = [];\r\n          return;\r\n        }\r\n\r\n        this.materialList = this.model.materialList.map(item => ({\r\n          id: item.id || ('temp_' + (++this.tempIdCounter)),\r\n          materialName: item.materialName || '',\r\n          materialSpecifications: item.materialSpecifications || '',\r\n          materialCount: parseInt(item.materialCount) || 1, // 改为从 materialCount 获取\r\n          imItemPrice: item.imItemPrice || 0,\r\n          companyId: item.companyId || '',\r\n          createBy: item.createBy || '',\r\n          createTime: item.createTime || '',\r\n          materialBillId: item.materialBillId || '',\r\n          productionOrderId: item.productionOrderId || '',\r\n          sysOrgCode: item.sysOrgCode || '',\r\n          updateBy: item.updateBy || '',\r\n          updateTime: item.updateTime || '',\r\n        }));\r\n      } catch (error) {\r\n        console.error('恢复物料列表失败:', error);\r\n        this.materialList = [];\r\n      }\r\n    },\r\n\r\n    edit (record) {\r\n      this.model = Object.assign({}, record);\r\n      this.visibleCheck = this.model.materialType === 0 || this.model.materialType === \"0\";\r\n      this.visible = true; // 设置visible属性为true\r\n      \r\n      // 打印当前编辑记录，方便排查问题\r\n      console.log('编辑记录:', record);\r\n      \r\n      // 确保productId正确设置，防止undefined或null值\r\n      if (!this.model.productId) {\r\n        this.model.productId = '';\r\n        this.model.productName = '';\r\n      }\r\n      \r\n      // 当存在productId但没有productName时，从加载的产品列表中查找产品名称\r\n      if (this.model.productId && !this.model.productName) {\r\n        // 如果产品列表已加载\r\n        if (this.products && this.products.length > 0) {\r\n          const selectedProduct = this.products.find(p => p.id === this.model.productId);\r\n          if (selectedProduct) {\r\n            this.model.productName = selectedProduct.name;\r\n            console.log('从产品列表中获取到产品名称:', this.model.productName);\r\n          } else {\r\n            // 如果在本地找不到，从服务器查询\r\n            this.queryProductNameById(this.model.productId);\r\n          }\r\n        } else {\r\n          // 如果产品列表尚未加载，先加载产品列表\r\n          this.loadProducts().then(() => {\r\n            const selectedProduct = this.products.find(p => p.id === this.model.productId);\r\n            if (selectedProduct) {\r\n              this.model.productName = selectedProduct.name;\r\n              console.log('异步加载产品名称成功:', this.model.productName);\r\n            } else {\r\n              // 如果仍然找不到，尝试单独查询这个产品\r\n              this.queryProductNameById(this.model.productId);\r\n            }\r\n          });\r\n        }\r\n      }\r\n      \r\n      // 恢复物料列表\r\n      if (Array.isArray(this.model.materialList) && this.model.materialList.length > 0) {\r\n        // 直接使用后端返回的物料列表\r\n        this.materialList = this.model.materialList.map(item => ({\r\n          id: item.id,\r\n          materialName: item.materialName || '',\r\n          materialSpecifications: item.materialSpecifications || '',\r\n          materialCount: parseInt(item.materialCount) || 1,\r\n          imItemPrice: item.imItemPrice || 0,\r\n          companyId: item.companyId || '',\r\n          createBy: item.createBy || '',\r\n          createTime: item.createTime || '',\r\n          materialBillId: item.materialBillId || '',\r\n          productionOrderId: item.productionOrderId || '',\r\n          sysOrgCode: item.sysOrgCode || '',\r\n          updateBy: item.updateBy || '',\r\n          updateTime: item.updateTime || '',\r\n        }));\r\n      } else {\r\n        // 如果没有物料列表数据，尝试从materialJson解析\r\n        this.restoreMaterialListFromJson();\r\n      }\r\n      \r\n      console.log('加载的物料列表:', this.materialList);\r\n    },\r\n    \r\n    // 通过产品ID查询产品名称\r\n    queryProductNameById(productId) {\r\n      if (!productId) return;\r\n      \r\n      getAction(this.url.queryProductById, { id: productId })\r\n        .then(res => {\r\n          if (res.success && res.result) {\r\n            this.model.productName = res.result.productName || res.result.name;\r\n            console.log('通过API查询获取产品名称:', this.model.productName);\r\n          } else {\r\n            console.warn('查询产品详情返回空数据');\r\n          }\r\n        })\r\n        .catch(err => {\r\n          console.error('查询产品详情失败:', err);\r\n        });\r\n    },\r\n\r\n    submitForm() {\r\n      const invalidItem = this.materialList.find(item =>\r\n        !moment(item.createTime, 'YYYY-MM-DD HH:mm:ss', true).isValid()\r\n      );\r\n\r\n      if (invalidItem) {\r\n        this.$message.error(`物料 ${invalidItem.materialName} 时间格式错误`);\r\n        return;\r\n      }\r\n\r\n      // 检查是否有物料\r\n      if (this.materialList.length === 0) {\r\n        this.$message.warning('请至少添加一种物料');\r\n        return;\r\n      }\r\n\r\n      // 更新最终的 materialList\r\n      this.updateMaterialList();\r\n\r\n      // 提取 materialList 中的 imItemPrice\r\n      const imItemPrice = this.materialList.reduce((total, item) => {\r\n        return total + (item.imItemPrice || 0) * (item.materialCount || 1); // 改为 materialCount\r\n      }, 0);\r\n\r\n      // 构造最终的请求参数\r\n      const requestData = {\r\n        ...this.model,\r\n        imItemPrice: imItemPrice,\r\n        materialList: this.materialList,\r\n      };\r\n\r\n      console.log('最终请求参数:', requestData);\r\n\r\n      // 验证表单\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          this.confirmLoading = true;\r\n          const httpurl = this.model.id ? this.url.edit : this.url.add;\r\n          const method = this.model.id ? 'put' : 'post';\r\n\r\n          httpAction(httpurl, requestData, method).then(res => {\r\n            if (res.success) {\r\n              this.$message.success(res.message);\r\n              this.$emit('ok');\r\n            } else {\r\n              this.$message.warning(res.message);\r\n            }\r\n          }).finally(() => {\r\n            this.confirmLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n// 变量定义\r\n@primary-color: #1890ff;\r\n@success-color: #52c41a;\r\n@warning-color: #faad14;\r\n@error-color: #f5222d;\r\n@border-radius-base: 8px;\r\n@card-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\r\n@transition-duration: 0.3s;\r\n\r\n.ant-input {\r\n  width: 100%;\r\n  max-width: 180px;\r\n}\r\n\r\n// 自定义卡片样式\r\n.custom-card {\r\n  margin-bottom: 24px;\r\n  border-radius: @border-radius-base;\r\n  box-shadow: @card-shadow;\r\n  overflow: hidden;\r\n  transition: all @transition-duration;\r\n\r\n  &:hover {\r\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\r\n  }\r\n\r\n  &.product-card {\r\n    background: linear-gradient(to right, #f0f7ff, #ffffff);\r\n    border-left: 4px solid #1890ff;\r\n  }\r\n\r\n  &.name-card {\r\n    background: linear-gradient(to right, #f6ffed, #ffffff);\r\n    border-left: 4px solid #52c41a;\r\n  }\r\n\r\n  &.material-card {\r\n    background: linear-gradient(to right, #f0fff0, #ffffff);\r\n    border-left: 4px solid #52c41a;\r\n  }\r\n\r\n  &.status-card {\r\n    background: linear-gradient(to right, #fffbe6, #ffffff);\r\n    border-left: 4px solid #faad14;\r\n  }\r\n}\r\n\r\n// 卡片标题\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  margin-bottom: 20px;\r\n  color: #1890ff;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .anticon {\r\n    margin-right: 10px;\r\n    font-size: 18px;\r\n  }\r\n}\r\n\r\n// 卡片头部\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.material-count-badge {\r\n  margin-right: 16px;\r\n}\r\n\r\n// 选中产品信息\r\n.selected-product-info {\r\n  margin-top: 16px;\r\n}\r\n\r\n// 物料搜索容器\r\n.material-search-container {\r\n  display: flex;\r\n  margin-bottom: 16px;\r\n  gap: 12px;\r\n  align-items: center;\r\n\r\n  .search-input {\r\n    flex: 1;\r\n  }\r\n}\r\n\r\n// 物料列表容器\r\n.material-list-container {\r\n  margin-top: 16px;\r\n  border: 1px solid #f0f0f0;\r\n  border-radius: @border-radius-base;\r\n  padding: 16px;\r\n  background-color: #fafafa;\r\n  \r\n  .count-input {\r\n    width: 90px;\r\n  }\r\n}\r\n\r\n// 物料名称单元格\r\n.material-name-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n\r\n  .material-name {\r\n    font-weight: 500;\r\n  }\r\n\r\n  .material-spec-tag {\r\n    margin-right: 0;\r\n    border-radius: 12px;\r\n  }\r\n}\r\n\r\n// 单位单元格\r\n.unit-cell {\r\n  .ant-tag {\r\n    padding: 0 8px;\r\n    border-radius: 12px;\r\n  }\r\n}\r\n\r\n// 数量单元格\r\n.count-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  \r\n  .unit-text {\r\n    color: rgba(0, 0, 0, 0.45);\r\n  }\r\n}\r\n\r\n// 操作单元格\r\n.action-cell {\r\n  .delete-btn {\r\n    border-radius: 50%;\r\n    width: 28px;\r\n    height: 28px;\r\n    padding: 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n// 物料汇总\r\n.material-summary {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 16px;\r\n  padding-top: 16px;\r\n  border-top: 1px dashed #e8e8e8;\r\n  \r\n  .summary-item {\r\n    margin-left: 24px;\r\n    \r\n    .summary-label {\r\n      color: rgba(0, 0, 0, 0.45);\r\n      margin-right: 8px;\r\n    }\r\n    \r\n    .summary-value {\r\n      font-weight: 600;\r\n      color: @primary-color;\r\n    }\r\n  }\r\n}\r\n\r\n// 状态开关容器\r\n.status-switch-container {\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .status-text {\r\n    margin-left: 16px;\r\n    font-size: 14px;\r\n\r\n    &.status-enabled {\r\n      color: @success-color;\r\n    }\r\n\r\n    &.status-disabled {\r\n      color: @error-color;\r\n    }\r\n  }\r\n}\r\n\r\n// 策略多选框组\r\n.strategy-checkbox-group {\r\n  width: 100%;\r\n\r\n  .strategy-checkbox {\r\n    display: block;\r\n    margin-bottom: 16px;\r\n    padding: 16px;\r\n    border: 1px solid #f0f0f0;\r\n    border-radius: @border-radius-base;\r\n    background-color: #fafafa;\r\n    transition: all @transition-duration;\r\n\r\n    &:hover {\r\n      border-color: @primary-color;\r\n      background-color: #f0f7ff;\r\n    }\r\n\r\n    &.ant-checkbox-wrapper-checked {\r\n      border-color: @primary-color;\r\n      background-color: #e6f7ff;\r\n      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);\r\n    }\r\n\r\n    .checkbox-content {\r\n      margin-left: 8px;\r\n\r\n      .checkbox-title {\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: rgba(0, 0, 0, 0.85);\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .checkbox-desc {\r\n        font-size: 12px;\r\n        color: rgba(0, 0, 0, 0.45);\r\n        line-height: 1.4;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 自定义选择器\r\n.custom-select {\r\n  width: 100%;\r\n}\r\n\r\n// 自定义开关\r\n.custom-switch {\r\n  &.ant-switch-checked {\r\n    background-color: @success-color;\r\n  }\r\n  \r\n  &:not(.ant-switch-checked) {\r\n    background-color: @error-color;\r\n  }\r\n}\r\n\r\n// 表单操作区\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 12px;\r\n  margin-top: 24px;\r\n  \r\n  .ant-btn {\r\n    min-width: 100px;\r\n  }\r\n}\r\n\r\n// 物料库存模态框\r\n.inventory-modal {\r\n  .modal-header {\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    margin-bottom: 20px;\r\n    color: @primary-color;\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    .anticon {\r\n      margin-right: 10px;\r\n      font-size: 20px;\r\n    }\r\n  }\r\n  \r\n  .inventory-search-container {\r\n    margin-bottom: 20px;\r\n    \r\n    .search-input {\r\n      width: 100%;\r\n    }\r\n  }\r\n  \r\n  .inventory-list-container {\r\n    max-height: 400px;\r\n    overflow-y: auto;\r\n    margin-bottom: 20px;\r\n    border: 1px solid #f0f0f0;\r\n    border-radius: @border-radius-base;\r\n    padding: 16px;\r\n    background-color: #fafafa;\r\n  }\r\n  \r\n  .inventory-name {\r\n    font-weight: 500;\r\n  }\r\n  \r\n  .quantity-value {\r\n    font-weight: 600;\r\n    color: @primary-color;\r\n  }\r\n  \r\n  .select-btn {\r\n    border-radius: 16px;\r\n  }\r\n  \r\n  .modal-footer {\r\n    margin-top: 16px;\r\n    text-align: right;\r\n  }\r\n}\r\n\r\n// 表格行样式\r\n.material-table-row {\r\n  transition: all @transition-duration;\r\n  \r\n  &:hover {\r\n    background-color: #f0f7ff;\r\n  }\r\n}\r\n\r\n.inventory-table-row {\r\n  transition: all @transition-duration;\r\n  \r\n  &:hover {\r\n    background-color: #f0f7ff;\r\n  }\r\n}\r\n\r\n// 库存搜索容器\r\n.inventory-search-container {\r\n  margin-bottom: 16px;\r\n  \r\n  .search-input {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.product-search-wrapper {\r\n  position: relative;\r\n  \r\n  .ant-select-auto-complete {\r\n    width: 100%;\r\n  }\r\n\r\n  .ant-input-suffix {\r\n    cursor: pointer;\r\n    color: @primary-color;\r\n    \r\n    &:hover {\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式调整\r\n@media screen and (max-width: 576px) {\r\n  .material-search-container {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .material-list-container {\r\n    overflow-x: auto;\r\n    padding: 12px 8px;\r\n  }\r\n  \r\n  .form-actions {\r\n    flex-direction: column;\r\n    \r\n    .ant-btn {\r\n      width: 100%;\r\n    }\r\n  }\r\n  \r\n  .status-switch-container {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n    \r\n    .status-text {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "sourceRoot": "src/views/admin/purchaseSalesInventory/material/modules"}]}