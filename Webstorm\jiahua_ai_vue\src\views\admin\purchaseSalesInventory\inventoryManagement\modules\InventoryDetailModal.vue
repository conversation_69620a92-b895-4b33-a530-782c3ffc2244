<template>
  <a-modal
    :title="modalTitle"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    destroyOnClose
    class="inventory-detail-modal"
  >
    <a-spin :spinning="loading">
      <div class="detail-content">
        <!-- 基本信息 -->
        <div class="detail-section">
          <div class="section-title">
            <a-icon :type="record.imInventoryType === '1' ? 'database' : 'appstore'" />
            <span>{{ record.imInventoryType === '1' ? '物料信息' : '产品信息' }}</span>
          </div>
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">{{ record.imInventoryType === '1' ? '物料名称' : '产品名称' }}</div>
              <div class="info-value">{{ record.imInventoryType === '1' ? record.imItemName : record.imProductName }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">单位</div>
              <div class="info-value">{{ record.imItemUnit || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">{{ clickType === 'inventory' ? '总库存数量' : '可用库存数量' }}</div>
              <div class="info-value highlight">{{ clickType === 'inventory' ? record.imInventoryQuantity : record.imAvailableStock }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">{{ record.imInventoryType === '1' ? '平均单价' : '平均成本' }}</div>
              <div class="info-value price">¥{{ formatNumber(record.imInventoryType === '1' ? record.imItemPrice : record.imCost) }}</div>
            </div>
          </div>
        </div>

        <!-- 物料详情 - 采购信息 -->
        <div v-if="record.imInventoryType === '1'" class="detail-section">
          <div class="section-title">
            <a-icon type="shopping" />
            <span>采购详情</span>
          </div>
          <a-table
            :columns="purchaseColumns"
            :dataSource="purchaseDetails"
            :pagination="false"
            size="small"
            :loading="detailLoading"
            :locale="{ emptyText: '暂无采购记录数据' }"
            :scroll="{ y: 250 }"
          >
            <template slot="cost" slot-scope="text">
              <span class="price-text">¥{{ formatNumber(text) }}</span>
            </template>
            <template slot="quantity" slot-scope="text">
              <span class="quantity-text">{{ text }}</span>
            </template>
          </a-table>
        </div>

        <!-- 产品详情 - 产品成本 -->
        <div v-if="record.imInventoryType === '2'" class="detail-section">
          <div class="section-title">
            <a-icon type="calculator" />
            <span>产品成本（根据物料清单）</span>
          </div>
          <div v-if="productCostLoading" class="loading-container">
            <a-spin size="large" />
          </div>
          <div v-else-if="productCost.materialDetails && productCost.materialDetails.length > 0">
            <div class="cost-summary">
              <div class="cost-item">
                <span class="cost-label">物料清单：</span>
                <span class="cost-value">{{ productCost.materialBillName || '未命名' }}</span>
              </div>
              <div class="cost-item">
                <span class="cost-label">总成本：</span>
                <span class="cost-value price">¥{{ formatNumber(productCost.totalCost) }}</span>
              </div>
            </div>
            <a-table
              :columns="materialCostColumns"
              :dataSource="productCost.materialDetails"
              :pagination="false"
              size="small"
              :locale="{ emptyText: '暂无物料成本数据' }"
              :scroll="{ y: 150 }"
            >
              <template slot="cost" slot-scope="text">
                <span class="price-text">¥{{ formatNumber(text) }}</span>
              </template>
              <template slot="unitPrice" slot-scope="text">
                <span class="price-text">¥{{ formatNumber(text) }}</span>
              </template>
            </a-table>
          </div>
          <div v-else class="empty-state">
            <a-empty description="暂无物料清单成本数据" />
          </div>
        </div>

        <!-- 产品详情 - 生产信息 -->
        <div v-if="record.imInventoryType === '2'" class="detail-section">
          <div class="section-title">
            <a-icon type="tool" />
            <span>生产批次详情</span>
          </div>
          <a-table
            :columns="productionColumns"
            :dataSource="productionDetails"
            :pagination="false"
            size="small"
            :loading="detailLoading"
            :locale="{ emptyText: '暂无生产记录数据' }"
            :scroll="{ y: 250 }"
          >
            <template slot="cost" slot-scope="text">
              <span class="price-text">¥{{ formatNumber(text) }}</span>
            </template>
            <template slot="quantity" slot-scope="text">
              <span class="quantity-text">{{ text }}</span>
            </template>
            <template slot="priority" slot-scope="text, record">
              <a-checkbox
                :checked="record.prioritySale"
                @change="(e) => handlePriorityChange(record, e.target.checked)"
              >
                优先出售此批次
              </a-checkbox>
            </template>
          </a-table>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'InventoryDetailModal',
  data() {
    return {
      visible: false,
      confirmLoading: false,
      loading: false,
      detailLoading: false,
      productCostLoading: false,
      record: {},
      clickType: 'inventory', // 'inventory' 或 'available'
      purchaseDetails: [],
      productionDetails: [],
      productCost: {
        totalCost: 0,
        materialDetails: []
      },

      // 物料成本表格列
      materialCostColumns: [
        {
          title: '物料名称',
          dataIndex: 'materialName',
          key: 'materialName',
          width: 150
        },
        {
          title: '用量',
          dataIndex: 'materialCount',
          key: 'materialCount',
          width: 80
        },
        {
          title: '单位/规格',
          dataIndex: 'materialUnit',
          key: 'materialUnit',
          width: 120
        },
        {
          title: '单价',
          dataIndex: 'unitPrice',
          key: 'unitPrice',
          width: 100,
          scopedSlots: { customRender: 'unitPrice' }
        },
        {
          title: '成本',
          dataIndex: 'materialCost',
          key: 'materialCost',
          width: 100,
          scopedSlots: { customRender: 'cost' }
        }
      ],

      // 采购详情表格列
      purchaseColumns: [
        {
          title: '采购单号',
          dataIndex: 'purchaseOrderNo',
          key: 'purchaseOrderNo',
          width: 150
        },
        {
          title: '采购人',
          dataIndex: 'purchaser',
          key: 'purchaser',
          width: 100
        },
        {
          title: '采购成本',
          dataIndex: 'purchaseCost',
          key: 'purchaseCost',
          width: 120,
          scopedSlots: { customRender: 'cost' }
        },
        {
          title: '数量',
          dataIndex: 'quantity',
          key: 'quantity',
          width: 80,
          scopedSlots: { customRender: 'quantity' }
        },
        {
          title: '剩余数量',
          dataIndex: 'remainingQuantity',
          key: 'remainingQuantity',
          width: 100,
          scopedSlots: { customRender: 'quantity' }
        },
        {
          title: '采购日期',
          dataIndex: 'purchaseDate',
          key: 'purchaseDate',
          width: 120
        }
      ],
      
      // 生产详情表格列
      productionColumns: [
        // {
        //   title: '生产批次',
        //   dataIndex: 'productionBatch',
        //   key: 'productionBatch',
        //   width: 150
        // },
        {
          title: '生产批次（生产单号）',
          dataIndex: 'productionOrderNo',
          key: 'productionOrderNo',
          width: 180
        },
        {
          title: '数量',
          dataIndex: 'quantity',
          key: 'quantity',
          width: 80,
          scopedSlots: { customRender: 'quantity' }
        },
        {
          title: '批次成本',
          dataIndex: 'batchCost',
          key: 'batchCost',
          width: 120,
          scopedSlots: { customRender: 'cost' }
        },
        {
          title: '生产日期',
          dataIndex: 'productionDate',
          key: 'productionDate',
          width: 120
        },
        {
          title: '优先出售',
          dataIndex: 'prioritySale',
          key: 'prioritySale',
          width: 120,
          scopedSlots: { customRender: 'priority' }
        }
      ]
    }
  },
  computed: {
    modalTitle() {
      if (this.record.imInventoryType === '1') {
        return this.clickType === 'inventory' ? '物料库存详情' : '物料可用库存详情'
      } else {
        return this.clickType === 'inventory' ? '产品库存详情' : '产品可用库存详情'
      }
    }
  },
  methods: {
    show(record, clickType = 'inventory') {
      this.visible = true
      this.record = record
      this.clickType = clickType
      this.loadDetails()
    },
    
    loadDetails() {
      this.detailLoading = true

      if (this.record.imInventoryType === '1') {
        // 加载物料采购详情
        this.loadPurchaseDetails()
      } else {
        // 加载产品生产详情和产品成本
        this.productCostLoading = true
        Promise.all([
          this.loadProductionDetails(),
          this.loadProductCost()
        ]).finally(() => {
          this.productCostLoading = false
        })
      }
    },
    
    loadPurchaseDetails() {
      const url = '/admin/inventoryManagement/getPurchaseDetails'
      getAction(url, { inventoryId: this.record.id }).then(res => {
        if (res.success) {
          this.purchaseDetails = res.result || []
        } else {
          this.$message.error('获取采购详情失败')
        }
        this.detailLoading = false
      }).catch(() => {
        this.$message.error('获取采购详情失败')
        this.detailLoading = false
      })
    },
    
    loadProductionDetails() {
      const url = '/admin/inventoryManagement/getProductionDetails'
      return getAction(url, { inventoryId: this.record.id }).then(res => {
        if (res.success) {
          this.productionDetails = res.result || []
        } else {
          this.$message.error('获取生产详情失败')
        }
        this.detailLoading = false
      }).catch(() => {
        this.$message.error('获取生产详情失败')
        this.detailLoading = false
      })
    },

    loadProductCost() {
      const url = '/admin/inventoryManagement/getProductCost'
      return getAction(url, { productName: this.record.imProductName }).then(res => {
        if (res.success) {
          this.productCost = res.result || { totalCost: 0, materialDetails: [] }
        } else {
          this.$message.error('获取产品成本失败')
        }
      }).catch(() => {
        this.$message.error('获取产品成本失败')
      })
    },
    
    handlePriorityChange(record, checked) {
      const url = '/admin/inventoryManagement/updatePrioritySale'
      postAction(url, { 
        batchId: record.id, 
        prioritySale: checked 
      }).then(res => {
        if (res.success) {
          record.prioritySale = checked
          this.$message.success('设置成功')
        } else {
          this.$message.error('设置失败')
        }
      }).catch(() => {
        this.$message.error('设置失败')
      })
    },
    
    handleOk() {
      this.visible = false
    },
    
    handleCancel() {
      this.visible = false
    },
    
    formatNumber(num) {
      if (!num || isNaN(num)) return '0.00'
      return parseFloat(num).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
    }
  }
}
</script>

<style lang="less" scoped>
.inventory-detail-modal {
  .detail-content {
    .detail-section {
      margin-bottom: 24px;
      
      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        
        .anticon {
          margin-right: 8px;
          color: #1890ff;
        }
      }
      
      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 16px;
        
        .info-item {
          .info-label {
            color: #8c8c8c;
            font-size: 14px;
            margin-bottom: 8px;
          }
          
          .info-value {
            color: #262626;
            font-size: 16px;
            
            &.highlight {
              color: #1890ff;
              font-weight: 600;
              font-size: 18px;
            }
            
            &.price {
              color: #52c41a;
              font-weight: 600;
            }
          }
        }
      }

      .cost-summary {
        background: #f5f5f5;
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 16px;

        .cost-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .cost-label {
            color: #8c8c8c;
            font-size: 14px;
          }

          .cost-value {
            color: #262626;
            font-weight: 500;

            &.price {
              color: #52c41a;
              font-weight: 600;
              font-size: 16px;
            }
          }
        }
      }

      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
      }

      .empty-state {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
      }
    }
  }

  .price-text {
    color: #52c41a;
    font-weight: 600;
  }

  .quantity-text {
    color: #262626;
    font-weight: 500;
  }

  // 表格样式优化
  .ant-table-tbody {
    min-height: 250px;
  }

  .ant-table-placeholder {
    min-height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;

    .ant-empty {
      margin: 0;
    }

    .ant-empty-description {
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}

@media (max-width: 768px) {
  .inventory-detail-modal {
    .detail-content {
      .detail-section {
        .info-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>
