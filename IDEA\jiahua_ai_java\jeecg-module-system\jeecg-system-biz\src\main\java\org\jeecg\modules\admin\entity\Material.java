package org.jeecg.modules.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 物料表
 * @Author: jeecg-boot
 * @Date:   2025-02-21
 * @Version: V1.0
 */
@Data
@TableName("material")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="material对象", description="物料表")
public class Material implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**物料名*/
	@Excel(name = "物料名", width = 15)
    @ApiModelProperty(value = "物料名")
    private java.lang.String materialName;
	/**物料规格*/
	@Excel(name = "物料规格", width = 15)
    @ApiModelProperty(value = "物料规格")
    private java.lang.String materialSpecifications;
	/**物料单个用量*/
	@Excel(name = "物料单个用量", width = 15)
    @ApiModelProperty(value = "物料单个用量")
    private java.lang.String materialCount;
    /**物料总用量*/
    @Excel(name = "物料总用量", width = 15)
    @ApiModelProperty(value = "物料总用量")
    private java.lang.String materialTotalCount;
    /**物料单位*/
    @Excel(name = "物料单位", width = 15)
    @ApiModelProperty(value = "物料单位")
    private java.lang.String materialUnit;

    /**物料清单id*/
    @Excel(name = "物料清单id", width = 15)
    @ApiModelProperty(value = "物料清单id")
    private java.lang.String materialBillId;/**物料清单id*/
    @Excel(name = "生产订单id", width = 15)
    @ApiModelProperty(value = "生产订单id")
    private java.lang.String productionOrderId;
    /**公司id*/
    @Excel(name = "公司id", width = 15)
    @ApiModelProperty(value = "公司id")
    private String companyId;
    /**成本 (数量*采购价)*/
    @Excel(name = "成本 (数量*采购价)", width = 15)
    @ApiModelProperty(value = "成本 (数量*采购价)")
    private BigDecimal materialCost;

    /**
     * 采购价
     */
    @Excel(name = "采购价", width = 15)
    @ApiModelProperty(value = "采购价")
    private BigDecimal imItemPrice;
}
